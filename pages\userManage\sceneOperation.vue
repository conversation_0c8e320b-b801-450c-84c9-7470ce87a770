<template>
	<!-- 场景管理 -->
	<view class="sceneOpera">
		<u-navbar :title="operTitle" :autoBack='true' bgColor='#FFFFFF'></u-navbar>
		<view class="form_view rightForm topSpace">
			<u--form labelPosition="left" :model="modelPatient" :rules="patientRules" ref="patientForm" labelWidth="120"
				:labelStyle="labelStyle">
				<u-form-item label="场景名称" prop="userInfo.sceneName" borderBottom :required="true">
					<u--input v-model="modelPatient.userInfo.sceneName" maxlength="10" inputAlign="right" border="none"
						placeholder="请输入场景名称"></u--input>
				</u-form-item>
			</u--form>
			<view class="btnXFView">
				<u-button type="primary" text="保存" :disabled="disabled" :customStyle="primaryBtnCss"
					@click="submit"></u-button>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		sceneAdd,
		sceneUpdate
	} from "@/network/api.js"
	export default {
		data() {
			return {
				operTitle: '',
				primaryBtnCss: {
					width: '574rpx',
					height: '88rpx',
					background: '#0165FC',
					boxShadow: '0rpx 4rpx 24rpx 0rpx rgba(54,150,255,0.4)',
					borderRadius: '200rpx',
					fontSize: '28rpx',
					fontFamily: 'Microsoft YaHei UI-Regular, Microsoft YaHei UI',
					color: '#FFFFFF',
					lineHeight: '28rpx'
				},
				labelStyle: {
					// fontSize: '28rpx',
					// fontFamily: 'Microsoft YaHei UI-Regular, Microsoft YaHei UI',
					// lineHeight: '28rpx'
				},
				disabled: false,
				modelPatient: {
					userInfo: {
						sceneName: '',
					},
				},
				patientRules: {
					'userInfo.sceneName': {
						type: 'string',
						required: true,
						message: '请输入',
						trigger: ['blur', 'change']
					}
				},
				addEditSign: ''
			};
		},
		onLoad(opt) {
			if (JSON.stringify(opt) === "{}") {
				this.addEditSign = 'add';
				this.operTitle = '场景新增';
			} else {
				let paramsData = JSON.parse(opt.params)
				this.addEditSign = 'edit';
				this.operTitle = '场景编辑';
				this.modelPatient.userInfo = paramsData;
			}
		},
		methods: {
			submit() {
				this.$refs.patientForm.validate().then(res => {
					uni.showLoading({
						title: '请求中..'
					})
					if (this.addEditSign === 'add') {
						let form = {
							userId: uni.getStorageSync('userId'),
							sceneName: this.modelPatient.userInfo.sceneName
						}
						sceneAdd(form).then(res => {
								uni.navigateBack({
									delta: 1 // 默认值是1，表示返回的页面层数
								});
								uni.hideLoading();
							})
							.catch((err) => {
								uni.hideLoading();
							})
					} else if (this.addEditSign === 'edit') {
						let form = {
							id: this.modelPatient.userInfo.id,
							sceneName: this.modelPatient.userInfo.sceneName
						}
						sceneUpdate(form).then(res => {
								uni.navigateBack({
									delta: 1 // 默认值是1，表示返回的页面层数
								});
								uni.hideLoading();
							})
							.catch((err) => {
								uni.hideLoading();
							})
					}
				}).catch(errors => {
					// uni.$u.toast('校验失败')
				})
			},
		},
		onReady() {
			//如果需要兼容微信小程序，并且校验规则中含有方法等，只能通过setRules方法设置规则。
			this.$refs.patientForm.setRules(this.patientRules)
		},
	};
</script>

<style lang="scss" scoped>
	.sceneOpera {
		padding: 0 0 100rpx;

		.topSpace {
			margin-top: 170rpx;
		}
	}
</style>