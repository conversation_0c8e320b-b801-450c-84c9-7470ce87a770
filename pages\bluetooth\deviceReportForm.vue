<template>
	<view class="report-page">
		<view class="container">
			<!-- 设备信息卡片 -->
			<view class="form-card">
				<view class="device-info">
					<view class="device-name">{{ (currentDevice && currentDevice.name) || '未知设备' }}</view>
					<view class="device-status" :class="{ connected: isConnected }">
						{{ isConnected ? '已连接' : '未连接' }}
					</view>
				</view>
			</view>

			<!-- 页面标题 -->
			<view class="section-title">设备数据上报</view>

			<!-- 数据上报卡片 -->
			<view class="form-card">
				<view class="report-card">
					<view class="card-header">
						<view class="card-icon data-icon">📊</view>
						<view class="card-title">数据上报</view>
					</view>
					<view class="card-description">
						主动上报设备当前的监测数据，包括气体浓度、设备状态、电池电量等信息
					</view>
					<view class="card-actions">
						<button class="report-btn data-btn"
							:class="{ loading: reportingStates.data, disabled: !isConnected }" @click="reportData"
							:disabled="!isConnected || reportingStates.data">
							<view class="btn-icon" v-if="!reportingStates.data">📤</view>
							<view class="loading-icon" v-if="reportingStates.data"></view>
							<text class="btn-text">{{ reportingStates.data ? '上报中...' : '数据上报' }}</text>
						</button>
					</view>
				</view>
			</view>

			<!-- 事件上报卡片 -->
			<view class="form-card">
				<view class="report-card">
					<view class="card-header">
						<view class="card-icon event-icon">⚡</view>
						<view class="card-title">事件上报</view>
					</view>
					<view class="card-description">
						主动上报设备的告警和故障事件，包括气体泄漏报警、设备异常等重要事件
					</view>
					<view class="card-actions">
						<button class="report-btn event-btn"
							:class="{ loading: reportingStates.event, disabled: !isConnected }" @click="reportEvent"
							:disabled="!isConnected || reportingStates.event">
							<view class="btn-icon" v-if="!reportingStates.event">🚨</view>
							<view class="loading-icon" v-if="reportingStates.event"></view>
							<text class="btn-text">{{ reportingStates.event ? '上报中...' : '事件上报' }}</text>
						</button>
					</view>
				</view>
			</view>

			<!-- 操作记录 -->
			<view class="section-title">操作记录</view>
			<view class="form-card">
				<view class="record-list" v-if="reportRecords.length > 0">
					<view class="record-item" v-for="(record, index) in reportRecords" :key="index">
						<view class="record-info">
							<view class="record-type" :class="record.type">
								{{ record.type === 'data' ? '数据上报' : '事件上报' }}
							</view>
							<view class="record-time">{{ record.time }}</view>
						</view>
						<view class="record-status" :class="record.status">
							{{ record.status === 'success' ? '成功' : '失败' }}
						</view>
					</view>
				</view>
				<view class="empty-records" v-else>
					<text class="empty-text">暂无操作记录</text>
				</view>
			</view>
		</view>

		<!-- 加载提示浮层 -->
		<view class="modal-overlay" v-if="showLoading">
			<view class="modal-content loading-modal">
				<view class="loading-icon"></view>
				<text class="loading-text">{{ loadingText }}</text>
			</view>
		</view>

		<!-- 结果提示弹窗 -->
		<view class="modal-overlay" v-if="showResult">
			<view class="modal-content result-modal">
				<view class="result-icon" :class="resultData.success ? 'success' : 'error'">
					{{ resultData.success ? '✓' : '✕' }}
				</view>
				<view class="result-title">{{ resultData.title }}</view>
				<view class="result-message">{{ resultData.content }}</view>
				<button class="btn btn-primary confirm-btn" @click="handleResultConfirm">知道了</button>
			</view>
		</view>

		<!-- 自定义toast -->
		<view class="toast-container" v-if="showToast">
			<view class="toast-message" :class="toastType">{{ toastMessage }}</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			currentDevice: null,
			isConnected: false,
			showLoading: false,
			showResult: false,
			showToast: false,
			toastMessage: '',
			toastType: 'default',
			loadingText: '正在上报数据...',

			// 上报状态跟踪
			reportingStates: {
				data: false,    // 数据上报状态
				event: false    // 事件上报状态
			},

			// 结果数据
			resultData: {
				title: '',
				content: '',
				success: false
			},

			// 操作记录
			reportRecords: []
		}
	},
	onLoad(options) {
		// 获取设备信息和连接状态
		if (options.device) {
			try {
				this.currentDevice = JSON.parse(decodeURIComponent(options.device));
			} catch (e) {
				console.error('解析设备信息失败:', e);
			}
		}

		// 检查连接状态
		this.checkConnectionStatus();

		// 加载历史记录
		this.loadReportRecords();

		// 设置页面标题
		if (this.currentDevice && this.currentDevice.name) {
			uni.setNavigationBarTitle({
				title: '数据上报 - ' + this.currentDevice.name
			});
		}
	},
	methods: {
		// 检查连接状态
		checkConnectionStatus() {
			const deviceId = this.currentDevice?.deviceId;
			const serviceId = uni.getStorageSync('writeServiceId');
			const characteristicId = uni.getStorageSync('writeCharacteristicId');

			this.isConnected = !!(deviceId && serviceId && characteristicId);
		},

		// 自定义toast方法
		showCustomToast(options) {
			this.toastMessage = options.message || '';
			this.toastType = options.type || 'default';
			this.showToast = true;

			setTimeout(() => {
				this.showToast = false;
			}, 2000);

			uni.showToast({
				title: options.message || '',
				icon: options.type === 'error' ? 'error' : (options.type === 'warning' ? 'none' : 'success'),
				duration: 2000
			});
		},

		// 数据上报功能
		async reportData() {
			if (!this.isConnected) {
				this.showCustomToast({
					message: '设备未连接，请先连接设备',
					type: 'error'
				});
				return;
			}

			this.reportingStates.data = true;
			this.loadingText = '正在上报数据...';
			this.showLoading = true;

			try {
				console.log('开始数据上报 (0x23)');

				// 构建数据上报指令：A5 FE 23 00 00 [CRC]
				const commandCode = 0x23;
				const command = [0xA5, 0xFE, commandCode, 0x00, 0x00];

				// 计算CRC校验码
				const crc = this.calculateCRC(command);
				command.push(crc);

				console.log('发送数据上报指令:', command.map(b => '0x' + b.toString(16).padStart(2, '0')).join(' '));

				// 注册全局指令响应处理
				const commandId = 'data_report_' + Date.now();
				this.registerGlobalCommand(commandId, commandCode, 'report', (result) => {
					this.handleReportResult('data', result);
				});

				// 发送指令
				const buffer = new ArrayBuffer(command.length);
				const uint8Array = new Uint8Array(buffer);
				uint8Array.set(command);

				await uni.writeBLECharacteristicValue({
					deviceId: this.currentDevice.deviceId,
					serviceId: uni.getStorageSync('writeServiceId'),
					characteristicId: uni.getStorageSync('writeCharacteristicId'),
					value: buffer
				});

				console.log('数据上报指令发送成功');

			} catch (error) {
				console.error('数据上报失败:', error);
				this.handleReportResult('data', {
					success: false,
					error: 'send_failed',
					message: '指令发送失败: ' + error.message
				});
			}
		},

		// 事件上报功能
		async reportEvent() {
			if (!this.isConnected) {
				this.showCustomToast({
					message: '设备未连接，请先连接设备',
					type: 'error'
				});
				return;
			}

			this.reportingStates.event = true;
			this.loadingText = '正在上报事件...';
			this.showLoading = true;

			try {
				console.log('开始事件上报 (0x24)');

				// 构建事件上报指令：A5 FE 24 00 00 [CRC]
				const commandCode = 0x24;
				const command = [0xA5, 0xFE, commandCode, 0x00, 0x00];

				// 计算CRC校验码
				const crc = this.calculateCRC(command);
				command.push(crc);

				console.log('发送事件上报指令:', command.map(b => '0x' + b.toString(16).padStart(2, '0')).join(' '));

				// 注册全局指令响应处理
				const commandId = 'event_report_' + Date.now();
				this.registerGlobalCommand(commandId, commandCode, 'report', (result) => {
					this.handleReportResult('event', result);
				});

				// 发送指令
				const buffer = new ArrayBuffer(command.length);
				const uint8Array = new Uint8Array(buffer);
				uint8Array.set(command);

				await uni.writeBLECharacteristicValue({
					deviceId: this.currentDevice.deviceId,
					serviceId: uni.getStorageSync('writeServiceId'),
					characteristicId: uni.getStorageSync('writeCharacteristicId'),
					value: buffer
				});

				console.log('事件上报指令发送成功');

			} catch (error) {
				console.error('事件上报失败:', error);
				this.handleReportResult('event', {
					success: false,
					error: 'send_failed',
					message: '指令发送失败: ' + error.message
				});
			}
		},

		// 处理上报结果
		handleReportResult(type, result) {
			this.reportingStates[type] = false;
			this.showLoading = false;

			// 记录操作历史
			this.addReportRecord(type, result.success);

			// 显示结果弹窗
			if (result.success) {
				this.resultData = {
					title: type === 'data' ? '数据上报成功' : '事件上报成功',
					content: '设备已成功接收上报指令并处理完成',
					success: true
				};
			} else {
				let errorMessage = '上报失败';

				switch (result.error) {
					case 'timeout':
						errorMessage = '上报超时，请检查设备连接状态';
						break;
					case 'send_failed':
						errorMessage = result.message || '指令发送失败';
						break;
					case 'device_error':
						errorMessage = '设备处理错误，请重试';
						break;
					case 'no_global_handler':
						errorMessage = '系统错误，请重启应用后重试';
						break;
					default:
						errorMessage = result.message || '未知错误';
				}

				this.resultData = {
					title: type === 'data' ? '数据上报失败' : '事件上报失败',
					content: errorMessage,
					success: false
				};
			}

			this.showResult = true;
		},

		// 添加操作记录
		addReportRecord(type, success) {
			const record = {
				type: type,
				status: success ? 'success' : 'failed',
				time: this.formatTime(new Date())
			};

			this.reportRecords.unshift(record);

			// 只保留最近20条记录
			if (this.reportRecords.length > 20) {
				this.reportRecords = this.reportRecords.slice(0, 20);
			}

			// 保存到本地存储
			this.saveReportRecords();
		},

		// 格式化时间
		formatTime(date) {
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			const seconds = String(date.getSeconds()).padStart(2, '0');

			return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
		},

		// 保存操作记录到本地存储
		saveReportRecords() {
			try {
				uni.setStorageSync('reportRecords', JSON.stringify(this.reportRecords));
			} catch (error) {
				console.error('保存操作记录失败:', error);
			}
		},

		// 加载操作记录
		loadReportRecords() {
			try {
				const records = uni.getStorageSync('reportRecords');
				if (records) {
					this.reportRecords = JSON.parse(records);
				}
			} catch (error) {
				console.error('加载操作记录失败:', error);
				this.reportRecords = [];
			}
		},

		// 结果确认
		handleResultConfirm() {
			this.showResult = false;
		},

		// CRC校验码计算
		calculateCRC(data) {
			// 按照deviceReadForm.vue的计算方法：命令 + 数据长度高字节 + 数据长度低字节
			// 对于上报指令，数据长度为0，所以CRC = (commandCode + 0x00 + 0x00) & 0xFF
			const commandCode = data[2]; // 命令码在第3个位置（索引2）
			const dataLengthHigh = data[3]; // 数据长度高字节
			const dataLengthLow = data[4]; // 数据长度低字节

			const crc = (commandCode + dataLengthHigh + dataLengthLow) & 0xFF;
			return crc;
		},

		// 注册全局指令响应处理
		registerGlobalCommand(commandId, commandCode, commandType, callback) {
			// 尝试多种方式获取蓝牙连接页面实例
			const pages = getCurrentPages();
			console.log('当前页面栈:', pages.map(p => p.route));

			// 尝试不同的路由路径
			let bluetoothPage = pages.find(page => page.route === 'pages/bluetooth/bluetoothConnect');
			if (!bluetoothPage) {
				bluetoothPage = pages.find(page => page.route.includes('bluetoothConnect'));
			}
			if (!bluetoothPage) {
				bluetoothPage = pages.find(page => page.$options && page.$options.name === 'bluetoothConnect');
			}

			console.log('找到的蓝牙页面:', bluetoothPage);

			if (bluetoothPage) {
				console.log('蓝牙页面可用方法:', Object.keys(bluetoothPage).filter(key => typeof bluetoothPage[key] === 'function'));

				if (bluetoothPage.registerPendingCommand) {
					bluetoothPage.registerPendingCommand(commandId, commandCode, commandType, callback);
					return;
				}
			}

			console.error('无法找到蓝牙连接页面或全局响应处理方法');
			// 降级处理：使用简单的超时机制
			this.fallbackReportCommand(commandId, commandCode, callback);
		},

		// 清除全局指令
		clearGlobalCommand(commandId) {
			// 清除全局指令
			const pages = getCurrentPages();
			let bluetoothPage = pages.find(page => page.route === 'pages/bluetooth/bluetoothConnect');
			if (!bluetoothPage) {
				bluetoothPage = pages.find(page => page.route.includes('bluetoothConnect'));
			}

			if (bluetoothPage && bluetoothPage.pendingCommands) {
				if (bluetoothPage.pendingCommands.has(commandId)) {
					const commandInfo = bluetoothPage.pendingCommands.get(commandId);
					clearTimeout(commandInfo.timeout);
					bluetoothPage.pendingCommands.delete(commandId);
				}
			}
		},

		// 降级处理：当无法使用全局响应处理机制时的简单处理
		fallbackReportCommand(commandId, commandCode, callback) {
			console.log('使用降级处理机制');

			// 设置简单的超时处理
			const timeout = setTimeout(() => {
				console.log(`上报指令 0x${commandCode.toString(16)} 超时`);

				// 显示超时提示
				this.showCustomToast({
					message: '上报超时，请检查设备连接',
					type: 'error'
				});

				if (callback) {
					callback({
						success: false,
						error: 'timeout',
						message: '上报超时'
					});
				}
			}, 5000);

			// 监听响应（降级方案）
			const responseHandler = (res) => {
				if (res.value) {
					const uint8Array = new Uint8Array(res.value);

					// 检查是否是对应指令的响应
					if (uint8Array.length >= 3 && uint8Array[0] === 0xA5 && uint8Array[1] === 0xFE && uint8Array[2] === commandCode) {
						clearTimeout(timeout);
						uni.offBLECharacteristicValueChange(responseHandler);

						console.log(`收到指令 0x${commandCode.toString(16)} 的响应`);

						if (callback) {
							callback({
								success: true,
								data: uint8Array
							});
						}
					}
				}
			};

			// 添加响应监听
			uni.onBLECharacteristicValueChange(responseHandler);
		}
	}
}
</script>

<style lang="scss" scoped>
// 基础样式继承自其他页面的设计风格
.report-page {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.container {
	padding: 30rpx;
}

.form-card {
	background-color: #ffffff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.section-title {
	font-size: 28rpx;
	color: #666666;
	margin: 30rpx 0 20rpx;
	padding-left: 20rpx;
	position: relative;

	&::before {
		content: '';
		position: absolute;
		left: 0;
		top: 50%;
		transform: translateY(-50%);
		width: 6rpx;
		height: 28rpx;
		background-color: #47afff;
		border-radius: 3rpx;
	}
}

// 设备信息样式
.device-info {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.device-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333333;
}

.device-status {
	padding: 8rpx 20rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	background-color: #f5f5f5;
	color: #999999;

	&.connected {
		background-color: #e6f9eb;
		color: #4cd964;
	}
}

// 上报卡片样式
.report-card {
	.card-header {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.card-icon {
		font-size: 40rpx;
		margin-right: 20rpx;
		width: 60rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 50%;

		&.data-icon {
			background-color: #e6f9eb;
		}

		&.event-icon {
			background-color: #e6f3ff;
		}
	}

	.card-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
	}

	.card-description {
		font-size: 26rpx;
		color: #666666;
		line-height: 1.6;
		margin-bottom: 30rpx;
	}

	.card-actions {
		display: flex;
		justify-content: center;
	}
}

// 上报按钮样式
.report-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20rpx 40rpx;
	border-radius: 50rpx;
	border: none;
	font-size: 28rpx;
	font-weight: bold;
	color: #ffffff;
	min-width: 200rpx;
	transition: all 0.3s ease;

	&.data-btn {
		background: linear-gradient(135deg, #4cd964, #5ac776);

		&:active {
			background: linear-gradient(135deg, #3cb653, #4cb665);
		}
	}

	&.event-btn {
		background: linear-gradient(135deg, #47afff, #5bb8ff);

		&:active {
			background: linear-gradient(135deg, #369eee, #4aa7ee);
		}
	}

	&.disabled {
		background: #cccccc !important;
		color: #999999 !important;
	}

	&.loading {
		opacity: 0.8;
	}

	.btn-icon {
		margin-right: 10rpx;
		font-size: 24rpx;
	}

	.btn-text {
		font-size: 28rpx;
	}

	.loading-icon {
		width: 24rpx;
		height: 24rpx;
		border: 3rpx solid rgba(255, 255, 255, 0.3);
		border-top: 3rpx solid #ffffff;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin-right: 10rpx;
	}
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

// 操作记录样式
.record-list {
	.record-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 0;
		border-bottom: 1rpx solid #f0f0f0;

		&:last-child {
			border-bottom: none;
		}
	}

	.record-info {
		flex: 1;
	}

	.record-type {
		font-size: 28rpx;
		font-weight: bold;
		margin-bottom: 8rpx;

		&.data {
			color: #4cd964;
		}

		&.event {
			color: #47afff;
		}
	}

	.record-time {
		font-size: 24rpx;
		color: #999999;
	}

	.record-status {
		padding: 6rpx 16rpx;
		border-radius: 16rpx;
		font-size: 22rpx;
		font-weight: bold;

		&.success {
			background-color: #e6f9eb;
			color: #4cd964;
		}

		&.failed {
			background-color: #ffe6e6;
			color: #ff4757;
		}
	}
}

.empty-records {
	text-align: center;
	padding: 60rpx 0;

	.empty-text {
		font-size: 26rpx;
		color: #999999;
	}
}

// 弹窗样式
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
}

.modal-content {
	background-color: #ffffff;
	border-radius: 20rpx;
	padding: 40rpx;
	margin: 0 60rpx;
	max-width: 600rpx;
	width: 100%;
	text-align: center;
}

.loading-modal {
	.loading-icon {
		width: 60rpx;
		height: 60rpx;
		border: 6rpx solid #f3f3f3;
		border-top: 6rpx solid #47afff;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin: 0 auto 20rpx;
	}

	.loading-text {
		font-size: 28rpx;
		color: #666666;
	}
}

.result-modal {
	.result-icon {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 40rpx;
		font-weight: bold;
		margin: 0 auto 20rpx;

		&.success {
			background-color: #e6f9eb;
			color: #4cd964;
		}

		&.error {
			background-color: #ffe6e6;
			color: #ff4757;
		}
	}

	.result-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 20rpx;
	}

	.result-message {
		font-size: 26rpx;
		color: #666666;
		line-height: 1.6;
		margin-bottom: 40rpx;
	}

	.confirm-btn {
		background: linear-gradient(135deg, #47afff, #5bb8ff);
		color: #ffffff;
		border: none;
		border-radius: 50rpx;
		padding: 20rpx 60rpx;
		font-size: 28rpx;
		font-weight: bold;
		width: 100%;

		&:active {
			background: linear-gradient(135deg, #369eee, #4aa7ee);
		}
	}
}

// Toast样式
.toast-container {
	position: fixed;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	z-index: 10000;
}

.toast-message {
	background-color: rgba(0, 0, 0, 0.8);
	color: #ffffff;
	padding: 20rpx 40rpx;
	border-radius: 10rpx;
	font-size: 26rpx;
	max-width: 500rpx;
	text-align: center;
	word-wrap: break-word;

	&.error {
		background-color: rgba(255, 71, 87, 0.9);
	}

	&.warning {
		background-color: rgba(255, 193, 7, 0.9);
	}

	&.success {
		background-color: rgba(76, 217, 100, 0.9);
	}
}
</style>
