<template>
	<view v-if="showPrivacy" :class="privacyClass">
		<view :class="contentClass">
			<view class="title">用户隐私保护指引</view>
			<view class="des">
				感谢您选择使用燃气安全管家小程序，我们非常重视您的个人信息安全和隐私保护。使用我们的产品前，请您仔细阅读"
				<text class="link" @tap="openPrivacyContract">{{privacyContractName}} </text>"，
				如您同意此隐私保护指引,请点击同意按钮,开始使用此小程序,我们将尽全力保护您的个人信息及合法权益，感谢您的信任！<br />
			</view>
			<view class="btns">
				<button class="item reject" @click="exitMiniProgram">拒绝</button>
				<button id="agree-btn" class="item agree" @click="handleAgreePrivacyAuthorization">同意</button>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'PrivacyPopup',
		data() {
			return {
				isRead: false,
				showPrivacy: false,
				privacyContractName: '用户协议与隐私政策',
				resolvePrivacyAuthorization: null,
				
			};
		},
		props: {
			position: {
				type: String,
				default: 'center'
			}
		},
		computed: {
			privacyClass() {
				return this.position === 'bottom' ? 'privacy privacy-bottom' : 'privacy';
			},
			contentClass() {
				return this.position === 'bottom' ? 'content content-bottom' : 'content';
			}
		},
		mounted() {
			// 完全禁用隐私监听器，避免触发微信隐私框架
			console.log('隐私弹窗组件已禁用，避免触发隐私检查');
		},

		methods: {
			openPrivacy() {
				this.showPrivacy = true;
			},
			
			openPrivacyContract() {
				// 跳转到隐私政策页面
				uni.navigateTo({
					url: '/pages/loginRegister/privacyPolicy',
					success: () => {
						this.isRead = true;
					},
					fail: (err) => {
						console.error('跳转到隐私政策页面失败:', err);
						// 如果跳转失败，回退到微信的原生隐私协议
						this.openWxPrivacyContract();
					}
				});
			},
			
			openWxPrivacyContract() {
				// 禁用微信原生隐私协议，避免触发隐私框架
				console.log('微信隐私协议已禁用');
				this.isRead = true;
			},
			
			exitMiniProgram() {
				// uni.navigateBack();
				wx.exitMiniProgram();

			},
			handleAgreePrivacyAuthorization() {
				console.log('用户同意隐私授权');
				this.showPrivacy = false;
				this.$emit('allowPrivacy');

				// 调用微信的隐私授权回调
				if (typeof this.resolvePrivacyAuthorization === 'function') {
					try {
						this.resolvePrivacyAuthorization({
							buttonId: 'agree-btn',
							event: 'agree',
						});
						console.log('隐私授权回调执行成功');
					} catch (error) {
						console.error('隐私授权回调执行失败:', error);
					}
				}
			},
			closePrivacy() {
				this.showPrivacy = false;
			}
		},
	};
</script>

<style scoped>
	.privacy {
		position: fixed;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		background: rgba(0, 0, 0, .5);
		z-index: 9999999;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.privacy-bottom {
		align-items: flex-end;
	}

	.content {
		width: 632rpx;
		padding: 48rpx;
		box-sizing: border-box;
		background: #fff;
		border-radius: 16rpx;
	}

	.content-bottom {
		position: absolute;
		bottom: 0;
		width: 96%;
		padding: 36rpx;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		border-radius: 16rpx 16rpx 0 0;
	}

	.content .title {
		text-align: center;
		color: #333;
		font-weight: bold;
		font-size: 32rpx;
	}

	.content .des {
		font-size: 26rpx;
		color: #666;
		margin-top: 40rpx;
		text-align: justify;
		line-height: 1.6;
	}

	.content .des .link {
		color: #1989ff;
		text-decoration: underline;
	}

	.btns {
		margin-top: 48rpx;
		margin-bottom: 12rpx;
		display: flex;
	}

	.btns .item {
		width: 200rpx;
		height: 72rpx;
		overflow: visible;
		display: flex;
		align-items: center;

		justify-content: center;
		/* border-radius: 16rpx; */
		box-sizing: border-box;
		border: none !important;
	}

	.btns .reject {
		background: #f4f4f5;
		color: #1989ff;
		font-size: 14px;
		background: #edf5fe;
		font-weight: 300;
		margin-right: 16rpx;
	}

	.btns .agree {
		width: 200rpx;
		background: #1989ff;
		color: #fff;
		font-size: 16px;

	}

	.privacy-bottom .btns .agree {
		width: 440rpx;

	}
</style>
