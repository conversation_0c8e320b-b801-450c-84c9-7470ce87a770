function unitConverter(num) {
	if (num) {
		num = num.toString().split("."); // 分隔小数点
		var arr = num[0].split("").reverse(); // 转换成字符数组并且倒序排列
		var res = [];
		for (var i = 0, len = arr.length; i < len; i++) {
			if (i % 3 === 0 && i !== 0) {
				res.push(","); // 添加分隔符
			}
			res.push(arr[i]);
		}
		res.reverse(); // 再次倒序成为正确的顺序

		if (num[1]) {
			if (num[1].length < 2) {
				// 如果有小数的话添加小数部分
				res = res.join("").concat("." + num[1] + '0');
			} else if (num[1].length > 2) {
				let data = num[1].substring(0, 2)
				// 如果有小数的话添加小数部分
				res = res.join("").concat("." + data);
			} else {
				res = res.join("").concat("." + num[1]);
			}

		} else {
			res = res.join("").concat('');
		}
	} else {
		res = '0.00'
	}
	return res;
}
export default {
	unitConverter
}

