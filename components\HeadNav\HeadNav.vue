<template>
	<view class="nav-box" @click="getHeadHeight" :style="{'height':height+'px','background':bgColor}">
		<!-- 状态栏占位 -->
		<view class="status_bar" :style="{'height':statusBarHeight+'px'}"></view>
		<!-- 导航内容 -->
		<view class="nav-main" :style="{'height': navBarHeight+'px'}">
			<!-- 回退图标 -->
			<image src="../../static/login/logo.png" mode="" class="back" >
			</image>
			<!-- 标题 -->
			<text class="nav-main-title" :style="!backIcon?'display: block;margin-left:20rpx;':''">{{title}}</text>
			<!-- 弹性盒占位 -->
			<view v-if="backIcon"></view>
		</view>
	</view>
</template>

<script>
	export default {
		//接收父组件传值(仅示例，其他业务场景可自行扩展)
		props: {
			// 导航栏背景色相关
			bgColor: {
				type: String,
				default: "#ffffff"
			},
			// 控制回退图标
			backIcon: {
				type: Boolean,
				default: true
			},
			// 标题相关
			title: {
				type: String,
				default: "我的"
			},
			// 特定路径
			path: {
				type: String,
			}
		},
		data() {
			return {
				// 自定义导航栏高度总和
				height: 0,
				// 微信小程序胶囊布局位置信息
				menuButtonRect: {},
				// 状态栏高度
				statusBarHeight: 0,
				// 导航栏高度（不包含状态栏）
				navBarHeight: 0
			}
		},
		created() {
			this.getHeight();
			this.getHeadHeight()
		},
		methods: {
			// 子传父 总高度
			getHeadHeight() {
				this.$emit('getHeadHeight', this.height)
			},
			//计算导航栏总高度
			getHeight() {
				// 判断获取微信小程序胶囊API是否可用
				if (uni.canIUse('getMenuButtonBoundingClientRect')) {
					// 获取状态栏高度(电量时间通知等信息-单位px)
					let sysInfo = uni.getSystemInfoSync();
					this.statusBarHeight = sysInfo.statusBarHeight;
					// 获取微信小程序胶囊布局位置信息
					let rect = uni.getMenuButtonBoundingClientRect();
					this.menuButtonRect = JSON.parse(JSON.stringify(rect));
					// (胶囊上部高度-状态栏高度)*2 + 胶囊高度 = 导航栏高度（不包含状态栏）
					//以此保证胶囊位于中间位置，多机型适配
					let navBarHeight = (rect.top - sysInfo.statusBarHeight) * 2 + rect.height;
					this.navBarHeight = navBarHeight;
					// 状态栏高度 + 导航栏高度 = 自定义导航栏高度总和
					this.height = sysInfo.statusBarHeight + navBarHeight;
				} else {
					uni.showToast({
						title: '您的微信版本过低，界面可能会显示不正常',
						icon: 'none',
						duration: 4000
					});
				}
			},
			//返回上一级
			back(path) {
				uni.navigateBack()
			},
		}
	}
</script>

<style lang="scss" scoped>
	// .nav-box {
	// position:fixed;
	//  width: 100%;
	//  top: 0;
	//  left: 0; 
	// z-index: 2;
	// }
	
	.status_bar {
		width: 100%;
	}

	.nav-main {
		display: flex;
		align-items: center;
		
		// justify-content: space-between;
		padding: 0 40rpx 0 20rpx;

		.back {
			width: 195rpx;
			height: 50rpx;
			 margin-top: 10rpx;
		}

		.nav-main-title {
			color: #fff;
			font-size: 32rpx;
		}
	}
</style>
