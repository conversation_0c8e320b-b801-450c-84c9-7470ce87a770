<template>
	<view class="device-installation">
		<view class="form_view rightForm">
			<u--form labelPosition="left" :model="modelInstall" :rules="installRules" ref="installForm"
				label-width="250rpx">
				<u-form-item label="设备编号" prop="userInfo.code" borderBottom :required="mode === 'install'">
					<u--input v-model="modelInstall.userInfo.code" maxlength="30" border="none"
						:placeholder="mode === 'detail' ? '' : '扫码添加设备' "
						:inputAlign="mode === 'detail' ? 'left' : 'right'" :disabled="mode === 'detail'">
					</u--input>
					<u-icon v-show="mode === 'install'" name="scan" color="#2979ff" slot="right" size="25" @click="mode === 'install' ? getScanCode() : null" :disabled="mode !== 'install'"></u-icon>
				</u-form-item>
				<u-form-item label="设备型号" prop="userInfo.deviceModel" borderBottom>
					<u--input v-model="modelInstall.userInfo.deviceModel" maxlength="30" border="none"
						:placeholder="mode === 'detail' ? '' : '请输入设备型号'"
						:inputAlign="mode === 'detail' ? 'left' : 'right'" :disabled="mode === 'detail'"></u--input>
				</u-form-item>
				<u-form-item label="物联卡号" prop="userInfo.iotCardNumber" borderBottom>
					<u--input v-model="modelInstall.userInfo.iotCardNumber" maxlength="30" border="none"
						:placeholder="mode === 'detail' ? '' : '请输入物联网卡号'"
						:inputAlign="mode === 'detail' ? 'left' : 'right'" :disabled="mode === 'detail'"></u--input>
				</u-form-item>
				<u-form-item label="省" prop="userInfo.province" borderBottom>
					<u--input v-model="modelInstall.userInfo.province" maxlength="30" border="none"
						:placeholder="mode === 'detail' ? '' : '请输入省'"
						:inputAlign="mode === 'detail' ? 'left' : 'right'" :disabled="mode === 'detail'"></u--input>
					<u-icon v-show="mode === 'install'" slot="right" name="map" color="#2979ff" size="25"
						@click="mode === 'install' ? scanClock() : null" :disabled="mode !== 'install'"></u-icon>
				</u-form-item>
				<u-form-item label="市" prop="userInfo.city" borderBottom>
					<u--input v-model="modelInstall.userInfo.city" maxlength="30" border="none"
						:placeholder="mode === 'detail' ? '' : '请输入市'"
						:inputAlign="mode === 'detail' ? 'left' : 'right'" :disabled="mode === 'detail'"></u--input>
				</u-form-item>
				<u-form-item label="区" prop="userInfo.area" borderBottom>
					<u--input v-model="modelInstall.userInfo.area" maxlength="30" border="none"
						:placeholder="mode === 'detail' ? '' : '请输入区'"
						:inputAlign="mode === 'detail' ? 'left' : 'right'" :disabled="mode === 'detail'"></u--input>
				</u-form-item>
				<u-form-item label="详细地址" prop="userInfo.detailAddress" borderBottom>
					<view style="width: 100%; height: auto">
						<textarea :placeholder="mode === 'detail' ? '' : '请输入详细地址'"
							:style="'width: 100%; text-align: ' + (mode === 'detail' ? 'left' : 'right')"
							class="textarea1" v-model="modelInstall.userInfo.detailAddress" auto-height
							:disabled="mode === 'detail'"></textarea>
					</view>
				</u-form-item>
				<u-form-item label="经度" prop="userInfo.longitude" borderBottom>
					<u--input v-model="modelInstall.userInfo.longitude" maxlength="30" border="none"
						:placeholder="mode === 'detail' ? '' : '请输入经度'"
						:inputAlign="mode === 'detail' ? 'left' : 'right'" :disabled="mode === 'detail'"></u--input>
				</u-form-item>
				<u-form-item label="纬度" prop="userInfo.latitude" borderBottom>
					<u--input v-model="modelInstall.userInfo.latitude" maxlength="30" border="none"
						:placeholder="mode === 'detail' ? '' : '请输入纬度'"
						:inputAlign="mode === 'detail' ? 'left' : 'right'" :disabled="mode === 'detail'"></u--input>
				</u-form-item>
				<u-form-item :label="mode === 'detail' ? '安装照片' : '上传照片'" borderBottom :required="mode === 'install'">
					<u-upload :fileList="fileList" @afterRead="afterRead" @delete="deletePic" name="1" multiple
						:maxCount="3" :previewFullImage="true" :disabled="mode === 'detail'" :showUploadList="true"
						:showProgress="false"></u-upload>
				</u-form-item>
				<u-form-item label="备注" prop="userInfo.remark" borderBottom>
					<view style="width: 100%; height: auto">
						<textarea :placeholder="mode === 'detail' ? '' : '请输入内容'"
							:style="'width: 100%; text-align: ' + (mode === 'detail' ? 'left' : 'right')"
							class="textarea1" v-model="modelInstall.userInfo.remark" auto-height maxlength="150"
							:disabled="mode === 'detail'"></textarea>
					</view>
					<view class="char-count" v-if="mode === 'install'">{{ modelInstall.userInfo.remark.length }}/150
					</view>
				</u-form-item>
				<u-form-item v-if="mode === 'install'">
					<u-button type="primary" text="提交" :disabled="disabled" :customStyle="primaryBtnCss"
						@click="submit"></u-button>
				</u-form-item>
			</u--form>
		</view>
	</view>
</template>

<script>
import {
	baseURL_a
} from '@/network/base.js';
import {
	installationTaskDetail
} from '@/network/api.js';

export default {
	data() {
		return {
			mode: 'install', // 页面模式：install-安装模式，detail-详情模式
			missionId: '', // 工单ID
			pointId: '', // 安装点ID
			taskId: '', // 任务ID
			pointName: '', // 安装点名称
			pointIdentify: '', // 安装点标识
			fileList: [], // 上传图片列表
			disabled: false,
			primaryBtnCss: {
				width: '574rpx',
				height: '88rpx',
				background: '#0165FC',
				boxShadow: '0rpx 4rpx 24rpx 0rpx rgba(54,150,255,0.4)',
				borderRadius: '200rpx',
				fontSize: '28rpx',
				fontFamily: 'Microsoft YaHei UI-Regular, Microsoft YaHei UI',
				color: '#FFFFFF',
				lineHeight: '28rpx'
			},
			modelInstall: {
				userInfo: {
					code: '', // 设备编号
					deviceModel: '', // 设备型号
					iotCardNumber: '', // 物联卡号
					province: '', // 省
					city: '', // 市
					area: '', // 区
					detailAddress: '', // 详细地址
					longitude: '', // 经度
					latitude: '', // 纬度
					remark: '' // 备注
				}
			},
			installRules: {
				'userInfo.code': {
					type: 'string',
					required: true,
					message: '请输入设备编号',
					trigger: ['blur', 'change']
				}
			}
		};
	},
	onLoad(opt) {
		// 接收传递的参数
		this.mode = opt.mode || 'install'; // 设置页面模式
		this.missionId = opt.missionId || '';
		this.pointId = opt.pointId || '';
		this.taskId = decodeURIComponent(opt.taskId || '');
		this.pointName = decodeURIComponent(opt.pointName || '');
		this.pointIdentify = decodeURIComponent(opt.pointIdentify || '');
		console.log('设备安装/详情页面参数:', opt);
		console.log('解码后的参数:', {
			mode: this.mode,
			missionId: this.missionId,
			pointId: this.pointId,
			taskId: this.taskId,
			pointName: this.pointName,
			pointIdentify: this.pointIdentify
		});

		// 如果是详情模式，加载设备详情数据
		if (this.mode === 'detail') {
			this.loadDeviceDetail();
			// 设置页面标题
			uni.setNavigationBarTitle({
				title: '设备详情'
			});
		} else {
			// 设置页面标题
			uni.setNavigationBarTitle({
				title: '设备安装'
			});
		}
	},
	methods: {
		// 加载设备详情数据
		loadDeviceDetail() {
			uni.showLoading({
				title: '加载中...'
			});

			const params = {
				customerId: uni.getStorageSync('userId'),
				missionTaskId: this.taskId
			};

			installationTaskDetail(params)
				.then(res => {
					console.log('设备详情数据:', res);
					uni.hideLoading();

					if (res.code === 200 && res.data) {
						const taskInfo = res.data.taskInfo;
						const pointInfo = res.data.pointInfo;
						const installImages = res.data.installImages || [];

						// 填充表单数据
						this.modelInstall.userInfo = {
							code: taskInfo.deviceCode || '',
							deviceModel: taskInfo.deviceVersion || '',
							iotCardNumber: taskInfo.iccid || '',
							province: taskInfo.province || '',
							city: taskInfo.city || '',
							area: taskInfo.district || '',
							detailAddress: taskInfo.detailAddress || '',
							longitude: taskInfo.installLon || '',
							latitude: taskInfo.installLat || '',
							remark: taskInfo.remark || ''
						};

						// 处理安装图片
						this.fileList = installImages.map(img => ({
							url: img.url || img,
							status: 'success',
							message: ''
						}));
					} else {
						uni.showToast({
							title: res.msg || '获取设备详情失败',
							icon: 'none'
						});
					}
				})
				.catch(err => {
					console.error('获取设备详情错误:', err);
					uni.hideLoading();
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none'
					});
				});
		},

		// 二维码扫描
		getScanCode() {
			// 详情模式下不允许扫码
			if (this.mode === 'detail') {
				return;
			}

			uni.scanCode({
				onlyFromCamera: true,
				success: res => {
					this.modelInstall.userInfo.code = res.result;
				}
			});
		},
		// 获取区县编码
		async getAdCode() {
			try {
				// 如果有省市区信息，从area_code_2024.json中查找对应的区县编码
				const province = this.modelInstall.userInfo.province;
				const city = this.modelInstall.userInfo.city;
				const district = this.modelInstall.userInfo.area;

				if (!province || !city || !district) {
					return '';
				}

				// 加载区域编码数据
				const areaData = await this.loadAreaCodeData();
				if (!areaData) {
					return '';
				}

				// 查找省份
				const provinceData = areaData.find(item => item.name === province || item.name.includes(province.replace('省', '').replace('市', '')));
				if (!provinceData || !provinceData.children) {
					return '';
				}

				// 查找城市
				let cityData = null;
				for (const cityGroup of provinceData.children) {
					if (cityGroup.children) {
						cityData = cityGroup.children.find(item => item.name === city || item.name.includes(city.replace('市', '').replace('区', '').replace('县', '')));
						if (cityData) break;
					}
					if (cityGroup.name === city || cityGroup.name.includes(city.replace('市', '').replace('区', '').replace('县', ''))) {
						cityData = cityGroup;
						break;
					}
				}

				if (!cityData) {
					return '';
				}

				// 查找区县
				if (cityData.children) {
					const districtData = cityData.children.find(item => item.name === district || item.name.includes(district.replace('区', '').replace('县', '').replace('市', '')));
					if (districtData) {
						return districtData.code.toString();
					}
				}

				// 如果没有找到区县，返回城市编码
				return cityData.code ? cityData.code.toString() : '';

			} catch (error) {
				console.error('获取区县编码失败:', error);
				return '';
			}
		},
		// 加载区域编码数据
		async loadAreaCodeData() {
			return new Promise((resolve) => {
				// 使用绝对路径加载本地JSON文件
				uni.request({
					url: '/static/homePage/area_code_2024.json',
					method: 'GET',
					header: {
						'Content-Type': 'application/json'
					},
					success: (res) => {
						console.log('区域编码数据加载结果:', res);
						if (res.statusCode === 200 && res.data) {
							resolve(res.data);
						} else {
							console.error('加载区域编码数据失败:', res);
							resolve(null);
						}
					},
					fail: (error) => {
						console.error('请求区域编码数据失败:', error);
						// 如果加载失败，尝试使用require方式
						try {
							// 备用方案：直接返回空字符串，避免阻塞提交
							console.log('区域编码加载失败，将使用空值');
							resolve(null);
						} catch (e) {
							console.error('备用加载方案也失败:', e);
							resolve(null);
						}
					}
				});
			});
		},
		// 地图选择地址
		scanClock() {
			// 详情模式下不允许定位
			if (this.mode === 'detail') {
				return;
			}

			let that = this;
			uni.showLoading({
				title: '定位中..'
			});
			uni.getLocation({
				type: 'gcj02',
				success: function (res) {
					console.log('获取位置成功:', res);
					let URL = 'https://apis.map.qq.com/ws/geocoder/v1/?location=';
					let key = 'XMCBZ-RU5CU-3SXV7-GAPV5-BKZXZ-6WBM5';
					let getAddressUrl = URL + res.latitude + ',' + res.longitude + `&key=${key}`;
					uni.request({
						url: getAddressUrl,
						success: result => {
							console.log('腾讯地图API返回:', result);
							uni.hideLoading();

							// 检查API返回状态
							if (result.data && result.data.status === 0 && result.data.result) {
								let Res_Data = result.data.result;
								console.log("🚀 ~ Res_Data:", Res_Data);

								// 检查地址组件是否存在
								if (Res_Data.address_component) {
									that.modelInstall.userInfo.province = Res_Data.address_component.province || '';
									that.modelInstall.userInfo.city = Res_Data.address_component.city || '';
									that.modelInstall.userInfo.area = Res_Data.address_component.district || '';
									that.modelInstall.userInfo.longitude = res.longitude.toString();
									that.modelInstall.userInfo.latitude = res.latitude.toString();

									// 自动填充详细地址
									if (Res_Data.address) {
										that.modelInstall.userInfo.detailAddress = Res_Data.address;
										console.log('自动填充详细地址:', Res_Data.address);
									}

									uni.showToast({
										title: '定位成功',
										icon: 'success'
									});
								} else {
									console.error('地址组件数据缺失');
									uni.showToast({
										title: '地址解析失败',
										icon: 'none'
									});
								}
							} else {
								console.error('腾讯地图API错误:', result.data);
								uni.showToast({
									title: result.data?.message || '地址解析失败',
									icon: 'none'
								});
							}
						},
						fail: function (error) {
							console.error('腾讯地图API请求失败:', error);
							uni.hideLoading();
							uni.showToast({
								title: '网络请求失败',
								icon: 'none'
							});
						}
					});
				},
				fail: function (error) {
					console.error('获取位置失败:', error);
					uni.hideLoading();
					uni.showToast({
						title: '定位失败，请检查定位权限',
						icon: 'none'
					});
				}
			});
		},
		// 新增图片
		afterRead(event) {
			// 详情模式下不允许上传图片
			if (this.mode === 'detail') {
				return;
			}

			// 当设置 mutiple 为 true 时, file 为数组格式，否则为对象格式
			console.log("event", event);
			console.log("event22", this.fileList);
			let lists = [].concat(event.file);
			lists.map((item) => {
				this.fileList.push({
					...item,
					status: 'success',
					message: ''
				});
			});
		},
		// 删除图片
		deletePic(event) {
			// 详情模式下不允许删除图片
			if (this.mode === 'detail') {
				return;
			}

			this.fileList.splice(event.index, 1);
		},
		// 使用FormData格式上传文件
		async uploadWithFormData() {
			return new Promise(async (resolve, reject) => {
				// 构建表单数据
				const formData = {
					taskId: this.taskId,
					backPointName: this.pointName, // 安装点名称
					backPointIdentify: this.pointIdentify, // 安装点标识
					deviceCode: this.modelInstall.userInfo.code,
					deviceVersion: this.modelInstall.userInfo.deviceModel,
					iccid: this.modelInstall.userInfo.iotCardNumber,
					province: this.modelInstall.userInfo.province,
					city: this.modelInstall.userInfo.city,
					district: this.modelInstall.userInfo.area,
					town: '', // 镇/街道，如果需要可以添加到表单中
					detailAddress: this.modelInstall.userInfo.detailAddress,
					installLon: this.modelInstall.userInfo.longitude,
					installLat: this.modelInstall.userInfo.latitude,
					// installTime: new Date().toISOString().slice(0, 19).replace('T', ' '), // 当前时间
					remark: this.modelInstall.userInfo.remark,
					adCode: await this.getAdCode(), // 获取区县编码
					customerId: uni.getStorageSync('userId')
				};

				// 打印完整的提交参数用于调试
				console.log('=== 设备安装提交参数调试信息 ===');
				console.log('完整formData:', JSON.stringify(formData, null, 2));
				console.log('文件列表:', this.fileList);
				console.log('文件数量:', this.fileList.length);
				console.log('提交接口:', baseURL_a + '/micro-app/installation/commit');
				console.log('Authorization token:', uni.getStorageSync('token'));
				console.log('================================');

				// 如果只有一个文件，使用uni.uploadFile
				if (this.fileList.length === 1) {
					uni.uploadFile({
						url: baseURL_a + '/micro-app/installation/commit',
						filePath: this.fileList[0].url,
						name: 'installImages',
						formData: formData,
						header: {
							"Authorization": `${uni.getStorageSync('token')}` || ''
						},
						success: (res) => {
							console.log('上传结果:', res);
							try {
								const data = typeof res.data === 'string' ? JSON.parse(res.data) : res.data;
								resolve(data);
							} catch (e) {
								resolve(res.data);
							}
						},
						fail: (err) => {
							console.error('上传失败:', err);
							reject(err);
						}
					});
				} else {
					// 多文件上传，需要逐个上传或使用其他方式
					this.uploadMultipleFiles(formData).then(resolve).catch(reject);
				}
			});
		},
		// 多文件上传处理
		uploadMultipleFiles(formData) {
			return new Promise((resolve, reject) => {
				let uploadPromises = [];

				// 为每个文件创建上传Promise
				this.fileList.forEach((file, index) => {
					const promise = new Promise((fileResolve, fileReject) => {
						// 为每个文件添加索引到formData
						const fileFormData = {
							...formData,
							fileIndex: index
						};

						uni.uploadFile({
							url: baseURL_a + '/micro-app/installation/commit',
							filePath: file.url,
							name: 'installImages',
							formData: fileFormData,
							header: {
								"Authorization": `${uni.getStorageSync('token')}` || ''
							},
							success: (res) => {
								try {
									const data = typeof res.data === 'string' ? JSON.parse(res.data) : res.data;
									fileResolve(data);
								} catch (e) {
									fileResolve(res.data);
								}
							},
							fail: (err) => {
								fileReject(err);
							}
						});
					});
					uploadPromises.push(promise);
				});

				// 等待所有文件上传完成
				Promise.all(uploadPromises)
					.then(results => {
						// 返回最后一个结果或合并结果
						resolve(results[results.length - 1]);
					})
					.catch(reject);
			});
		},

		// 提交表单
		submit() {
			// 详情模式下不允许提交
			if (this.mode === 'detail') {
				return;
			}

			this.$refs.installForm.validate().then(res => {
				// 检查是否上传了照片
				if (this.fileList.length === 0) {
					uni.showToast({
						title: '请上传安装照片',
						icon: 'none'
					});
					return;
				}

				uni.showLoading({
					title: '提交中...'
				});

				this.disabled = true;

				// 使用FormData格式上传文件
				this.uploadWithFormData()
					.then(res => {
						console.log('提交结果:', res);
						uni.hideLoading();
						if (res.code === 200) {
							uni.showToast({
								title: '安装成功',
								icon: 'success'
							});
							// 返回上一页并刷新
							setTimeout(() => {
								uni.navigateBack({
									delta: 1,
									success: () => {
										// 通知上一页刷新数据
										uni.$emit('refreshInstallationList');
									}
								});
							}, 1500);
						} else {
							uni.showToast({
								title: res.msg || '提交失败',
								icon: 'none'
							});
							this.disabled = false;
						}
					})
					.catch(err => {
						console.error('提交错误:', err);
						uni.hideLoading();
						uni.showToast({
							title: '网络错误，请稍后重试',
							icon: 'none'
						});
						this.disabled = false;
					});
			}).catch(errors => {
				uni.showToast({
					title: '请完善必填信息',
					icon: 'none'
				});
			});
		}
	},
	onReady() {
		// 只在安装模式下设置表单验证规则
		if (this.mode === 'install') {
			this.$refs.installForm.setRules(this.installRules);
		}
	}
};
</script>

<style lang="scss" scoped>
.device-installation {
	padding: 20rpx 30rpx 160rpx;
	background-color: #F6F6F6;
	min-height: 100vh;

	.form_view {
		background-color: #ffffff;
		border-radius: 20rpx;
		padding: 40rpx 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	}

	.textarea1 {
		min-height: 80rpx;
		padding: 20rpx;
		border: 2rpx solid #e4e7ed;
		border-radius: 8rpx;
		font-size: 28rpx;
		color: #606266;
		line-height: 1.4;
		box-sizing: border-box;
		resize: none;
		overflow: hidden;

		&::placeholder {
			color: #c0c4cc;
		}

		&:disabled {
			background-color: #f5f7fa;
			color: #909399;
			border-color: #e4e7ed;
			cursor: not-allowed;
		}
	}

	.char-count {
		text-align: right;
		font-size: 24rpx;
		color: #909399;
		margin-top: 10rpx;
	}
}
</style>