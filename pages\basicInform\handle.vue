<template>
	<view class="dispatch">
		<view class="head">
			<view class="head-body">
			<image style="height: 100rpx;width: 100rpx;margin-left: 50rpx;" src="@/static/product/home.png" mode="" v-if="devContent.equipType=='001'"></image>
			<image style="height: 100rpx;width: 100rpx;margin-left: 50rpx;" src="@/static/product/fajing.png" mode="" v-else-if="devContent.equipType=='002'"></image>
			<image v-else-if="devContent.equipType=='003'" style="height: 100rpx;width: 100rpx;" src="@/static/product/gongshang.png" mode="" ></image>
			<image style="height: 100rpx;width: 100rpx;margin-left: 50rpx;" src="@/static/homePage/dmsIcon.png" mode="" v-else-if="devContent.equipType=='004'"></image>
		    </view>
			<view class="head-text">
				<view class="head-text1">
			<text  v-if="devContent.equipType=='001'">家用型可燃气体探测器</text>
			<text  v-else-if="devContent.equipType=='002'">地下空间燃气泄漏监测仪</text>
			<text  v-else-if="devContent.equipType=='003'">工商业可燃气体探测器</text>
			<text  v-else-if="devContent.equipType=='004'">地埋式燃气泄漏监测仪</text>
			    </view>
				<view class="head-text2">
			<text>设备编号：{{devContent.code}}</text>
		        </view>
			</view>

		</view>
		
		<view class="body1">
			<view class="body1-0">
			<view class="body1-a">
				<text style="color: #98968d;">设备名称:</text>
				<text>{{devContent.name}}</text>
			</view>
			<view class="body1-a">
				<text style="color: #98968d;">设备浓度:</text>
				<text>{{devContent.chroma}}%LEL</text>
			</view>
			<view class="body1-a">
				<text style="color: #98968d;">故障状态:</text>
				<text>{{devContent.status}}</text>
			</view>
			<view class="body1-a">	
			     <text style="color: #98968d;">故障描述:</text>
			     <text>{{devContent.description}}</text>
			</view>
			<view class="body1-a">
				<text style="color: #98968d;">故障创建时间:</text>
				<text>{{devContent.createTime}}</text>
			</view>
			<view class="body1-a">
				<text style="color: #98968d;">设备安装地址:</text>
				<text>{{devContent.address}}</text>
			</view>
		    <view class="body1-a">
		    	<text style="color: #98968d;">故障上传照片:</text>
				<u-upload :file-list="fileList1"  name="1" disabled multiple :maxCount="3"
				:previewFullImage="true" :deletable='false' useBeforeRead></u-upload>
		    	<!-- <u-upload :file-list="fileList1"  name="1"  :maxCount="3"
		    	 :deletable='false' ></u-upload> -->
		    </view>
			<view class="body1-a">
				<text style="color: #98968d;">用户沟通电话:</text>
				<text>{{devContent.mobile}}</text>
			</view>
			<view class="body1-a">
				<text style="color: #98968d;">用户统一标识号:</text>
				<text>{{devContent.userId}}</text>
			</view>
			</view>
		</view>
		      <view class="" style="margin-top: 70rpx;margin-bottom: 30rpx;">
			  <text style="font-size: 36rpx;margin-left: 300rpx;">派遣信息</text>
		      </view>
		<view class="body2">
			<view class="form_view rightForm">
			<u--form labelPosition="left" :model="modelAdd" :rules="addRules" ref="addForm" labelWidth="120" :labelStyle="labelStyle">
				
				<!-- <u-form-item label="派遣人员" prop="userInfo.description" borderBottom :required="true" @click="sceneShow = true;">
					<view style="width: 100%; height: auto">
						
						<!-- <textarea placeholder="请选择派遣人员" style="width: 100%;" class="textarea1"  v-model="modelAdd.userInfo.ywName"  auto-height ></textarea> -->
					   <!-- <u--input  v-model="modelAdd.userInfo.mobile" maxlength="15"   disabled disabledColor="#ffffff"
					   placeholder="请输入电话号码" border="none" inputAlign="right"	></u--input>
					</view>	
					<u-icon slot="right" name="arrow-right"></u-icon>
				</u-form-item> --> 
				<u-form-item  label="运维人员" prop="userInfo.mobile" borderBottom :required="true" @click="sceneShow = true;">
					<u--input  v-model="modelAdd.userInfo.ywName" maxlength="15"   disabled disabledColor="#ffffff" 
					placeholder="请选择运维人员" border="none" inputAlign="right"	></u--input>
					<u-icon slot="right" name="arrow-right"></u-icon>	
				</u-form-item>
				<u-form-item  label="联系电话" prop="userInfo.mobile" borderBottom :required="true" >
					<u--input  v-model="modelAdd.userInfo.mobile" maxlength="15"   disabled disabledColor="#ffffff" 
					 border="none" inputAlign="right"	></u--input>
						
				</u-form-item>
			<!-- <u-form-item label="备注信息" prop="userInfo.description" borderBottom :required="true">
				<view style="width: 100%; height: auto">
					 <textarea  style="width: 100%;" class="textarea1"  v-model="modelAdd.userInfo.description"  auto-height ></textarea>
				</view>
				
				</u-form-item> -->
				
				<u-form-item>
					<u-button type="primary" text="派遣" :disabled="disabled" :customStyle="primaryBtnCss"
						@click="submit"></u-button>
				</u-form-item>
			</u--form>
			</view>
			
			
		</view>
		
		<u-picker itemHeight="60" :show="sceneShow" :columns="sceneColumns" keyName="name" @confirm="sceneConfirm"
			@cancel="sceneCancel"></u-picker>
	</view>
</template>

<script>
	import {
		cstFault,
		YWList,
		Update
	} from "@/network/api.js"
	import {
		baseURL
	} from '@/network/base.js';
	export default {
		data() {
			return {
				devContent:{},
				fileList1: [],
				fileList2: [],
				sceneShow: false,
				sceneColumns: [],
				updateData: {
					operationId: '',
					id: 0,
					status: 1,
				},
				modelAdd: {
					userInfo: {
						name: '',
						equipDev: '',
						equipType: '',
						code: '',
						chroma: '',
						address: '',
						createTime: '',
						description: '',
						picUrl: "",
						type: '',
						mobile: '',
						userId: '',
						ywName: '',
						ywMobile: '',
						id: '',
					},
				},
				
				addRules: {

					'userInfo.description': {
						type: 'string',
						required: true,
						message: '请输入处理结果',
						trigger: [ 'blur','change']
					},
					'userInfo.picUrl': {
						type: 'string',
						required: true,
						message: '请上传处理照片',
						trigger: ['blur', 'change']
					},
					'userInfo.mobile': {
						type: 'string',
						required: true,
						message: '请输入电话号码',
						trigger: ['blur', 'change']
					},
				}
			}
		},
		methods: {
			onLoad(opt) { // 初始化
			this.devicDetail(opt.gzId)
			},
			devicDetail(id) {
				cstFault(id).then(res => {
						let resData = res.data;
						if (resData.picUrl != '') {
							let picUrlArry = (resData.picUrl).split(',');
							let fileArry = [];
							for (let i = 0; i < picUrlArry.length; i++) {
								fileArry.push({
									url: picUrlArry[i]
								})
							}
							
							this.fileList1 = fileArry;
						}
						this.devContent = res.data
						
				}).catch((err) => {})
				
				YWList().then(res => {
					console.log('eeee', res)
					this.sceneColumns = [];
					this.sceneColumns.push(res.data);
				}).catch((err) => {})
				
			
			
			},
			sceneConfirm(e) {
				console.log('eeee', e)
				this.modelAdd.userInfo.ywName = e.value[0].name
				this.modelAdd.userInfo.mobile = e.value[0].mobile
				this.modelAdd.userInfo.id = e.value[0].id
				this.$refs.addForm.validateField('userInfo.mobile')
				this.sceneShow = false;
			},
			sceneCancel() {
				this.sceneShow = false;
			},
			scanClock(){
				let that = this;
				uni.showLoading({
					title: '定位中..'
				})
				uni.getLocation({
					type: 'gcj02',
					success: function (res) {
						let URL = 'https://apis.map.qq.com/ws/geocoder/v1/?location=';
						let key = 'XMCBZ-RU5CU-3SXV7-GAPV5-BKZXZ-6WBM5';
						let getAddressUrl = URL + res.latitude + ',' + res.longitude + `&key=${key}`;
						uni.request({
							url: getAddressUrl,
							success: result => {
								let Res_Data = result.data.result;
								console.log("🚀 ~ Res_Data:", Res_Data.address)
								that.modelPatient.userInfo.address = Res_Data.address;
								uni.hideLoading();
							}
						});
					}
				});
			},
			submit() {
				this.$refs.addForm.validate().then(res => {
					 // uni.$u.toast('校验通过')
					// this.showModal = true;
					//this.modalConfirm()
					let form = this.updateData;
					  form.id = this.devContent.id;
					  form.operationId = this.modelAdd.userInfo.id;
					  console.log(form,'三传')
					Update(form).then(res => {
							uni.navigateBack({
								delta: 1 // 默认值是1，表示返回的页面层数
							});
							uni.$u.toast('提交成功')
						})
						.catch((err) => {
							console.log("1212", err)
							
						})
					
				}).catch(errors => {
					 uni.$u.toast('校验失败')
				})
				},
			// 新增图片
			async afterRead(event) {
				// 当设置 mutiple 为 true 时, file 为数组格式，否则为对象格式
				console.log("event", event)
				console.log("event22", this.fileList2)
				let lists = [].concat(event.file)
				let fileListLen = this.fileList2.length
				lists.map((item) => {
					this.fileList2.push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				for (let i = 0; i < lists.length; i++) {
					const result = await this.uploadFilePromise(lists[i].url)
					let item = this.fileList2[fileListLen]
					this.fileList2.splice(fileListLen, 1, Object.assign(item, {
						status: 'success',
						message: '',
						url: result
					}))
					fileListLen++
				}
			},
			// 删除图片
			deletePic(event) {
				this.fileList2.splice(event.index, 1)
			},
			uploadFilePromise(url) {
				return new Promise((resolve, reject) => {
					let a = uni.uploadFile({
						url: baseURL + '/common/upload', // 仅为示例，非真实的接口地址
						filePath: url,
						name: 'file',
						// formData: {
						// 	user: 'test'
						// },
						success: (res) => {
							console.log("iiii", res, typeof(res.data))
							setTimeout(() => {
								resolve(res.data)
							}, 1000)
						}
					});
				})
			},
			
		}
	}
</script>
<style>
	page {
		background-color: #F6F6F6;
	}
</style>
<style lang="scss" scoped>

.dispatch{
	.head{
		background-color: #ffffff;
		margin-top: 20rpx;
		width: 690rpx;
		margin-left: 30rpx;
		height: 120rpx;
		display: flex;
		align-items: center;
		text-align: center; 
		
		.head-body{
			
		}
		.head-text {
			width: 100%;
			display: flex;
			flex-direction: column;
			align-items: center;
			text-align: center; 
			.head-text1 {

				}
			.head-text2 {	
				text{
					color: #a9d9ff;
				}
				}
		}
	}

     .body1{
		 
		 width: 700rpx;
		 background-color: #ffffff;
		 margin-top: 20rpx;
		 margin-left: 25rpx;
		
		 
		 .body1-0{
			 border-radius: 32rpx;
			 padding: 1.5%;
			 margin: 10rpx 0;
			 display: flex;
			 flex-direction: column;
		 .body1-a {
			display: grid;
			grid-template-columns: 37% 63%;
			
		 }
		
		 }
	 }
	 
	 .body2 {
		 background-color: #ffffff;
		 width: 700rpx;
		 margin-left: 25rpx;
		 padding: 0 0 100rpx;
		 textarea::placeholder {
			 text-align:right;
		 }
	 }
	 
	placeholder {
		 text-align:right;
	}

}
</style>
