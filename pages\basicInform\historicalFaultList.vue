<template>
	<!-- 历史故障列表 -->
	<view class="historicalList">
		<!-- 查询筛选 -->
		<view class="queryView">
			<view class="query_a">
				<view class="query_a_m" v-if='!showQuerySheet'>
					<text v-for="(item,index) in modelQuery.queryForm" :key="index">
						{{item}}
					</text>
				</view>
			</view>
			<u-button type="primary" :customStyle="primaryBtnCss" text="筛选" @click="showQuerySheet = true"></u-button>
		</view>
		<u-action-sheet :show="showQuerySheet" title="筛选" @close="showQuerySheet = false">
			<view class="form_view rightForm">
				<u--form labelPosition="left" :model="modelQuery" ref="queryForm" labelWidth="100"
					:labelStyle="labelStyle">
					<u-form-item label="设备类型" prop="queryForm.equipTypeName" borderBottom @click="typeShow = true;">
						<u--input v-model="modelQuery.queryForm.equipTypeName" disabled disabledColor="#ffffff"
							inputAlign="right" border="none" placeholder="请选择设备类型"></u--input>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>
					<u-form-item label="设备编号" prop="queryForm.code" borderBottom>
						<u--input v-model="modelQuery.queryForm.code" maxlength="30" border="none" placeholder="请输入设备编号"
							inputAlign="right"></u--input>
					</u-form-item>
				</u--form>
				<u-picker itemHeight="60" :show="typeShow" :columns="typeColumns" keyName="name" @confirm="typeConfirm"
					@cancel="typeCancel"></u-picker>
				<view class="twoBtn">
					<u-button type="info" text="重置" :customStyle="otherBtnCss" @click="resetQuery"></u-button>
					<u-button type="primary" text="确定" :customStyle="otherBtnCss" @click="saveQuery"></u-button>
				</view>
			</view>
		</u-action-sheet>
		<view class="listView">
			<view class="listModule" v-for="(item,index) in gzDataList" :key='index' @click="goFaultDetail(item)">
				<view class="list_left">
					<text>故障</text>
					<image src="@/static/homePage/gzlIcon.png" mode=""></image>
				</view>
				<view class="list_right">
					<view class="list_a">
						<text>{{item.code}}</text>
						<text v-if="item.status == '待处理'" style="font-size: 24rpx;color: #ff0000;line-height: 44rpx;" class="list_a_b">{{item.status}}</text>
						<text v-else-if="item.status == '处理中'" style="font-size: 24rpx;color: #f1e20e;line-height: 44rpx;" class="list_a_b">{{item.status}}</text>
						<text v-else-if="item.status == '已处理'" style="font-size: 24rpx;color: #55aa00;line-height: 44rpx;" class="list_a_b">{{item.status}}</text>
					</view>
					<view class="list_b">
						<text>{{item.name}}</text>
						<text class="list_b_b">{{item.createTime}}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		cstFaultList,
		DefaultList
	} from "@/network/api.js"
	export default {
		data() {
			return {
				showQuerySheet: false,
				primaryBtnCss: {
					width: '100rpx',
					height: '58rpx',
					// background: '#0165FC',
					borderRadius: '60rpx',
					fontSize: '28rpx',
					// color: '#FFFFFF',
					lineHeight: '28rpx',
					margin: '0'
				},
				otherBtnCss: {
					width: '260rpx',
					borderRadius: '60rpx',
					fontSize: '28rpx',
					fontFamily: 'Microsoft YaHei UI-Regular, Microsoft YaHei UI',
					lineHeight: '28rpx'
				},
				typeShow: false,
				typeColumns: [
					[{
						name: '家用型可燃气体探测器',
							id: '001'
						},
						{
							name: '地下空间燃气泄漏监测仪',
							id: '002'
						},
						{
							name: '工商业可燃气体探测器',
							id: '003'
						},
						{
							name: '地埋式燃气泄漏监测仪',
							id: '004'
						}
					]
				],
				modelQuery: {
					queryForm: {
						equipTypeName: '',
						code: ''
					},
				},
				equipType: '',
				gzDataList: [], //数据
				total: null, //总条数
				pageNum: 1, //第几页
				pageSize: 15 //显示多少条
			};
		},
		onLoad(opt) { // 初始化
			// this.init(opt)
			this.getList()
		},
		methods: {
			init(opt) { // 初始化数据
				// console.log(JSON.parse(opt.params));
			},
			resetQuery() {
				this.modelQuery.queryForm.equipTypeName = '';
				this.equipType = '';
				this.modelQuery.queryForm.code = '';
				this.pageNum = 1;
				this.gzDataList = [];
				this.getList();
				this.showQuerySheet = false;
			},
			saveQuery() {
				this.showQuerySheet = false;
				this.pageNum = 1;
				this.gzDataList = [];
				this.getList();
			},
			typeConfirm(e) {
				this.modelQuery.queryForm.equipTypeName = e.value[0].name
				this.equipType = e.value[0].id
				this.typeShow = false;
			},
			typeCancel() {
				this.typeShow = false;
			},
			getList() { //获取数据
				let params = {
					userId: uni.getStorageSync('userId'),
					equipType: this.equipType,
					code: this.modelQuery.queryForm.code,
					pageNum: this.pageNum,
					pageSize: this.pageSize
				}
				console.log(params,'params')
				DefaultList(params).then(res => {
					console.log(res.data,'故障列表');
						this.gzDataList = [...this.gzDataList, ...res.data.list]
					console.log(this.gzDataList,'故障列表1');	
					this.total = res.data.total
						
				})
				console.log(this.total,'total')
				// cstFaultList(params).then(res => {
				// 	if (res.rows) {
				// 		this.gzDataList = [...this.gzDataList, ...res.rows]
				// 	}
				// 	this.total = res.total
				// })
			},
			goFaultDetail(item) {
				if(item.status == '已处理')
				{
					uni.navigateTo({
						url: './dispatch?gzId=' + item.id
					})
				}
				else if(item.status == '处理中'){
					uni.navigateTo({
						url: './dispatch?gzId=' + item.id
					})
				}
				else if(item.status == '待处理'){
					uni.navigateTo({
						url: './faultDetails?gzId=' + item.id
					})
				}
				
			}
		},
		onReachBottom() { //触底事件
			if (this.pageNum * this.pageSize >= this.total) {
				uni.showToast({
					title: '没有更多数据了',
					icon: 'none',
					duration: 1000
				});
			} else {
				if (this.pageNum <= this.pageNum - 1) {
					setTimeout(() => {
						uni.hideLoading()
					}, 500)
				} else {
					uni.showLoading({
						title: '加载中'
					});
					this.pageNum++
					this.getList()
				}
				setTimeout(() => {
					uni.hideLoading()
				}, 500)
			}
		},
		onReady() {},
	};
</script>
<style>
	page {
		background-color: #F6F6F6;
	}
</style>
<style lang="scss" scoped>
	.historicalList {
		.queryView {
			width: 92%;
			padding: 10rpx 4%;
			background-color: #ffffff;
			margin: 10rpx 0;
			position: fixed;
			top: 0;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.query_a {
				.query_a_m {
					display: flex;

					text {
						padding: 0 16rpx;
						font-size: 26rpx;
						color: #87898f;
						font-weight: 600;
						display: block;
					}
				}
			}
		}

		.twoBtn {
			margin-top: 30rpx;
			display: flex;
		}

		.listView {
			margin-top: 95rpx;

			.listModule {
				display: flex;
				background-color: #ffffff;
				margin-bottom: 12rpx;

				.list_left {
					width: 102rpx;
					padding: 26rpx 0 26rpx 32rpx;
					background-image: linear-gradient(to right, rgba(255, 153, 0, 0.5) 1%, rgba(216, 216, 216, 0) 84%);

					text {
						font-weight: 600;
						font-size: 28rpx;
						color: #FF9900;
						line-height: 44rpx;
						display: block;
					}

					image {
						width: 40rpx;
						height: 40rpx;
						margin-top: 12rpx;
					}
				}

				.list_right {
					flex: 1;
					display: flex;
					flex-direction: column;
					justify-content: center;
					padding-right: 32rpx;

					text {
						font-weight: 500;
						font-size: 28rpx;
						line-height: 44rpx;
					}

					.list_a {
						color: #3D3D3D;
						display: flex;
						justify-content: space-between;
						align-items: center;
						margin-bottom: 10rpx;

						// .list_a_b {
						// 	font-size: 24rpx;
						// 	color: #FF9900;
						// 	line-height: 44rpx;
						// }
					}

					.list_b {
						color: #606266;
						display: flex;
						justify-content: space-between;
						align-items: center;

						.list_b_b {
							font-size: 24rpx;
							color: #616161;
							line-height: 44rpx;
						}
					}
				}
			}
		}
	}
</style>