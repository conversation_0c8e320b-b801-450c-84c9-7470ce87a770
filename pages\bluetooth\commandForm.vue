<template>
	<view class="command-page">
		<view class="container">
			<view class="form-card">
				<view class="form-group">
					<view class="label">参数类型</view>
					<picker @change="paramTypeChange" :value="paramTypeIndex" :range="paramTypeArray" range-key="label">
						<view class="form-input">
							<text>{{ paramTypeText || '请选择参数类型' }}</text>
							<text class="picker-arrow">></text>
						</view>
					</picker>
				</view>
			</view>

			<view class="section-title">参数配置</view>

			<view class="form-card">
				<view class="form-group" v-if="paramType === '0x10'">
					<view class="label required">上报IP</view>
					<input class="form-input" type="text" v-model="reportIp" placeholder="请输入上报IP地址" />
				</view>

				<view class="form-group" v-if="paramType === '0x10'">
					<view class="label required">上报端口</view>
					<input class="form-input" type="number" v-model="reportPort" placeholder="请输入上报端口号" />
				</view>

				<view class="form-group" v-if="paramType === '0x11'">
					<view class="label required">采集周期(分钟)</view>
					<input class="form-input" type="number" v-model="collectFrequency" placeholder="请输入采集周期，如30" />
					<view class="form-hint">周期范围：1-43200分钟</view>
				</view>

				<view class="form-group" v-if="paramType === '0x11'">
					<view class="label required">上报周期(分钟)</view>
					<input class="form-input" type="number" v-model="reportFrequency" placeholder="请输入上报周期，如60" />
					<view class="form-hint">周期范围：1-43200分钟</view>
				</view>

				<view class="form-group" v-if="paramType === '0x12'">
					<view class="label required">高报阈值</view>
					<input class="form-input" type="number" v-model="highThreshold" placeholder="请输入高报阈值" />
				</view>

				<view class="form-group" v-if="paramType === '0x12'">
					<view class="label required">低报阈值</view>
					<input class="form-input" type="number" v-model="lowThreshold" placeholder="请输入低报阈值" />
				</view>

				<!-- 0x19 - 设置电池截止电压 -->
				<view class="form-group" v-if="paramType === '0x19'">
					<view class="label required">电池截止电压(mV)</view>
					<input class="form-input" type="number" v-model="batteryVoltage" placeholder="请输入电池截止电压，如3300" />
					<view class="form-hint">电压范围：2700mV - 3600mV</view>
				</view>

				<!-- 0x1a - 设置高低报间隔时间 -->
				<view class="form-group" v-if="paramType === '0x1a'">
					<view class="label required">高低报间隔时间(分钟)</view>
					<input class="form-input" type="number" v-model="callbackTime" placeholder="请输入间隔时间，如10" />
					<view class="form-hint">时间范围：1-43200分钟</view>
				</view>

				<!-- 0x1c - 清零外液次数 -->
				<view class="form-group" v-if="paramType === '0x1c'">
					<view class="form-message">
						<text>此操作将清零设备的水浸次数计数器</text>
					</view>
				</view>

				<!-- 0x1e - 清零低液次数 -->
				<view class="form-group" v-if="paramType === '0x1e'">
					<view class="form-message">
						<text>此操作将清零设备的低报次数计数器</text>
					</view>
				</view>

				<!-- 0x1f - 设置GPS坐标 -->
				<view class="form-group" v-if="paramType === '0x1f'">
					<view class="label required">GPS纬度</view>
					<input class="form-input" type="text" v-model="gpsLatitude" placeholder="请输入纬度，如39.908823" />
				</view>

				<view class="form-group" v-if="paramType === '0x1f'">
					<view class="label required">GPS经度</view>
					<input class="form-input" type="text" v-model="gpsLongitude" placeholder="请输入经度，如116.397470" />
				</view>

				<view class="form-group" v-if="paramType === '0x1f'">
					<button class="btn btn-map-picker" @click="openMapPicker">
						<text class="map-icon">📍</text>
						<text>地图拾取坐标</text>
					</button>
					<view class="form-hint">支持手动输入或地图拾取经纬度坐标</view>
				</view>

				<!-- 0x20 - 设置水浸开关 -->
				<view class="form-group" v-if="paramType === '0x20'">
					<view class="label required">水浸开关状态</view>
					<picker @change="waterSwitchChange" :value="waterSwitchIndex" :range="waterSwitchArray"
						range-key="label">
						<view class="form-input">
							<text>{{ waterSwitchText || '请选择开关状态' }}</text>
							<text class="picker-arrow">></text>
						</view>
					</picker>
				</view>

				<!-- 0x21 - 角度校准 -->
				<view class="form-group" v-if="paramType === '0x21'">
					<view class="form-message">
						<text>此操作将对设备进行角度校准，请确保设备处于水平状态</text>
					</view>
				</view>

				<view class="form-group" v-if="paramType">
					<view class="label">指令预览</view>
					<view class="command-preview">{{ previewCommand || '完成参数填写后显示指令' }}</view>
				</view>
			</view>

			<view class="form-actions">
				<button class="btn btn-secondary" @click="resetForm">重置</button>
				<button class="btn btn-primary" @click="submitCommand">提交指令</button>
			</view>
		</view>

		<!-- 加载提示浮层 - 替换u-popup为普通弹窗 -->
		<view class="modal-overlay" v-if="showLoading">
			<view class="modal-content loading-modal">
				<view class="loading-icon"></view>
				<text class="loading-text">指令发送中...</text>
			</view>
		</view>

		<!-- 结果提示弹窗 - 替换u-modal为普通弹窗 -->
		<view class="modal-overlay" v-if="showResult">
			<view class="modal-content result-modal">
				<view class="result-icon" :class="resultData.success ? 'success' : 'error'">
					{{ resultData.success ? '✓' : '✕' }}
				</view>
				<view class="result-title">{{ resultData.title }}</view>
				<view class="result-message">{{ resultData.content }}</view>
				<button class="btn btn-primary confirm-btn" @click="handleResultConfirm">知道了</button>
			</view>
		</view>

		<!-- 替换u-toast为简单的自定义toast -->
		<view class="toast-container" v-if="showToast">
			<view class="toast-message" :class="toastType">{{ toastMessage }}</view>
		</view>

		<!-- 地图坐标拾取弹窗 -->
		<view class="modal-overlay" v-if="showMapPicker">
			<view class="modal-content map-picker-modal">
				<view class="map-picker-close" @click="closeMapPicker">✕</view>
				<view class="map-container">
					<map id="gpsMap" :longitude="mapCenter.longitude" :latitude="mapCenter.latitude" :scale="16"
						:markers="mapMarkers" @tap="onMapTap" @markertap="onMarkerTap" class="gps-map">
					</map>
				</view>
				<view class="coordinate-display">
					<text class="coordinate-text">纬度: {{ selectedCoordinate.latitude || '请点击地图选择' }}</text>
					<text class="coordinate-text">经度: {{ selectedCoordinate.longitude || '请点击地图选择' }}</text>
				</view>
				<view class="map-picker-actions">
					<button class="map-btn map-btn-cancel" @click="closeMapPicker">取消</button>
					<button class="map-btn map-btn-confirm" @click="confirmCoordinate"
						:disabled="!selectedCoordinate.latitude">确认选择</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			currentDevice: null,
			reportIp: '',
			reportPort: '',
			collectFrequency: '',
			reportFrequency: '',
			highThreshold: '',
			lowThreshold: '',

			// 新增指令的数据字段
			batteryVoltage: '', // 0x19 - 电池截止电压 (DWORD)
			callbackTime: '', // 0x1a - 高低报回调时间 (WORD)
			// 0x1c, 0x1e 清零操作无需参数
			gpsLatitude: '', // 0x1f - GPS纬度
			gpsLongitude: '', // 0x1f - GPS经度
			showMapPicker: false, // 地图选择器显示状态
			waterSwitch: '', // 0x20 - 水浸开关 (0/1)
			waterSwitchIndex: 0,
			waterSwitchText: '',
			waterSwitchArray: [
				{ value: '0', label: '关闭' },
				{ value: '1', label: '开启' }
			],
			// 0x21 角度校准无需参数

			showLoading: false,
			showResult: false,

			resultData: {
				title: '',
				content: '',
				success: false
			},

			commandHistory: [], // 用于存储命令历史

			// 添加toast相关变量
			showToast: false,
			toastMessage: '',
			toastType: 'default',

			// 添加全局响应处理相关状态
			pendingCommands: new Map(),
			commandCounter: 0,
			globalResponseTimeout: 10000, // 10秒超时

			// 地图相关状态
			mapCenter: {
				latitude: 39.908823, // 默认北京坐标
				longitude: 116.397470
			},
			selectedCoordinate: {
				latitude: '',
				longitude: ''
			},
			mapMarkers: [],
			paramType: '',
			paramTypeText: '',
			paramTypeIndex: 0,
			paramTypeArray: [
				{
					value: '0x10',
					label: '设备网络参数配置'
				},
				{
					value: '0x11',
					label: '设备监测频率设置'
				},
				{
					value: '0x12',
					label: '设备报警阈值设置'
				},
				{
					value: '0x19',
					label: '设置电池截止电压'
				},
				{
					value: '0x1a',
					label: '设置高低报间隔时间'
				},
				{
					value: '0x1c',
					label: '清零水浸次数'
				},
				{
					value: '0x1e',
					label: '清零低报次数'
				},
				{
					value: '0x1f',
					label: '设置GPS坐标'
				},
				{
					value: '0x20',
					label: '设置水浸开关'
				},
				{
					value: '0x21',
					label: '角度校准'
				}
			],
		}
	},
	computed: {
		// 计算指令预览
		previewCommand() {
			if (!this.paramType) return '';

			try {
				let cmd = 0;
				let dataBytes = [];
				let dataLength = 0;

				// 确定命令类型和数据
				if (this.paramType === '0x10') { // IP端口配置
					if (!this.reportIp || !this.reportPort) return '';

					cmd = 0x10;

					// 处理IP地址，按"."分割为4个数
					const ipParts = this.reportIp.split('.');
					if (ipParts.length !== 4) return '';

					const ipNumbers = ipParts.map(part => parseInt(part));
					if (ipNumbers.some(isNaN)) return '';

					// 处理端口号
					const port = parseInt(this.reportPort);
					if (isNaN(port)) return '';

					const portHigh = (port >> 8) & 0xFF; // 高位字节
					const portLow = port & 0xFF; // 低位字节

					// 组装数据部分
					if (port < 257) {
						// 端口号小于257时，只需要1个字节存储
						dataBytes = [...ipNumbers, portLow];
						dataLength = 5; // IP地址(4字节) + 端口(1字节) = 5字节
					} else {
						// 端口号大于等于257时，需要2个字节存储
						dataBytes = [...ipNumbers, portHigh, portLow];
						dataLength = 6; // IP地址(4字节) + 端口(2字节) = 6字节
					}
				} else if (this.paramType === '0x11') { // 采集频率和上报频率设置
					if (!this.collectFrequency || !this.reportFrequency) return '';

					const collectFreq = parseInt(this.collectFrequency);
					const reportFreq = parseInt(this.reportFrequency);

					if (isNaN(collectFreq) || isNaN(reportFreq)) return '';

					cmd = 0x11;
					dataBytes = [collectFreq, reportFreq];
					dataLength = 2; // 采集频率(1字节) + 上报频率(1字节) = 2字节
				} else if (this.paramType === '0x12') { // 报警阈值设置
					if (!this.highThreshold || !this.lowThreshold) return '';

					const highThresh = parseInt(this.highThreshold);
					const lowThresh = parseInt(this.lowThreshold);

					if (isNaN(highThresh) || isNaN(lowThresh)) return '';

					cmd = 0x12;
					dataBytes = [lowThresh, highThresh];
					dataLength = 2; // 高报阈值(1字节) + 低报阈值(1字节) = 2字节
				} else if (this.paramType === '0x19') { // 设置电池截止电压
					if (!this.batteryVoltage) return '';

					const voltage = parseInt(this.batteryVoltage);
					if (isNaN(voltage)) return '';

					// 电压已经是毫伏为单位，直接使用
					const voltageMillivolts = voltage;

					cmd = 0x19;
					dataBytes = [
						(voltageMillivolts >> 24) & 0xFF,
						(voltageMillivolts >> 16) & 0xFF,
						(voltageMillivolts >> 8) & 0xFF,
						voltageMillivolts & 0xFF
					];
					dataLength = 4; // DWORD = 4字节
				} else if (this.paramType === '0x1a') { // 设置高低报回调时间
					if (!this.callbackTime) return '';

					const time = parseInt(this.callbackTime);
					if (isNaN(time)) return '';

					cmd = 0x1a;
					dataBytes = [
						(time >> 8) & 0xFF, // 高字节
						time & 0xFF // 低字节
					];
					dataLength = 2; // WORD = 2字节
				} else if (this.paramType === '0x1c') { // 清零外液次数
					cmd = 0x1c;
					dataBytes = [];
					dataLength = 0; // 无数据
				} else if (this.paramType === '0x1e') { // 清零低液次数
					cmd = 0x1e;
					dataBytes = [];
					dataLength = 0; // 无数据
				} else if (this.paramType === '0x1f') { // 设置GPS坐标
					if (!this.gpsLatitude || !this.gpsLongitude) return '';

					const lat = parseFloat(this.gpsLatitude);
					const lng = parseFloat(this.gpsLongitude);

					if (isNaN(lat) || isNaN(lng)) return '';

					// GPS坐标转换为DWORD[2] (8字节)，假设以微度为单位存储
					// const latMicrodegrees = Math.round(lat * 1000000);
					// const lngMicrodegrees = Math.round(lng * 1000000);
					// 转成 IEEE754 32位浮点数存入 DWORD
					function floatToDwordArray(value) {
						const buf = new ArrayBuffer(4);
						new DataView(buf).setFloat32(0, value, true); // true 表示小端
						return new Uint32Array(buf)[0];
					}
					const latMicrodegrees = floatToDwordArray(lat);
					const lngMicrodegrees = floatToDwordArray(lng);

					cmd = 0x1f;
					dataBytes = [
						(latMicrodegrees >> 24) & 0xFF,
						(latMicrodegrees >> 16) & 0xFF,
						(latMicrodegrees >> 8) & 0xFF,
						latMicrodegrees & 0xFF,
						(lngMicrodegrees >> 24) & 0xFF,
						(lngMicrodegrees >> 16) & 0xFF,
						(lngMicrodegrees >> 8) & 0xFF,
						lngMicrodegrees & 0xFF
					];
					dataLength = 8; // DWORD[2] = 8字节
				} else if (this.paramType === '0x20') { // 设置水浸开关
					if (!this.waterSwitch) return '';

					const switchValue = parseInt(this.waterSwitch);
					if (isNaN(switchValue)) return '';

					cmd = 0x20;
					dataBytes = [switchValue];
					dataLength = 1; // BYTE = 1字节
				} else if (this.paramType === '0x21') { // 角度校准
					cmd = 0x21;
					dataBytes = [1]; // 发送校准指令，数据为1
					dataLength = 1; // BYTE = 1字节
				}

				// 计算CRC校验 - 命令 + 数据长度 + 数据的和取低位
				let crc = cmd;
				crc += (dataLength >> 8) & 0xFF;
				crc += dataLength & 0xFF;

				for (let i = 0; i < dataBytes.length; i++) {
					crc += dataBytes[i];
				}

				crc = crc & 0xFF; // 取低8位

				// 构建指令预览字符串
				let preview = 'A5FE';  // 帧头
				preview += cmd.toString(16).padStart(2, '0').toUpperCase();  // 命令
				preview += dataLength.toString(16).padStart(4, '0').toUpperCase();  // 数据长度
				dataBytes.forEach(byte => {
					preview += byte.toString(16).padStart(2, '0').toUpperCase();  // 数据
				});
				preview += crc.toString(16).padStart(2, '0').toUpperCase();  // CRC

				return preview;
			} catch (e) {
				console.error('生成指令预览失败:', e);
				return '';
			}
		}
	},
	onLoad(options) {
		// 获取从蓝牙连接页面传递过来的设备信息
		if (options.device) {
			try {
				this.currentDevice = JSON.parse(decodeURIComponent(options.device));
			} catch (e) {
				console.error('解析设备信息失败:', e);
			}
		}

		// 设置页面标题，显示当前设备名称
		if (this.currentDevice && this.currentDevice.name) {
			uni.setNavigationBarTitle({
				title: '指令下发 - ' + this.currentDevice.name
			})
		}

		// 从本地存储加载历史记录
		const history = uni.getStorageSync('commandHistory');
		if (history) {
			try {
				this.commandHistory = JSON.parse(history);
			} catch (e) {
				console.error('解析历史记录失败:', e);
			}
		}

		// 设置蓝牙监听
		this.setupBluetoothListener();
	},
	onUnload() {
		// 清理所有待处理的指令
		for (const [commandId, info] of this.pendingCommands.entries()) {
			clearTimeout(info.timeout);
		}
		this.pendingCommands.clear();
	},
	methods: {
		// 添加全局响应处理方法
		generateCommandId() {
			this.commandCounter++;
			return `cmd_${Date.now()}_${this.commandCounter}`;
		},
		registerPendingCommand(commandId, commandCode, callback, timeout = null) {
			const timeoutMs = timeout || this.globalResponseTimeout;
			const timeoutHandle = setTimeout(() => {
				if (this.pendingCommands.has(commandId)) {
					this.pendingCommands.delete(commandId);
					console.log(`⏰ 指令 ${commandId} 响应超时`);
					this.showLoading = false;
					this.resultData = {
						title: '指令超时',
						content: '设备响应超时，请检查设备连接',
						success: false
					};
					this.showResult = true;
					if (callback) {
						callback({
							success: false,
							error: 'timeout',
							message: '指令处理超时'
						});
					}
				}
			}, timeoutMs);
			this.pendingCommands.set(commandId, {
				commandCode: commandCode,
				callback: callback,
				timeout: timeoutHandle,
				timestamp: Date.now()
			});
			console.log(`📝 注册指令 ${commandId}: 代码=0x${commandCode.toString(16).toUpperCase()}`);
		},
		// 监听蓝牙特征值变化
		setupBluetoothListener() {
			uni.onBLECharacteristicValueChange((res) => {
				if (res.value) {
					// 尝试处理全局指令响应
					if (this.tryHandleGlobalCommandResponse(res.value)) {
						console.log('✅ 全局指令响应已处理');
						return;
					}
				}
			});
		},
		tryHandleGlobalCommandResponse(buffer) {
			try {
				const uint8Array = new Uint8Array(buffer);
				if (uint8Array.length < 6) {
					return false;
				}
				// 检查帧头是否为 A5 FE
				if (uint8Array[0] !== 0xA5 || uint8Array[1] !== 0xFE) {
					return false;
				}
				const commandCode = uint8Array[2];
				const dataLength = (uint8Array[3] << 8) | uint8Array[4];
				if (uint8Array.length < 6 + dataLength) {
					return false;
				}
				// 检查是否有待处理的指令匹配此响应
				let hasMatchingCommand = false;
				for (const [commandId, info] of this.pendingCommands.entries()) {
					if (info.commandCode === commandCode) {
						hasMatchingCommand = true;
						break;
					}
				}
				if (!hasMatchingCommand) {
					return false;
				}
				// 提取状态码（数据区的第一个字节）
				const statusCode = dataLength > 0 ? uint8Array[5] : 0x00;
				// 调用响应处理函数
				this.handleCommandResponse(commandCode, statusCode);
				return true;
			} catch (error) {
				console.error('全局响应处理异常:', error);
				return false;
			}
		},
		handleCommandResponse(commandCode, statusCode) {
			console.log(`📨 收到指令响应: 代码=0x${commandCode.toString(16).toUpperCase()}, 状态=0x${statusCode.toString(16).toUpperCase()}`);
			// 查找对应的待处理指令
			let matchedCommandId = null;
			let commandInfo = null;
			for (const [commandId, info] of this.pendingCommands.entries()) {
				if (info.commandCode === commandCode) {
					matchedCommandId = commandId;
					commandInfo = info;
					break;
				}
			}
			if (!matchedCommandId || !commandInfo) {
				console.log(`⚠️ 未找到匹配的待处理指令: 0x${commandCode.toString(16).toUpperCase()}`);
				return;
			}
			// 清除超时定时器并移除待处理指令
			clearTimeout(commandInfo.timeout);
			this.pendingCommands.delete(matchedCommandId);
			// 隐藏加载状态
			this.showLoading = false;
			// 根据状态码处理响应
			switch (statusCode) {
				case 0x00: // 成功
					this.resultData = {
						title: '设置成功',
						content: '指令执行成功，设备已完成相应设置',
						success: true
					};
					// 更新历史记录状态为成功
					this.updateCommandHistoryStatus(matchedCommandId, 'success');
					break;
				case 0x01: // 指令发送成功但设备端设置失败
					this.resultData = {
						title: '设置失败',
						content: '指令发送成功，但设备无法完成设置，请检查参数或设备状态',
						success: false
					};
					this.updateCommandHistoryStatus(matchedCommandId, 'device_failed');
					break;
				case 0x02: // 命令不支持
					this.resultData = {
						title: '命令不支持',
						content: '当前设备不支持此命令，请确认设备型号和固件版本',
						success: false
					};
					this.updateCommandHistoryStatus(matchedCommandId, 'not_supported');
					break;
				default: // 未知状态码
					this.resultData = {
						title: '未知响应',
						content: `收到未知状态码: 0x${statusCode.toString(16).toUpperCase()}`,
						success: false
					};
					this.updateCommandHistoryStatus(matchedCommandId, 'unknown_error');
					break;
			}
			this.showResult = true;
			if (commandInfo.callback) {
				commandInfo.callback({
					success: statusCode === 0x00,
					statusCode: statusCode,
					message: this.resultData.content
				});
			}
		},
		updateCommandHistoryStatus(commandId, status) {
			// 更新本地存储中的命令历史状态
			try {
				const history = uni.getStorageSync('commandHistory');
				if (history) {
					const commandHistory = JSON.parse(history);
					const record = commandHistory.find(r => r.commandId === commandId);
					if (record) {
						record.status = status;
						record.responseTime = this.formatDateTime(new Date());
						uni.setStorageSync('commandHistory', JSON.stringify(commandHistory));
					}
				}
			} catch (e) {
				console.error('更新命令历史状态失败:', e);
			}
		},
		resetForm() {
			this.paramType = '';
			this.paramTypeText = '';
			this.paramTypeIndex = 0;
			this.reportIp = '';
			this.reportPort = '';
			this.collectFrequency = '';
			this.reportFrequency = '';
			this.highThreshold = '';
			this.lowThreshold = '';

			// 重置新增字段
			this.batteryVoltage = '';
			this.callbackTime = '';
			this.gpsLatitude = '';
			this.gpsLongitude = '';
			this.waterSwitch = '';
			this.waterSwitchIndex = 0;
			this.waterSwitchText = '';

			// 重置地图相关状态
			this.selectedCoordinate = {
				latitude: '',
				longitude: ''
			};
			this.mapMarkers = [];
		},
		// 自定义toast方法替代u-toast
		showCustomToast(options) {
			this.toastMessage = options.message || '';
			this.toastType = options.type || 'default';
			this.showToast = true;

			// 自动关闭
			setTimeout(() => {
				this.showToast = false;
			}, 2000);

			// 为了兼容性，同时使用uni.showToast
			uni.showToast({
				title: options.message || '',
				icon: options.type === 'error' ? 'error' : (options.type === 'warning' ? 'none' : 'success'),
				duration: 2000
			});
		},
		submitCommand() {
			// 表单验证
			if (!this.paramType) {
				this.showCustomToast({
					message: '请选择参数类型',
					type: 'error'
				});
				return;
			}

			if (this.paramType === '0x10') {
				if (!this.reportIp) {
					this.showCustomToast({
						message: '请输入上报IP',
						type: 'error'
					});
					return;
				}

				if (!this.reportPort) {
					this.showCustomToast({
						message: '请输入上报端口',
						type: 'error'
					});
					return;
				}
			} else if (this.paramType === '0x11') {
				if (!this.collectFrequency) {
					this.showCustomToast({
						message: '请输入采集周期',
						type: 'error'
					});
					return;
				}

				const collectFreq = parseInt(this.collectFrequency);
				if (isNaN(collectFreq) || collectFreq < 1 || collectFreq > 43200) {
					this.showCustomToast({
						message: '采集周期范围应在1-43200分钟之间',
						type: 'error'
					});
					return;
				}

				if (!this.reportFrequency) {
					this.showCustomToast({
						message: '请输入上报周期',
						type: 'error'
					});
					return;
				}

				const reportFreq = parseInt(this.reportFrequency);
				if (isNaN(reportFreq) || reportFreq < 1 || reportFreq > 43200) {
					this.showCustomToast({
						message: '上报周期范围应在1-43200分钟之间',
						type: 'error'
					});
					return;
				}
			} else if (this.paramType === '0x12') {
				if (!this.highThreshold) {
					this.showCustomToast({
						message: '请输入高报阈值',
						type: 'error'
					});
					return;
				}

				if (!this.lowThreshold) {
					this.showCustomToast({
						message: '请输入低报阈值',
						type: 'error'
					});
					return;
				}
			} else if (this.paramType === '0x19') {
				if (!this.batteryVoltage) {
					this.showCustomToast({
						message: '请输入电池截止电压',
						type: 'error'
					});
					return;
				}

				const voltage = parseInt(this.batteryVoltage);
				if (isNaN(voltage) || voltage < 2700 || voltage > 3600) {
					this.showCustomToast({
						message: '电池电压范围应在2700mV-3600mV之间',
						type: 'error'
					});
					return;
				}
			} else if (this.paramType === '0x1a') {
				if (!this.callbackTime) {
					this.showCustomToast({
						message: '请输入高低报间隔时间',
						type: 'error'
					});
					return;
				}

				const time = parseInt(this.callbackTime);
				if (isNaN(time) || time < 1 || time > 43200) {
					this.showCustomToast({
						message: '高低报间隔时间范围应在1-43200分钟之间',
						type: 'error'
					});
					return;
				}
			} else if (this.paramType === '0x1f') {
				if (!this.gpsLatitude) {
					this.showCustomToast({
						message: '请输入GPS纬度',
						type: 'error'
					});
					return;
				}

				if (!this.gpsLongitude) {
					this.showCustomToast({
						message: '请输入GPS经度',
						type: 'error'
					});
					return;
				}
			} else if (this.paramType === '0x20') {
				if (!this.waterSwitch) {
					this.showCustomToast({
						message: '请选择水浸开关状态',
						type: 'error'
					});
					return;
				}
			}

			// 显示加载中
			this.showLoading = true;

			// 组装要发送的命令数据
			const commandData = {
				paramType: this.paramType,
				reportIp: this.reportIp,
				reportPort: this.reportPort,
				collectFrequency: this.collectFrequency,
				reportFrequency: this.reportFrequency,
				highThreshold: this.highThreshold,
				lowThreshold: this.lowThreshold,
				batteryVoltage: this.batteryVoltage,
				callbackTime: this.callbackTime,
				gpsLatitude: this.gpsLatitude,
				gpsLongitude: this.gpsLongitude,
				waterSwitch: this.waterSwitch,
			};

			console.log('准备发送指令:', commandData);

			// 获取存储的蓝牙写入服务和特征值ID
			const deviceId = this.currentDevice?.deviceId;
			const serviceId = uni.getStorageSync('writeServiceId');
			const characteristicId = uni.getStorageSync('writeCharacteristicId');

			if (!deviceId || !serviceId || !characteristicId) {
				this.showLoading = false;
				this.resultData = {
					title: '下发失败',
					content: '蓝牙特征值未找到，请重新连接设备',
					success: false
				};
				this.showResult = true;
				return;
			}

			// 构建发送数据
			try {
				// 根据蓝牙协议格式构建数据
				/*
				帧头      命令   数据长度     数据       CRC     
				2Byte    1Byte   2Byte      n Byte     1Byte  
				0x5AFE    xx      xxxx        xx         xx    
				*/
				let cmd = 0;
				let dataBytes = [];
				let dataLength = 0;

				// 确定命令类型和数据
				if (this.paramType === '0x10') { // IP端口配置
					cmd = 0x10;

					// 处理IP地址，按"."分割为4个数
					const ipParts = this.reportIp.split('.').map(part => parseInt(part));

					// 处理端口号
					const port = parseInt(this.reportPort);

					const portHigh = (port >> 8) & 0xFF; // 高位字节
					const portLow = port & 0xFF; // 低位字节

					// 组装数据部分
					if (port < 257) {
						// 端口号小于257时，只需要1个字节存储
						dataBytes = [...ipParts, portLow];
						dataLength = 5; // IP地址(4字节) + 端口(1字节) = 5字节
					} else {
						// 端口号大于等于257时，需要2个字节存储
						dataBytes = [...ipParts, portHigh, portLow];
						dataLength = 6; // IP地址(4字节) + 端口(2字节) = 6字节
					}
				} else if (this.paramType === '0x11') { // 采集频率和上报频率设置
					cmd = 0x11;
					dataBytes = [parseInt(this.collectFrequency), parseInt(this.reportFrequency)];
					dataLength = 2; // 采集频率(1字节) + 上报频率(1字节) = 2字节
				} else if (this.paramType === '0x12') { // 报警阈值设置
					cmd = 0x12;
					dataBytes = [parseInt(this.highThreshold), parseInt(this.lowThreshold)];
					dataLength = 2; // 高报阈值(1字节) + 低报阈值(1字节) = 2字节
				} else if (this.paramType === '0x19') { // 设置电池截止电压
					cmd = 0x19;
					const voltage = parseInt(this.batteryVoltage);
					const voltageMillivolts = voltage;

					dataBytes = [
						(voltageMillivolts >> 24) & 0xFF,
						(voltageMillivolts >> 16) & 0xFF,
						(voltageMillivolts >> 8) & 0xFF,
						voltageMillivolts & 0xFF
					];
					dataLength = 4; // DWORD = 4字节
				} else if (this.paramType === '0x1a') { // 设置高低报回调时间
					cmd = 0x1a;
					const time = parseInt(this.callbackTime);

					dataBytes = [
						(time >> 8) & 0xFF, // 高字节
						time & 0xFF // 低字节
					];
					dataLength = 2; // WORD = 2字节
				} else if (this.paramType === '0x1c') { // 清零外液次数
					cmd = 0x1c;
					dataBytes = [];
					dataLength = 0; // 无数据
				} else if (this.paramType === '0x1e') { // 清零低液次数
					cmd = 0x1e;
					dataBytes = [];
					dataLength = 0; // 无数据
				} else if (this.paramType === '0x1f') { // 设置GPS坐标
					cmd = 0x1f;
					const lat = parseFloat(this.gpsLatitude);
					const lng = parseFloat(this.gpsLongitude);

					// GPS坐标转换为DWORD[2] (8字节)，以微度为单位存储
					const latMicrodegrees = Math.round(lat * 1000000);
					const lngMicrodegrees = Math.round(lng * 1000000);

					dataBytes = [
						(latMicrodegrees >> 24) & 0xFF,
						(latMicrodegrees >> 16) & 0xFF,
						(latMicrodegrees >> 8) & 0xFF,
						latMicrodegrees & 0xFF,
						(lngMicrodegrees >> 24) & 0xFF,
						(lngMicrodegrees >> 16) & 0xFF,
						(lngMicrodegrees >> 8) & 0xFF,
						lngMicrodegrees & 0xFF
					];
					dataLength = 8; // DWORD[2] = 8字节
				} else if (this.paramType === '0x20') { // 设置水浸开关
					cmd = 0x20;
					const switchValue = parseInt(this.waterSwitch);

					dataBytes = [switchValue];
					dataLength = 1; // BYTE = 1字节
				} else if (this.paramType === '0x21') { // 角度校准
					cmd = 0x21;
					dataBytes = [1]; // 发送校准指令，数据为1
					dataLength = 1; // BYTE = 1字节
				}

				// 计算CRC校验 - 命令 + 数据长度 + 数据的和取低位
				let crc = cmd;
				crc += (dataLength >> 8) & 0xFF;
				crc += dataLength & 0xFF;

				for (let i = 0; i < dataBytes.length; i++) {
					crc += dataBytes[i];
				}

				crc = crc & 0xFF; // 取低8位

				// 构建完整的蓝牙指令
				// 帧头(2字节) + 命令(1字节) + 数据长度(2字节) + 数据(n字节) + CRC(1字节)
				const totalLength = 2 + 1 + 2 + dataLength + 1;
				const buffer = new ArrayBuffer(totalLength);
				const dataView = new DataView(buffer);

				// 填充帧头 (0xA5FE)
				dataView.setUint8(0, 0xA5);
				dataView.setUint8(1, 0xFE);

				// 填充命令
				dataView.setUint8(2, cmd);

				// 填充数据长度 (大端序)
				dataView.setUint16(3, dataLength, false);

				// 填充数据
				for (let i = 0; i < dataBytes.length; i++) {
					dataView.setUint8(5 + i, dataBytes[i]);
				}

				// 填充CRC校验
				dataView.setUint8(totalLength - 1, crc);

				// 生成指令ID并注册到全局响应处理机制
				const commandId = this.generateCommandId();
				// 注册指令响应处理
				this.registerPendingCommand(commandId, cmd, (result) => {
					console.log('指令响应处理完成:', result);
				});

				// 记录日志 - 指令预览
				const commandHexString = this.arrayBufferToHexString(buffer);
				console.log('发送的蓝牙指令:', commandHexString);

				// 保存到历史记录（添加commandId）
				const record = {
					id: Date.now(),
					commandId: commandId, // 添加这个字段用于后续状态更新
					paramType: this.paramTypeText,
					reportIp: this.reportIp,
					reportPort: this.reportPort,
					collectFrequency: this.collectFrequency,
					reportFrequency: this.reportFrequency,
					highThreshold: this.highThreshold,
					lowThreshold: this.lowThreshold,
					batteryVoltage: this.batteryVoltage,
					callbackTime: this.callbackTime,
					gpsLatitude: this.gpsLatitude,
					gpsLongitude: this.gpsLongitude,
					waterSwitch: this.waterSwitch,
					command: commandHexString,
					timestamp: this.formatDateTime(new Date()),
					status: 'pending' // 初始状态为等待响应
				};

				this.commandHistory.push(record);
				uni.setStorageSync('commandHistory', JSON.stringify(this.commandHistory));

				// 写入数据到蓝牙设备
				uni.writeBLECharacteristicValue({
					deviceId: deviceId,
					serviceId: serviceId,
					characteristicId: characteristicId,
					value: buffer,
					success: (res) => {
						console.log(`✅ 指令 [${commandId}] 发送成功，等待设备响应...`);
						// 不再立即显示成功，等待设备响应
					},
					fail: (err) => {
						console.error(`❌ 指令 [${commandId}] 发送失败:`, err);
						// 移除待处理指令
						if (this.pendingCommands.has(commandId)) {
							const commandInfo = this.pendingCommands.get(commandId);
							clearTimeout(commandInfo.timeout);
							this.pendingCommands.delete(commandId);
						}
						this.showLoading = false;
						this.resultData = {
							title: '下发失败',
							content: '指令发送失败，请检查设备连接或参数后重试。错误：' + (err.errMsg || '未知错误'),
							success: false
						};
						this.showResult = true;
					}
				});
			} catch (error) {
				console.error('准备发送数据时出错:', error);

				this.showLoading = false;

				// 指令发送失败
				this.resultData = {
					title: '下发失败',
					content: '指令数据准备失败：' + error.message,
					success: false
				};

				this.showResult = true;
			}
		},
		// 辅助方法：将ArrayBuffer转换为16进制字符串（用于调试）
		arrayBufferToHexString(buffer) {
			const uint8Array = new Uint8Array(buffer);
			return Array.from(uint8Array)
				.map(byte => byte.toString(16).padStart(2, '0').toUpperCase())
				.join(' ');
		},
		handleResultConfirm() {
			this.showResult = false;
			// 如果成功，可以考虑返回到上一页
			if (this.resultData.success) {
				// uni.navigateBack();
			}
		},

		paramTypeChange(e) {
			const index = e.detail.value;
			this.paramTypeIndex = index;
			this.paramType = this.paramTypeArray[index].value;
			this.paramTypeText = this.paramTypeArray[index].label;
		},
		waterSwitchChange(e) {
			const index = e.detail.value;
			this.waterSwitchIndex = index;
			this.waterSwitch = this.waterSwitchArray[index].value;
			this.waterSwitchText = this.waterSwitchArray[index].label;
		},
		formatDateTime(date) {
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			const seconds = String(date.getSeconds()).padStart(2, '0');
			return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
		},

		// GPS地图相关方法
		openMapPicker() {
			// 如果已有坐标，则以当前坐标为中心
			if (this.gpsLatitude && this.gpsLongitude) {
				const lat = parseFloat(this.gpsLatitude);
				const lng = parseFloat(this.gpsLongitude);
				if (!isNaN(lat) && !isNaN(lng)) {
					this.mapCenter.latitude = lat;
					this.mapCenter.longitude = lng;
					this.selectedCoordinate.latitude = lat;
					this.selectedCoordinate.longitude = lng;
					this.updateMapMarkers();
				}
			} else {
				// 尝试获取当前位置
				this.getCurrentLocation();
			}
			this.showMapPicker = true;
		},

		closeMapPicker() {
			this.showMapPicker = false;
			this.selectedCoordinate = {
				latitude: '',
				longitude: ''
			};
			this.mapMarkers = [];
		},

		getCurrentLocation() {
			uni.getLocation({
				type: 'gcj02',
				success: (res) => {
					this.mapCenter.latitude = res.latitude;
					this.mapCenter.longitude = res.longitude;
				},
				fail: (err) => {
					console.log('获取位置失败:', err);
					// 使用默认位置（北京）
				}
			});
		},

		onMapTap(e) {
			const { latitude, longitude } = e.detail;
			this.selectedCoordinate.latitude = latitude;
			this.selectedCoordinate.longitude = longitude;
			this.updateMapMarkers();
		},

		onMarkerTap(e) {
			console.log('标记点击:', e);
		},

		updateMapMarkers() {
			if (this.selectedCoordinate.latitude && this.selectedCoordinate.longitude) {
				this.mapMarkers = [{
					id: 1,
					latitude: this.selectedCoordinate.latitude,
					longitude: this.selectedCoordinate.longitude,
					iconPath: '/static/images/marker.png',
					width: 30,
					height: 30,
					callout: {
						content: `${this.selectedCoordinate.latitude.toFixed(6)}, ${this.selectedCoordinate.longitude.toFixed(6)}`,
						color: '#333',
						fontSize: 12,
						borderRadius: 4,
						bgColor: '#fff',
						padding: 5,
						display: 'ALWAYS'
					}
				}];
			}
		},

		confirmCoordinate() {
			if (this.selectedCoordinate.latitude && this.selectedCoordinate.longitude) {
				this.gpsLatitude = this.selectedCoordinate.latitude.toFixed(6);
				this.gpsLongitude = this.selectedCoordinate.longitude.toFixed(6);
				this.closeMapPicker();
				this.showCustomToast({
					message: '坐标选择成功',
					type: 'success'
				});
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.command-page {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.container {
	padding: 30rpx;
}

.form-card {
	background-color: #ffffff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.section-title {
	font-size: 28rpx;
	color: #666666;
	margin: 30rpx 0 20rpx;
	padding-left: 20rpx;
	position: relative;

	&::before {
		content: '';
		position: absolute;
		left: 0;
		top: 50%;
		transform: translateY(-50%);
		width: 6rpx;
		height: 28rpx;
		background-color: #47afff;
		border-radius: 3rpx;
	}
}

.form-group {
	margin-bottom: 30rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.label {
	font-size: 28rpx;
	color: #333333;
	margin-bottom: 16rpx;

	&.required::before {
		content: '*';
		color: #ff3b30;
		margin-right: 8rpx;
	}
}

.form-input {
	width: 100%;
	height: 88rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 16rpx;
	padding: 0 30rpx;
	font-size: 28rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	box-sizing: border-box;

	&.disabled {
		background-color: #f5f5f5;
		color: #999999;
	}
}

.command-preview {
	width: 100%;
	min-height: 88rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 16rpx;
	padding: 20rpx 30rpx;
	font-size: 28rpx;
	font-family: monospace;
	background-color: #f9f9f9;
	color: #333;
	word-break: break-all;
	display: flex;
	align-items: center;
}

.form-actions {
	display: flex;
	justify-content: space-between;
	margin-top: 60rpx;
	gap: 30rpx;
}

.btn {
	flex: 1;
	height: 88rpx;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	font-weight: 500;
}

.btn-primary {
	background-color: #47afff;
	color: white;
}

.btn-secondary {
	background-color: #f5f5f5;
	color: #666666;
	border: 2rpx solid #e0e0e0;
}

/* 自定义modal样式 */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 9999;
}

.modal-content {
	background-color: #ffffff;
	border-radius: 20rpx;
	padding: 40rpx;
	width: 560rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.loading-modal {
	width: 240rpx;
	height: 240rpx;
}

.loading-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	border: 6rpx solid #f3f3f3;
	border-top: 6rpx solid #47afff;
	animation: spin 1s linear infinite;
	margin-bottom: 30rpx;
}

.result-icon {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 60rpx;
	font-weight: bold;
	margin-bottom: 30rpx;

	&.success {
		background-color: #e6f9eb;
		color: #4cd964;
	}

	&.error {
		background-color: #ffebeb;
		color: #ff3b30;
	}
}

.result-title {
	font-size: 36rpx;
	font-weight: bold;
	margin-bottom: 20rpx;
}

.result-message {
	font-size: 28rpx;
	color: #666666;
	text-align: center;
	margin-bottom: 40rpx;
}

.confirm-btn {
	width: 100%;
}

/* 自定义toast样式 */
.toast-container {
	position: fixed;
	bottom: 100rpx;
	left: 50%;
	transform: translateX(-50%);
	z-index: 9999;
}

.toast-message {
	padding: 20rpx 40rpx;
	background-color: rgba(0, 0, 0, 0.7);
	color: #ffffff;
	border-radius: 10rpx;
	font-size: 28rpx;

	&.error {
		background-color: rgba(255, 59, 48, 0.9);
	}

	&.warning {
		background-color: rgba(255, 204, 0, 0.9);
	}
}

.picker-arrow {
	color: #999;
	font-size: 32rpx;
}

.form-message {
	padding: 30rpx;
	background-color: #f5f5f5;
	border-radius: 16rpx;
	text-align: center;
	color: #666666;
}

.form-hint {
	font-size: 24rpx;
	color: #999999;
	margin-top: 12rpx;
	padding-left: 8rpx;
}

/* 地图拾取按钮样式 */
.btn-map-picker {
	width: 100%;
	height: 88rpx;
	background-color: #f8f9fa;
	border: 2rpx solid #e0e0e0;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 16rpx;
	font-size: 28rpx;
	color: #47afff;
	margin-bottom: 16rpx;
}

.map-icon {
	font-size: 32rpx;
}

/* 地图选择器弹窗样式 */
.map-picker-modal {
	width: 90vw;
	height: auto;
	max-height: 70vh;
	max-width: 700rpx;
	padding: 0;
	border-radius: 20rpx;
	overflow: hidden;
	display: flex;
	flex-direction: column;
	position: relative;
}

.map-picker-close {
	position: absolute;
	top: 20rpx;
	right: 20rpx;
	z-index: 1000;
	font-size: 28rpx;
	color: #fff;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	background-color: rgba(0, 0, 0, 0.6);
	backdrop-filter: blur(10rpx);
}

.map-container {
	height: 400rpx;
	flex-shrink: 0;
	width: 100%;
}

.gps-map {
	width: 100%;
	height: 100%;
}

.coordinate-display {
	padding: 24rpx 30rpx;
	background-color: #f8f9fa;
	border-top: 2rpx solid #e0e0e0;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
	flex-shrink: 0;
}

.coordinate-text {
	font-size: 26rpx;
	color: #333;
	font-family: monospace;
}

.map-picker-actions {
	display: flex;
	gap: 20rpx;
	padding: 24rpx 30rpx;
	background-color: #fff;
	flex-shrink: 0;
}

/* Apple风格按钮 */
.map-btn {
	flex: 1;
	height: 88rpx;
	border-radius: 12rpx;
	border: none;
	font-size: 32rpx;
	font-weight: 500;
	display: flex;
	align-items: center;
	justify-content: center;
	white-space: nowrap;
	transition: all 0.2s ease;
}

.map-btn-cancel {
	background-color: #f2f2f7;
	color: #007aff;
}

.map-btn-cancel:active {
	background-color: #e5e5ea;
}

.map-btn-confirm {
	background-color: #007aff;
	color: #ffffff;
}

.map-btn-confirm:active {
	background-color: #0056cc;
}

.map-btn-confirm:disabled {
	background-color: #c7c7cc;
	color: #ffffff;
}
</style>