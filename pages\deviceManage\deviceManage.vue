<template>
	<!-- 设备管理 -->
	<view class="deviceManage">
		<!-- 查询筛选 -->
		<view class="queryView">
			<view class="query_a">
				<view class="query_a_m" v-if='!showQuerySheet'>
					<text v-for="(item,index) in modelQuery.queryForm" :key="index">
						{{item}}
					</text>
				</view>
			</view>
			<u-button type="primary" :customStyle="primaryBtnCss" text="筛选" @click="showQuerySheet = true"></u-button>
		</view>
		<u-action-sheet :show="showQuerySheet" title="筛选" @close="showQuerySheet = false">
			<view class="form_view rightForm">
				<u--form labelPosition="left" :model="modelQuery" ref="queryForm" labelWidth="100"
					:labelStyle="labelStyle">
					<u-form-item label="设备类型" prop="queryForm.equipTypeName" borderBottom @click="typeShow = true;">
						<u--input v-model="modelQuery.queryForm.equipTypeName" disabled disabledColor="#ffffff"
							inputAlign="right" border="none" placeholder="请选择设备类型"></u--input>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>
					<u-form-item label="设备编号" prop="queryForm.code" borderBottom>
						<u--input v-model="modelQuery.queryForm.code" maxlength="30" border="none" placeholder="请输入设备编号"
							inputAlign="right"></u--input>
					</u-form-item>
				</u--form>
				<u-picker itemHeight="60" :show="typeShow" :columns="typeColumns" keyName="name" @confirm="typeConfirm"
					@cancel="typeCancel"></u-picker>
				<view class="twoBtn">
					<u-button type="info" text="重置" :customStyle="otherBtnCss" @click="resetQuery"></u-button>
					<u-button type="primary" text="确定" :customStyle="otherBtnCss" @click="saveQuery"></u-button>
				</view>
			</view>
		</u-action-sheet>
		<view class="sumDataView">
			<view>设备总数 <text class="allNum"> {{sumData.totalCount}}</text></view>
			<view>正常 <text class="zxNum"> {{sumData.normalCount}}</text></view>
			<view>故障 <text class="bjNum"> {{sumData.abnormalCount}}</text></view>
		</view>
		<view class="listView">
			<view class="listModule" v-for="(item,index) in deviceList" :key='index'>
				<view class="userHead">
					
					<image src="@/static/homePage/fmjIcon.png" mode="" v-if="item.equipType=='001'"></image>
					<image src="@/static/homePage/dmsIcon.png" mode="" v-else-if="item.equipType=='002'"></image>
					<image src="@/static/homePage/jyIcon.png" mode="" v-else-if="item.equipType=='003'"></image>
					<image src="@/static/homePage/gsIcon.png" mode="" v-else-if="item.equipType=='004'"></image>
				</view>
				<view class="dataNumView">
					<!-- <text class="dataNum">20</text><text>%VOL</text> -->
					<text class="dataNum">{{item.chromaStr}}</text>
				</view>
				<view class="userContent">
					<view class="titleHead">
						<text>设备-{{item.equipType}}</text>
						<view class="operBtn">
							<text>编号：{{item.code}}</text>
						</view>
					</view>
					<view class="">
						<text>状态：{{item.status=='00'?'正常':item.status=='01'?'光路故障':item.status=='02'?'温度获取异常':item.status=='03'?'温控故障':'--'}}</text>
					</view>
					<view class="">
						<text>设备类型：{{item.name}}</text>
					</view>
					<view class="">
						<text>地址：{{item.address||'--'}}</text>
					</view>
				</view>
			</view>
		</view>
		<u-modal :content="delContent" title="提示" confirmText="确定" showCancelButton :show="delShowModal"
			@confirm="delConfirm" @cancel="delShowModal=false" style="text-align: center;"></u-modal>
	</view>
</template>

<script>
	import {
		deviceInfoList,
		equipStatistics
	} from "@/network/api.js"
	export default {
		data() {
			return {
				showQuerySheet: false,
				primaryBtnCss: {
					width: '100rpx',
					height: '58rpx',
					// background: '#0165FC',
					borderRadius: '60rpx',
					fontSize: '28rpx',
					// color: '#FFFFFF',
					lineHeight: '28rpx',
					margin: '0'
				},
				otherBtnCss: {
					width: '260rpx',
					borderRadius: '60rpx',
					fontSize: '28rpx',
					fontFamily: 'Microsoft YaHei UI-Regular, Microsoft YaHei UI',
					lineHeight: '28rpx'
				},
				sumData: {
					abnormalCount: 0,
					normalCount: 0,
					totalCount: 0
				},
				modelQuery: {
					queryForm: {
						equipTypeName: '',
						code: ''
					},
				},
				typeShow: false,
				typeColumns: [
					[{
						name: '家用型可燃气体探测器',
							id: '001'
						},
						{
							name: '地下空间燃气泄漏监测仪',
							id: '002'
						},
						{
							name: '工商业可燃气体探测器',
							id: '003'
						},
						{
							name: '地埋式燃气泄漏监测仪',
							id: '004'
						}
					]
				],
				deviceList: [],
				equipType: '',
				delContent: '是否删除',
				delShowModal: false,
				total: null, //总条数
				pageNum: 1, //第几页
				pageSize: 15 //显示多少条
			};
		},
		onLoad(opt) { // 初始化
			// this.init(opt)
			this.getDevicList()
			this.getDevicNum()
		},
		methods: {
			init(opt) { // 初始化数据
				// console.log(JSON.parse(opt.params));
			},
			getDevicNum() {
				let query = {
					token: uni.getStorageSync('token'),
				}
				equipStatistics(query).then(res => {
						this.sumData = res.data;
					})
					.catch((err) => {})
			},
			getDevicList() {
				let query = {
					userType: uni.getStorageSync('userType'),
					customerId: uni.getStorageSync('userId'),
					token: uni.getStorageSync('token'),
					equipType: this.equipType,
					code: this.modelQuery.queryForm.code,
					pageNum: this.pageNum,
					pageSize: this.pageSize
					// sceneId: id
				}
				deviceInfoList(query).then(res => {
						this.deviceList = [...this.deviceList, ...res.data.list]
						this.total = res.data.total;
					})
					.catch((err) => {})
			},
			delConfirm() {
				this.delShowModal = false
			},
			resetQuery() {
				this.modelQuery.queryForm.equipTypeName = '';
				this.equipType = '';
				this.modelQuery.queryForm.code = '';
				this.pageNum = 1;
				this.deviceList = [];
				this.getDevicList();
				this.showQuerySheet = false;
			},
			saveQuery() {
				this.showQuerySheet = false;
				this.pageNum = 1;
				this.deviceList = [];
				this.getDevicList();
			},
			typeConfirm(e) {
				this.modelQuery.queryForm.equipTypeName = e.value[0].name
				this.equipType = e.value[0].id
				this.typeShow = false;
			},
			typeCancel() {
				this.typeShow = false;
			},
		},
		onReachBottom() { //触底事件
			if (this.pageNum * this.pageSize >= this.total) {
				uni.showToast({
					title: '没有更多数据了',
					icon: 'none',
					duration: 1000
				});
			} else {
				if (this.pageNum <= this.pageNum - 1) {
					setTimeout(() => {
						uni.hideLoading()
					}, 500)
				} else {
					uni.showLoading({
						title: '加载中'
					});
					this.pageNum++
					this.getDevicList()
				}
				setTimeout(() => {
					uni.hideLoading()
				}, 500)
			}
		},
		onReady() {},
	};
</script>
<style>
	page {
		background-color: #F6F6F6;
	}
</style>
<style lang="scss" scoped>
	.deviceManage {
		.queryView {
			width: 92%;
			padding: 10rpx 4%;
			background-color: #ffffff;
			margin: 10rpx 0;
			position: fixed;
			top: 0;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.query_a {
				.query_a_m {
					display: flex;

					text {
						padding: 0 16rpx;
						font-size: 26rpx;
						color: #87898f;
						font-weight: 600;
						display: block;
					}
				}
			}
		}

		.twoBtn {
			margin-top: 30rpx;
			display: flex;
		}

		.sumDataView {
			display: flex;
			align-items: center;
			justify-content: space-around;
			background-color: #ffffff;
			padding: 20rpx 0;
			margin-top: 95rpx;
			font-size: 28rpx;
			color: #3D3D3D;
			line-height: 44rpx;

			text {
				margin-left: 10rpx;
				font-weight: 600;
			}

			.allNum {
				color: #676767;
			}

			.zxNum {
				color: #5AC725;
			}

			.bjNum {
				color: #FF0000;
			}
		}

		.listView {
			margin-top: 10rpx;

			.listModule {
				background-color: #ffffff;
				margin-bottom: 12rpx;
				padding: 26rpx 40rpx;
				font-size: 24rpx;
				color: #3D3D3D;
				line-height: 38rpx;
				display: flex;
				align-items: center;
				position: relative;

				.userHead {
					margin-right: 44rpx;

					image {
						width: 86rpx;
						height: 86rpx;
					}
				}

				.dataNumView {
					position: absolute;
					right: 64rpx;
					top: 96rpx;
					color: #3D3D3D;

					.dataNum {
						font-size: 36rpx;
						color: #FF0000;
						line-height: 36rpx;
					}
				}

				.userContent {
					flex: 1;
				}

				.titleHead {
					font-size: 34rpx;
					color: #409EFF;
					line-height: 56rpx;
					margin-bottom: 6rpx;
					display: flex;
					justify-content: space-between;
					align-items: center;

					.operBtn {
						font-size: 24rpx;
						color: #3D3D3D;
						line-height: 36rpx;
					}
				}

				.devAllView {
					display: flex;
					flex-wrap: wrap;
					justify-content: space-between;
					margin: 10rpx 0;

					text {
						width: 49%;
						background: #F3F3F3;
						border-radius: 10rpx;
						text-align: center;
						font-size: 24rpx;
						color: #3D3D3D;
						line-height: 50rpx;
						margin: 10rpx 0;
					}
				}
			}
		}
	}
</style>