<template>
	<!-- 设备编辑 -->
	<view class="editDeviceInform">
		<view class="addHead">
			
			<image src="@/static/product/fajing.png" mode="" v-if="modelAdd.userInfo.equipType=='002'"></image>
			<image src="@/static/homePage/dmsIcon.png" mode="" v-else-if="modelAdd.userInfo.equipType=='004'"></image>
			<image  src="@/static/product/home.png" mode="" v-else-if="modelAdd.userInfo.equipType=='001'"></image>
			<image src="@/static/product/gongshang.png" mode="" v-else-if="modelAdd.userInfo.equipType=='003'"></image>
		</view>
		<view class="form_view rightForm">
			<u--form labelPosition="left" :model="modelAdd" :rules="addRules" ref="addForm" :labelWidth="100">
				<u-form-item label="所属场景" prop="userInfo.sceneName" borderBottom :required="true"
					@click="sceneShow = true;">
					<u--input v-model="modelAdd.userInfo.sceneName" disabled disabledColor="#ffffff" inputAlign="right"
						border="none" placeholder="请选择所属场景"></u--input>
					<u-icon slot="right" name="arrow-right"></u-icon>
				</u-form-item>
				<u-form-item label="设备名称" prop="userInfo.name" borderBottom :required="true">
					<u--input v-model="modelAdd.userInfo.name" maxlength="15" border="none" placeholder="请输入设备名称" 
						inputAlign="right"></u--input>
				</u-form-item>
				<u-form-item label="设备编号" prop="userInfo.code" borderBottom>
					<u--input v-model="modelAdd.userInfo.code" maxlength="30" border="none" placeholder="请输入设备编号" :readonly="true"
						inputAlign="right"></u--input>
				</u-form-item>
				<!-- <u-form-item label="地址" prop="userInfo.address" borderBottom>
					<u--input v-model="modelAdd.userInfo.address" maxlength="30" border="none" placeholder="请输入地址"
						inputAlign="right"></u--input>
					<u-icon slot="right" name="map"></u-icon>
				</u-form-item> -->
				<u-form-item label="省" prop="userInfo.province" borderBottom>
					<u--input v-model="modelAdd.userInfo.province" maxlength="30" border="none"
						placeholder="请输入省" inputAlign="right"></u--input>
				</u-form-item>
				<u-form-item label="市" prop="userInfo.city" borderBottom>
					<u--input v-model="modelAdd.userInfo.city" maxlength="30" border="none"
						placeholder="请输入市" inputAlign="right"></u--input>
				</u-form-item>
				<u-form-item label="区" prop="userInfo.area" borderBottom>
					<u--input v-model="modelAdd.userInfo.area" maxlength="30" border="none"
						placeholder="请输入区" inputAlign="right"></u--input>
				</u-form-item>
				<u-form-item label="街道" prop="userInfo.road" borderBottom>
					<u--input v-model="modelAdd.userInfo.road" maxlength="30" border="none"
						placeholder="请输入街道" inputAlign="right"></u--input>
				</u-form-item>
				<u-form-item label="小区" prop="userInfo.neighbourhood" borderBottom>
					<u--input v-model="modelAdd.userInfo.neighbourhood" maxlength="30" border="none"
						placeholder="请输入小区" inputAlign="right"></u--input>
				</u-form-item>
				<u-form-item label="门牌" prop="userInfo.room" borderBottom>
					<u--input v-model="modelAdd.userInfo.room" maxlength="30" border="none"
						placeholder="请输入门牌" inputAlign="right"></u--input>
				</u-form-item>
				<u-form-item>
					<u-button type="primary" text="保存" :disabled="disabled" :customStyle="primaryBtnCss"
						@click="submit"></u-button>
				</u-form-item>	
			</u--form>
			<u-picker itemHeight="60" :show="sceneShow" :columns="sceneColumns" keyName="sceneName" @confirm="sceneConfirm"
				@cancel="sceneCancel"></u-picker>
			<!-- <view class="btnXFView"> -->
			<!-- <u-form-item>
				<u-button type="primary" text="编辑" :disabled="disabled" :customStyle="primaryBtnCss"
					@click="submit"></u-button>
			</u-form-item>	 -->	
			<!-- </view> -->
			<u-modal title="提示" :content="content" :show="showModal" @confirm="modalConfirm"></u-modal>
		</view>
	</view>
</template>

<script>
	import {
		sceneListAll,
		updateDevice
	} from "@/network/api.js"
	export default {
		data() {
			return {
				content: '当前无所属场景数据，请前往添加',
				showModal: false,
				tabsLocation: 0,
				formEndArry: [],
				primaryBtnCss: {
					width: '574rpx',
					height: '88rpx',
					background: '#0165FC',
					boxShadow: '0rpx 4rpx 24rpx 0rpx rgba(54,150,255,0.4)',
					borderRadius: '200rpx',
					fontSize: '28rpx',
					fontFamily: 'Microsoft YaHei UI-Regular, Microsoft YaHei UI',
					color: '#FFFFFF',
					lineHeight: '28rpx'
				},
				disabled: false,
				labelStyle: {
					// fontSize: '28rpx',
					// fontFamily: 'Microsoft YaHei UI-Regular, Microsoft YaHei UI',
					// lineHeight: '28rpx'
				},
				sceneShow: false,
				sceneColumns: [],
				modelAdd: {
					userInfo: {
						sceneName: '',
						name: '',
						code: '',
						address: '',
						province: '',  //省
						city: '',  //市
						area: '',  //区
						road: '',  //街道
						neighbourhood: '',  //小区
						room: '',  //门牌
					},
				},
				addRules: {
					'userInfo.sceneName': {
						type: 'string',
						required: true,
						message: '请选择所属场景',
						trigger: ['blur', 'change']
					},
					'userInfo.name': {
						type: 'string',
						required: true,
						message: '请输入设备名称',
						trigger: ['blur', 'change']
					},
					'userInfo.code': {
						type: 'string',
						required: false,
						message: '请输入设备编号',
						trigger: ['blur', 'change']
					},
					'userInfo.address': {
						type: 'string',
						required: false,
						message: '请输入地址',
						trigger: ['blur', 'change']
					}
				}
			};
		},
		onLoad(opt) { // 初始化
			this.getConfigsList();
			this.init(opt)
		},
		methods: {
			getConfigsList() {
				let query = {
					userType: uni.getStorageSync('userType'), // 用户类型
					id: uni.getStorageSync('userId') // 用户ID
				}
				sceneListAll(query).then(res => {
						this.sceneColumns = [];
						this.sceneColumns.push(res.data);
					})
					.catch((err) => {})
			},
			init(opt) { // 初始化数据
				console.log(JSON.parse(opt.params));
				let optData = JSON.parse(opt.params);
				console.log(optData,'optData');
				this.modelAdd.userInfo = optData;
			},
			sceneConfirm(e) {
				console.log('eeee', e)
				this.modelAdd.userInfo.sceneName = e.value[0].sceneName
				this.modelAdd.userInfo.sceneId = e.value[0].id
				this.$refs.addForm.validateField('userInfo.sceneName')
				this.sceneShow = false;
			},
			sceneCancel() {
				this.sceneShow = false;
			},

			submit() {
				this.$refs.addForm.validate().then(res => {
					// uni.$u.toast('校验通过')
					// this.showModal = true;
					this.modalConfirm()
				}).catch(errors => {
					// uni.$u.toast('校验失败')
				})
			},
			modalConfirm() {
				// this.showModal = false
				console.log('confirm');
				uni.showLoading({
					title: '请求中..'
				})
				let form = {
					customerId: uni.getStorageSync('userId'),
					token: uni.getStorageSync('token'),
					code: this.modelAdd.userInfo.code,
					name: this.modelAdd.userInfo.name,
					sceneId: this.modelAdd.userInfo.sceneId,
					
					province: this.modelAdd.userInfo.province,  //省
					city: this.modelAdd.userInfo.city,  //市
					area: this.modelAdd.userInfo.area,  //区
					road: this.modelAdd.userInfo.road,  //街道
					neighbourhood: this.modelAdd.userInfo.neighbourhood,  //小区
					room: this.modelAdd.userInfo.room  //门牌
				}
				console.log('form', form);
				updateDevice(form).then(res => {
						uni.navigateBack({
							delta: 1 // 默认值是1，表示返回的页面层数
						});
						uni.hideLoading();
					})
					.catch((err) => {
						console.log("1212", err)
						uni.hideLoading();
					})
			},
			modalCancel() {
				this.showModal = false
				console.log('cancel');
			},
			modalClose() {
				this.showModal = false
				console.log('close');
			}
		},
		onReady() {
			//如果需要兼容微信小程序，并且校验规则中含有方法等，只能通过setRules方法设置规则。
			this.$refs.addForm.setRules(this.addRules)
		},
	};
</script>

<style lang="scss" scoped>
	.editDeviceInform {
		padding: 0 0 160rpx;

		.addHead {
			text-align: center;
			margin: 100rpx 0;

			image {
				width: 400rpx;
				height: 400rpx;
			}
		}

		.form_view {
			padding-top: 0;
		}
	}
</style>