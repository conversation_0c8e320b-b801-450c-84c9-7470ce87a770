<template>
	<view class="history-page">
		<!-- 无需设置导航栏，使用微信小程序自带的导航栏 -->
		<view class="container">
			<view class="empty-history" v-if="!hasHistory">
				<image src="@/static/image/暂无设备.png" class="empty-image"></image>
				<text class="empty-text">暂无历史指令</text>
				<text class="empty-desc">您还没有发送过任何蓝牙指令</text>
				
				<!-- 仅在开发环境中显示的测试按钮 -->
				<!-- <button class="test-btn" @click="generateTestData">生成测试数据</button> -->
			</view>
			
			<view class="history-list" v-else>
				<view class="history-item" v-for="(item, index) in displayHistory" :key="index">
					<view class="item-content">
						<view class="item-row">
							<text class="item-label">产品类型:</text>
							<text class="item-value">{{item.productType}}</text>
						</view>
						<view class="item-row">
							<text class="item-label">参数类型:</text>
							<text class="item-value">{{item.paramType || '设备网络参数配置'}}</text>
						</view>
						<view class="item-row">
							<text class="item-label">目标设备:</text>
							<text class="item-value">{{item.targetDevice}}</text>
						</view>
						<view class="item-row" v-if="item.reportIp">
							<text class="item-label">上报IP:</text>
							<text class="item-value">{{item.reportIp}}</text>
						</view>
						<view class="item-row" v-if="item.reportPort">
							<text class="item-label">上报端口:</text>
							<text class="item-value">{{item.reportPort}}</text>
						</view>
						<view class="item-time">
							<text>{{item.timestamp}}</text>
							<text class="status" :class="[getStatusClass(item.status)]">
								{{getStatusText(item.status)}}
							</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 自定义toast组件 -->
		<view class="toast-container" v-if="showToast">
			<view class="toast-message" :class="toastType">{{toastMessage}}</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			commandHistory: [],
			showToast: false,
			toastMessage: '',
			toastType: 'default'
		}
	},
	computed: {
		displayHistory() {
			// 返回倒序的历史记录，最新的在前面
			return this.commandHistory.slice().reverse();
		},
		// 添加计算属性来判断是否有历史记录
		hasHistory() {
			return this.commandHistory && this.commandHistory.length > 0;
		}
	},
	onLoad() {
		this.loadHistory();
		console.log('历史记录页面加载，记录数量:', this.commandHistory.length);
	},
	onShow() {
		// 每次页面显示时重新加载历史记录
		this.loadHistory();
		console.log('历史记录页面显示，记录数量:', this.commandHistory.length);
	},
	methods: {
		loadHistory() {
			const history = uni.getStorageSync('commandHistory');
			if (history) {
				try {
					this.commandHistory = JSON.parse(history);
					console.log('成功加载历史记录:', this.commandHistory.length, '条');
				} catch (e) {
					console.error('解析历史记录失败:', e);
					this.commandHistory = [];
				}
			} else {
				console.log('未找到历史记录');
				this.commandHistory = [];
			}
		},
		getStatusText(status) {
			switch (status) {
				case 'synced':
					return '已同步';
				case 'sync_failed':
					return '同步失败';
				case 'pending_sync':
				default:
					return '待同步';
			}
		},
		getStatusClass(status) {
			switch (status) {
				case 'synced':
					return 'status-synced';
				case 'sync_failed':
					return 'status-failed';
				case 'pending_sync':
				default:
					return 'status-pending';
			}
		},
		showCustomToast(options) {
			this.toastMessage = options.message || '';
			this.toastType = options.type || 'default';
			this.showToast = true;
			
			// 自动关闭
			setTimeout(() => {
				this.showToast = false;
			}, 2000);
			
			// 为了兼容性，同时使用uni.showToast
			uni.showToast({
				title: options.message || '',
				icon: options.type === 'error' ? 'error' : (options.type === 'warning' ? 'none' : 'success'),
				duration: 2000
			});
		},
		generateTestData() {
			console.log('生成测试数据');
			// 创建一条测试记录
			const testRecord = {
				id: Date.now(),
				productType: '家用型燃气报警器',
				paramType: '设备网络参数配置',
				reportIp: '*************',
				reportPort: '8080',
				targetDevice: 'SN_A0001_KITCHEN',
				timestamp: this.formatDateTime(new Date()),
				status: 'pending_sync'
			};
			
			// 添加到历史记录
			this.commandHistory.push(testRecord);
			
			// 保存到本地存储
			uni.setStorageSync('commandHistory', JSON.stringify(this.commandHistory));
			
			// 显示提示
			this.showCustomToast({
				message: '已生成测试数据',
				type: 'default'
			});
		},
		formatDateTime(date) {
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			const seconds = String(date.getSeconds()).padStart(2, '0');
			return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
		}
	}
}
</script>

<style lang="scss" scoped>
.history-page {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.container {
	padding: 30rpx;
	min-height: 90vh;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
}

.empty-history {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 70vh;
	margin-top: 30rpx;
}

.empty-image {
	width: 240rpx;
	height: 240rpx;
	margin-bottom: 40rpx;
}

.empty-text {
	color: #666666;
	font-size: 36rpx;
	font-weight: 500;
	margin-bottom: 10rpx;
}

.empty-desc {
	color: #999999;
	font-size: 28rpx;
	margin-top: 10rpx;
	margin-bottom: 50rpx;
}

.test-btn {
	background-color: #47afff;
	color: #ffffff;
	font-size: 28rpx;
	padding: 10rpx 30rpx;
	border-radius: 30rpx;
	border: none;
	margin-top: 30rpx;
}

.history-list {
	width: 100%;
}

.history-item {
	padding: 20rpx 0;
	margin-bottom: 30rpx;
	background-color: #ffffff;
	border-radius: 20rpx;
	padding: 20rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
	
	&:last-child {
		margin-bottom: 0;
	}
}

.item-content {
	background-color: #fafafa;
	border-radius: 16rpx;
	padding: 24rpx;
	border-left: 8rpx solid #47afff;
}

.item-row {
	display: flex;
	margin-bottom: 16rpx;
}

.item-label {
	font-size: 28rpx;
	color: #666666;
	width: 180rpx;
}

.item-value {
	font-size: 28rpx;
	color: #333333;
	flex: 1;
	font-weight: 500;
}

.item-time {
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-size: 24rpx;
	color: #999999;
	margin-top: 20rpx;
	border-top: 2rpx dashed #f0f0f0;
	padding-top: 16rpx;
}

.status {
	font-weight: 500;
	padding: 4rpx 12rpx;
	border-radius: 8rpx;
	
	&.status-synced {
		color: #4cd964;
		background-color: rgba(76, 217, 100, 0.1);
	}
	
	&.status-pending {
		color: #ffcc00;
		background-color: rgba(255, 204, 0, 0.1);
	}
	
	&.status-failed {
		color: #ff3b30;
		background-color: rgba(255, 59, 48, 0.1);
	}
}

/* 添加Toast样式 */
.toast-container {
	position: fixed;
	bottom: 100rpx;
	left: 50%;
	transform: translateX(-50%);
	z-index: 9999;
}

.toast-message {
	padding: 20rpx 40rpx;
	background-color: rgba(0, 0, 0, 0.7);
	color: #ffffff;
	border-radius: 10rpx;
	font-size: 28rpx;
	
	&.error {
		background-color: rgba(255, 59, 48, 0.9);
	}
	
	&.warning {
		background-color: rgba(255, 204, 0, 0.9);
	}
}
</style> 