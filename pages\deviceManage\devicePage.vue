<template>
	<!-- 设备页面 -->
	<view class="devicePage">

		<view class="addHead">

			<view class="image1">

				<image src="@/static/product/fajing.png" mode="" v-if="devContent.equipType == '002'"></image>
				<image src="@/static/homePage/dmsIcon.png" mode="" v-else-if="devContent.equipType == '004'"></image>
				<template v-else-if="devContent.equipType == '001'">
					<image src="@/static/product/yuanxing.png" mode="" v-if="devContent.code.indexOf('82') == 0">
					</image>
					<image src="@/static/product/fangxing.png" mode="" v-else-if="devContent.code.indexOf('81') == 0">
					</image>
					<image src="@/static/product/home.png" mode="" v-else></image>
				</template>
				<image src="@/static/product/gongshang.png" mode="" v-else-if="devContent.equipType == '003'"></image>
			</view>
		</view>
		<view class="funcView">
			<view class="func_a" v-if="isConnect == 1">
				<view class="func_a_image">
					<view class="image_a">
						<image src="@/static/image/信号 (1).png" mode=""></image>
					</view>
					<text>已连接</text>
				</view>


			</view>
			<view class="func_a" v-else>
				<view class="func_a_image">
					<view class="image_a">
						<image src="@/static/image/unSignal1.png" mode=""></image>
					</view>
				</view>
				<text>未连接</text>
			</view>
			<view class="func" v-if="devContent.equipType == '001' || devContent.equipType == '003'">


				<view class="func_b">
					<view class="func_b_a">
						<text class="b_a_T1" :style="'color:' + gradeColor + ';' + 'font-size:' + '40rpx'">{{ grade }}</text>
						<text class="b_a_T2">燃气等级值</text>
					</view>
					<view class="func_b_b">
						<text class="b_a_T1">{{ modelPatient.userInfo.chroma }}%LEL</text>
						<text class="b_a_T2">燃气当前值</text>
					</view>
					<view class="func_b_c">
						<!-- <text class="b_a_T1">{{devContent.status}}</text> -->

						<text class="b_a_T1" v-if="stutes == '正常工作'"
							style="color: #53C21D;font-size: 40rpx;">{{ stutes }}</text>
						<text class="b_a_T1" v-else style="color: #ff0000;font-size: 40rpx;">{{ stutes }}</text>
						<text class="b_a_T2">设备状态</text>
					</view>

				</view>
				<!-- </swiper-item> -->
				<!--
			<swiper-item> -->
				<view class="line"></view>

				<view class="func_b_1">
					<view class="func_b_a">
						<text class="b_a_T1" v-if="devContent.equipType == '001'">8%LEL</text>
						<text class="b_a_T1" v-else-if="devContent.equipType == '003'">10%LEL</text>
						<text class="b_a_T1" v-else>3%LEL</text>
						<text class="b_a_T2">报警设定值</text>
					</view>
					<view class="func_b_b">
						<text class="b_a_T1">甲烷(CH4)</text>
						<text class="b_a_T2">探测气体</text>
					</view>
					<view class="func_b_c">
						<!-- <view class="body-view">
					    <switch class='switch-view' :checked="switch1Checked" @change="switch1Change" type='switch'/>
					  </view>
					<text class="b_c_T2">远程关阀</text> -->
						<text class="b_a_T1">未检测</text>
						<text class="b_a_T2">电磁阀状态</text>

					</view>
				</view>



				<!-- </swiper-item>
			</swiper> -->

			</view>

			<view class="func_c" v-if="devContent.equipType == '001' || devContent.equipType == '003'">
				<view class="func_c_a" @click="goToast1">
					<text>设备消音</text>
					<image src="@/static/image/消音.png" mode=""></image>
				</view>
				<view class="func_c_b" @click="goToast2">
					<text>安全自检</text>
					<image src="@/static/image/自检.png" mode=""></image>
				</view>
			</view>
			<view class="func_c" v-if="devContent.equipType == '001' || devContent.equipType == '003'">
				<view class="func_c_a" @click="switch1Change">
					<text>远程关阀</text>
					<image src="@/static/image/关阀.png" mode=""></image>
				</view>
				<view class="func_c_b" @click="goDevDetail">
					<text>设备详情</text>
					<image src="@/static/image/详情.png" mode=""></image>
				</view>
			</view>

			<view class="func" v-else>
				<view class="func_b">
					<view class="func_b_a">
						<text class="b_a_T1" :style="{ color: gradeColor }">{{ grade }}</text>
						<text class="b_a_T2">燃气等级值</text>
					</view>
					<view class="func_b_b">
						<text class="b_a_T1">{{ chroma_1 }}%LEL</text>
						<text class="b_a_T2">燃气当前值</text>
					</view>
					<view class="func_b_c">
						<text class="b_a_T1" v-if="stutes == '正常工作'" style="color: #53C21D;">{{ stutes }}</text>
						<text class="b_a_T3" v-else style="color: #ff0000;">{{ stutes }}</text>
						<text class="b_a_T2">设备状态</text>
					</view>
				</view>
			</view>


			<view v-if="devContent.equipType == '002' || devContent.equipType == '004'" class="func_d"
				@click="goDevDetail">
				<text>详情</text>
				<image src="@/static/homePage/rightIcon.png" mode=""></image>
			</view>
		</view>
		<u-toast ref="uToast"></u-toast>

		<u-modal title="提示" :content="content" :show="showModal"></u-modal>

		<u-modal :title="test_title" :show="show_Modal" @confirm="confirm"
			style="text-align: center;height: 40%;width: 80%;">
			<div>

				<p style="display: flex;justify-content: center;align-items: center;margin-top: 20rpx;">
					<img v-if="iamge_status == '1'" style="width:50px;height:50px;margin-bottom:50rpx;"
						src="../../static/homePage/jinggao.png" alt="">
					<image style="width:200rpx;height:200rpx;margin: 0 auto;" v-else-if="iamge_status == '2'"
						src="@/static/image/自检中.gif" mode="aspectFit" />
					<img v-else-if="iamge_status == '3'" style="width:50px;height:50px;margin-bottom: 50rpx;"
						src="../../static/image/设备正常.png" alt="">
				</p>
				<div style="display: flex;justify-content: center;align-items: center;">
					<p v-if="Self_Status == 1010">设备自检中，请勿进行其他操作! </p>
					<p v-else-if="Self_Status == 1014">
						<text>设备自检完成！自检结果:</text>
						<text v-if="test_msg == '正常工作'" style="color: #2acd3e;">{{ test_msg }}</text>
						<text v-else style="color: red;">{{ test_msg }}</text>
					</p>
					<p v-else-if="Self_Status == 1011">设备初始化中，无法自检! </p>
					<p v-else-if="Self_Status == 1012">设备告警中，无法自检! </p>
					<p v-else-if="Self_Status == 1013">设备自检超时! </p>
					<p v-else-if="Self_Status == 500">出现了错误，请重新自检! </p>
				</div>
			</div>
		</u-modal>
	</view>
</template>

<script>
import {
	BulkAdd,
	GetConfigs,
	devDetail,
	DeviceContorl,
	GetCmdRes
} from "@/network/api.js"
import { baseURL_a } from "@/network/base.js"
export default {
	data() {
		return {
			switch1Checked: true,
			content: '当前无所属场景数据，请前往添加',
			showModal: false,
			show_Modal: false,
			test_msg: '未检测到结果',
			iamge_status: 2,
			isOver: false,
			test_title: '提示',
			Self_Status: 1010,
			tabsLocation: 0,
			devContent: {},
			chroma_1: '',
			fontWeightstyle: 500,
			stutes: '正常工作',
			modelPatient: {
				userInfo: {
					equipTypeE: '',
					code: '',
					name: '',
					statusE: '',
					chroma: 0,
					temp: '',
					signal: '',
					pwr: '',
					location: '',
					updateTime: ''
				},
			},
			count: 0,
			isConnect: 0,
			grade: '正常',
			gradeColor: "#53C21D",
		};
	},
	onLoad(opt) { // 初始化

		let optData = JSON.parse(opt.params);
		this.devContent = optData;
		console.log(this.devContent, '1');

		if (optData.online == true) {
			this.isConnect = 1;
		}
		if (this.devContent.equipType == '002' && this.isConnect == 1) {
			if (this.devContent.chroma >= 1 && this.devContent.chroma < 3) {
				this.grade = "轻度泄漏";
				this.gradeColor = "#ffaa00"
			}
			else if (this.devContent.chroma >= 3) {
				this.grade = "严重泄漏";
				this.gradeColor = "#ff0000"
			}
			else {
				this.grade = "正常";
				this.gradeColor = "#53C21D"
			}
		}
		else if (this.devContent.equipType == '004' && this.isConnect == 1) {
			if (this.devContent.chroma >= 1 && this.devContent.chroma < 3) {
				this.grade = "轻度泄漏";
				this.gradeColor = "#ffaa00"
			}
			else if (this.devContent.chroma >= 3) {
				this.grade = "严重泄漏";
				this.gradeColor = "#ff0000"
			}
			else {
				this.grade = "正常";
				this.gradeColor = "#53C21D"
			}
		}
		else if (this.devContent.equipType == '001' && this.isConnect == 1) {
			if (this.devContent.chroma >= 3 && this.devContent.chroma < 8) {
				this.grade = "轻度泄漏";
				this.gradeColor = "#ffaa00"
			}
			else if (this.devContent.chroma >= 8) {
				this.grade = "严重泄漏";
				this.gradeColor = "#ff0000"
			}
			else {
				this.grade = "正常";
				this.gradeColor = "#53C21D"
			}
		}
		else if (this.devContent.equipType == '003' && this.isConnect == 1) {
			if (this.devContent.chroma >= 3 && this.devContent.chroma < 10) {
				this.grade = "轻度泄漏";
				this.gradeColor = "#ffaa00"
			}
			else if (this.devContent.chroma >= 10) {
				this.grade = "严重泄漏";
				this.gradeColor = "#ff0000"
			}
			else {
				this.grade = "正常";
				this.gradeColor = "#53C21D"
			}
		}
		this.init(opt)


	},
	methods: {
		getConfigsList() {

		},
		confirm() {
			if (!this.isOver) {
				console.log('设备自检未完成')
			}
			else {

				this.show_Modal = false



			}
		},
		onHide() {
			this.clearTimer();
		},
		onPullDownRefresh(opt) {
			let query = {
				code: this.devContent.code, //设备编码
				token: uni.getStorageSync('token')
			}
			devDetail(query).then(res => {
				this.devContent = res.data;
				this.modelPatient.userInfo = res.data;
				this.modelPatient.userInfo.chroma = res.data.chroma;
				this.stutes = res.data.status;
				if (this.modelPatient.userInfo.online == false) {
					this.isConnect = 0;
				}
				else {
					this.isConnect = 1;
				}

				console.log(this.modelPatient.userInfo.chroma, 'chroma')
				if (this.modelPatient.userInfo.equipType == '002' && this.isConnect == 1) {
					if (this.modelPatient.userInfo.chroma >= 1 && this.modelPatient.userInfo.chroma < 3) {
						this.grade = "轻度泄漏";
						this.gradeColor = "#ffaa00"
					}
					else if (this.modelPatient.userInfo.chroma >= 3) {
						this.grade = "严重泄漏";
						this.gradeColor = "#ff0000"
					}
					else {
						this.grade = "正常";
						this.gradeColor = "#53C21D"
					}
				}
				else if (this.modelPatient.userInfo.equipType == '004' && this.isConnect == 1) {
					if (this.modelPatient.userInfo.chroma >= 1 && this.modelPatient.userInfo.chroma < 3) {
						this.grade = "轻度泄漏";
						this.gradeColor = "#ffaa00"
					}
					else if (this.modelPatient.userInfo.chroma >= 3) {
						this.grade = "严重泄漏";
						this.gradeColor = "#ff0000"
					}
					else {
						this.grade = "正常";
						this.gradeColor = "#53C21D"
					}
				}
				else if (this.modelPatient.userInfo.equipType == '001' && this.isConnect == 1) {
					if (this.modelPatient.userInfo.chroma >= 3 && this.modelPatient.userInfo.chroma < 8) {
						this.grade = "轻度泄漏";
						this.gradeColor = "#ffaa00"
					}
					else if (this.modelPatient.userInfo.chroma >= 8) {
						this.grade = "严重泄漏";
						this.gradeColor = "#ff0000"
					}
					else {
						this.grade = "正常";
						this.gradeColor = "#53C21D"
					}
				}
				else if (this.modelPatient.userInfo.equipType == '003' && this.isConnect == 1) {
					if (this.modelPatient.userInfo.chroma >= 3 && this.modelPatient.userInfo.chroma < 10) {
						this.grade = "轻度泄漏";
						this.gradeColor = "#ffaa00"
					}
					else if (this.modelPatient.userInfo.chroma >= 10) {
						this.grade = "严重泄漏";
						this.gradeColor = "#ff0000"
					}
					else {
						this.grade = "正常";
						this.gradeColor = "#53C21D"
					}
				}
				uni.stopPullDownRefresh();

			})
				.catch((err) => { })

			// setTimeout(function () {
			// 			uni.stopPullDownRefresh();
			// 		}, 1000);
		},
		clearTimer() {
			clearInterval(this.timer);
		},
		startTimer() {

			this.timer = setInterval(this.getCmdRes, 3000); // 每3秒调用一次接口

		},
		CloseFA() {
			let query = {
				cmd: "03",
				dev: this.devContent.code
			}
			DeviceContorl(query).then(res => {

				uni.showLoading({
					title: '关阀指令发送中',
					mask: true
				});

				setTimeout(function () {
					uni.hideLoading();
				}, 1500);
				resolve(res.data)
			})
				.catch((err) => { })

		},
		getCmdRes() {
			this.count = this.count + 1,

				console.log("this.count", this.count)
			let query = {
				vecode: uni.getStorageSync('vecode')
			}
			GetCmdRes(query).then(res => {
				if (res.success == true) {

					if (res.code == 1011) {
						this.iamge_status = 1,
							this.Self_Status = 1011,
							this.clearTimer(),
							this.isOver = true


					}
					else if (res.code == 1012) {
						this.iamge_status = 1,
							this.Self_Status = 1012,
							this.clearTimer(),
							this.isOver = true
					}
					else if (res.code == 1013) {
						this.iamge_status = 1,
							this.Self_Status = 1013,
							this.clearTimer(),
							this.isOver = true
					}
					else if (res.code == 1014) {

						this.iamge_status = 3,
							this.Self_Status = 1014,
							this.clearTimer(),
							this.test_msg = res.msg,
							this.stutes = res.msg;
						this.isOver = true
					}
					else if (res.code == 1010) {
						if (this.count >= 15) {
							this.clearTimer(),
								this.iamge_status = 1,
								this.Self_Status = 500,
								this.isOver = true
						}
						else {
							this.iamge_status = 2,
								this.Self_Status = 1010
						}
					}

					else {
						this.clearTimer(),
							this.iamge_status = 1,
							this.Self_Status = 500,
							this.isOver = true
					}

				}
				else {
					this.clearTimer(),
						this.iamge_status = 1,
						this.Self_Status = 500,
						this.isOver = true


				};
			})
				.catch((err) => { })


		},

		init(opt) { // 初始化数据


			let query = {
				code: this.devContent.code, //设备编码
				token: uni.getStorageSync('token')
			}
			devDetail(query).then(res => {

				this.modelPatient.userInfo = res.data;
				this.modelPatient.userInfo = res.data;
				this.modelPatient.userInfo.chroma = res.data.chroma;
				this.stutes = res.data.status;
				console.log(this.modelPatient.userInfo, 'optData.chroma')

				this.chroma_1 = this.modelPatient.userInfo.chroma



				if (this.modelPatient.userInfo.online == false) {
					this.isConnect = 0;
				}
				else {
					this.isConnect = 1;
				}


				console.log(this.modelPatient.userInfo.chroma, 'chroma')
				console.log(this.modelPatient.userInfo.updateTime, 'updateTime')
				if (this.modelPatient.userInfo.equipType == '004' && this.isConnect == 1) {
					if (this.modelPatient.userInfo.chroma >= 1 && this.modelPatient.userInfo.chroma < 3) {
						this.grade = "轻度泄漏";
						this.gradeColor = "#ffaa00"
					}
					else if (this.modelPatient.userInfo.chroma >= 3) {
						this.grade = "严重泄漏";
						this.gradeColor = "#ff0000"
					}
					else {
						this.grade = "正常";
						this.gradeColor = "#53C21D"
					}
				}
				else if (this.modelPatient.userInfo.equipType == '002' && this.isConnect == 1) {
					if (this.modelPatient.userInfo.chroma >= 1 && this.modelPatient.userInfo.chroma < 3) {
						this.grade = "轻度泄漏";
						this.gradeColor = "#ffaa00"
					}
					else if (this.modelPatient.userInfo.chroma >= 3) {
						this.grade = "严重泄漏";
						this.gradeColor = "#ff0000"
					}
					else {
						this.grade = "正常";
						this.gradeColor = "#53C21D"
					}
				}
				else if (this.modelPatient.userInfo.equipType == '001' && this.isConnect == 1) {
					if (this.modelPatient.userInfo.chroma >= 3 && this.modelPatient.userInfo.chroma < 8) {
						this.grade = "轻度泄漏";
						this.gradeColor = "#ffaa00"
					}
					else if (this.modelPatient.userInfo.chroma >= 8) {
						this.grade = "严重泄漏";
						this.gradeColor = "#ff0000"
					}
					else {
						this.grade = "正常";
						this.gradeColor = "#53C21D"
					}
				}
				else if (this.modelPatient.userInfo.equipType == '003' && this.isConnect == 1) {
					if (this.modelPatient.userInfo.chroma >= 3 && this.modelPatient.userInfo.chroma < 10) {
						this.grade = "轻度泄漏";
						this.gradeColor = "#ffaa00"
					}
					else if (this.modelPatient.userInfo.chroma >= 10) {
						this.grade = "严重泄漏";
						this.gradeColor = "#ff0000"
					}
					else {
						this.grade = "正常";
						this.gradeColor = "#53C21D"
					}
				}

			})
				.catch((err) => { })





		},
		switch1Change(e) {
			this.isOver = false
			if (this.isConnect == 1) {


				uni.showModal({
					title: '提示',
					content: '确定要远程关阀吗？关阀后需手动开启!',
					success: res => {
						if (res.confirm) {//这里是点击了确定以后

							let query = {
								cmd: "03",
								dev: this.devContent.code
							}
							DeviceContorl(query).then(res => {

								uni.showLoading({
									title: '关阀指令发送中',
									mask: true
								});

								setTimeout(function () {
									uni.hideLoading();
								}, 1500);
								resolve(res.data)
							})
								.catch((err) => { })
						} else {//这里是点击了取消以后
							console.log('用户点击取消')
						}
					}
				})


			}
			else {
				this.$refs.uToast.show({
					message: '设备未连接',
					position: 'bottom'
				})
			}
		},
		goDevDetail() {
			let query = {
				code: this.devContent.code,
				isConnect: this.isConnect

			}
			uni.navigateTo({
				url: '/pages/deviceManage/deviceDetails?code=' + this.devContent.code + '&isConnect=' + this.isConnect
			})
		},
		publicMethod(time) {
			return (new Date(time)).getTime()
		},
		goToast1() {
			this.isOver = false
			if (this.isConnect == 1) {

				uni.showModal({
					title: '提示',
					content: '确定要远程消音吗',
					success: res => {
						if (res.confirm) {//这里是点击了确定以后

							let query = {
								cmd: "01",
								dev: this.devContent.code
							}
							DeviceContorl(query).then(res => {

								uni.showLoading({
									title: '消音指令发送中',
									mask: true
								});

								setTimeout(function () {
									uni.hideLoading();
								}, 1500);
								resolve(res.data)
							})
								.catch((err) => { })

						}
						else {//这里是点击了取消以后
							console.log('用户点击取消')
						}
					}
				})
			}

			else {
				this.$refs.uToast.show({
					message: '设备未连接',
					position: 'bottom'
				})
			}
		},
		goToast2() {
			this.isOver = false
			if (this.isConnect == 1) {

				uni.showModal({
					title: '提示',
					content: '确定要进行安全自检吗？',
					success: res => {
						if (res.confirm) {//这里是点击了确定以后
							let query = {
								cmd: "02",
								dev: this.devContent.code
							}
							this.count = 0,
								DeviceContorl(query).then(res => {
									this.count = 0,
										console.log("dadsadada");
									uni.setStorageSync("vecode", res.data.vecode);
									this.iamge_status = 2;
									this.Self_Status = 1010;
									this.show_Modal = true;
									this.timer = setInterval(this.getCmdRes, 2000); // 每5秒调用一次接口
								})
									.catch((err) => { })
						} else {//这里是点击了取消以后
							console.log('用户点击取消')
						}
					}
				})
			}
			else {
				this.$refs.uToast.show({
					message: '设备未连接',
					position: 'bottom'
				})
			}
		},
		modalConfirm() {
			this.showModal = false
			console.log('confirm');
			uni.showLoading({
				title: '请求中..',
				mask: true
			})
			let form = {
				uid: uni.getStorageSync('uid'),
				tubeBulkAddStructures: this.formEndArry
			}
			BulkAdd(form).then(res => {
				if (res.statusCode === 200) {
					// uni.showToast({
					// 	title: "保存成功",
					// 	icon: "success"
					// });
					uni.switchTab({
						url: "/pages/homePage/homePage"
					})
					uni.hideLoading();
				} else {
					uni.hideLoading();
					uni.showToast({
						title: res.data,
						icon: "none"
					});
				}
			})
				.catch((err) => {
					console.log("1212", err)
					uni.hideLoading();
				})
		},
	},
	onReady() {
		uni.setNavigationBarTitle({
			title: this.devContent.name
		})
	},
};
</script>

<style>
page {
	background-color: #F6F6F6;
}
</style>
<style lang="scss" scoped>
.devicePage {

	.NULL1 {
		width: 100%;
		height: 186rpx;
	}

	.addHead {

		padding: 0rpx 30rpx;
		margin-top: 20rpx;


		.image1 {
			border-radius: 20rpx;
			display: flex;
			justify-content: center;
			/*水平居中*/
			align-items: center;
			/*垂直居中*/
			background-color: #FFFFFF;
			height: 400rpx;

			image {

				width: 350rpx;
				height: 350rpx;
			}
		}
	}

	.funcView {
		padding: 20rpx 30rpx;

		.func_a {
			display: flex;

			height: 160rpx;
			background-color: #FFFFFF;
			border-radius: 20rpx;
			padding: 0rpx 30rpx;
			line-height: 160rpx;
			margin-bottom: 30rpx;
			font-size: 40rpx;
			color: #606266;

			.func_a_image {
				display: flex;

				.image_a {
					padding: 10rpx 20rpx 0 0;

					image {
						width: 50rpx;
						height: 50rpx;
					}
				}



			}

			text {}
		}


		.func {
			background-color: #FFFFFF;
			border-radius: 20rpx;
			//padding: 40rpx 30rpx;
			margin-bottom: 30rpx;

			.func_b {
				background-color: #FFFFFF;
				border-radius: 20rpx;
				padding: 40rpx 30rpx;

				display: flex;
				justify-content: space-around;

				.b_a_T1 {
					display: block;
					// font-size: 40rpx;
					color: #53C21D;
				}

				.b_a_T2 {
					display: block;
					font-size: 28rpx;
					color: #606266;
					line-height: 44rpx;
				}

				.func_b_a {
					width: 30%;
					text-align: center;


				}

				.func_b_b {
					width: 30%;
					text-align: center;
					border-left: 1px solid #D8D8D8;
					border-right: 1px solid #D8D8D8;
					padding: 0 10rpx;

					.b_a_T1 {
						color: #3D3D3D;
						font-size: 40rpx;
					}
				}

				.func_b_c {
					width: 30%;
					text-align: center;

					.body-view {
						margin-bottom: 6rpx;
					}

					.b_c_T2 {
						color: #3D3D3D;
					}
				}
			}

			.line {
				margin: 0 30rpx 0 30rpx;
				text-align: center;
				border: 0.5rpx solid #D8D8D8;

			}

			.func_b_1 {
				background-color: #FFFFFF;
				border-radius: 20rpx;
				padding: 40rpx 30rpx;

				display: flex;
				justify-content: space-around;

				.b_a_T1 {
					display: block;
					font-size: 40rpx;
					color: #53C21D;
				}

				.b_a_T2 {
					display: block;
					font-size: 28rpx;
					color: #606266;
					line-height: 44rpx;
				}

				.func_b_a {
					width: 30%;
					text-align: center;

				}

				.func_b_b {
					width: 30%;
					text-align: center;
					border-left: 1px solid #D8D8D8;
					border-right: 1px solid #D8D8D8;
					padding: 0 10rpx;

					.b_a_T1 {
						color: #3D3D3D;
						font-size: 40rpx;
					}
				}

				.func_b_c {
					width: 30%;
					text-align: center;

					.body-view {
						margin-bottom: 6rpx;
					}

					.b_c_T2 {
						color: #3D3D3D;
					}
				}
			}

		}



		.func_c {
			display: flex;
			justify-content: space-between;

			.func_c_a {
				width: 40%;
				height: 160rpx;
				background-color: #FFFFFF;
				border-radius: 20rpx;
				padding: 0rpx 30rpx;
				margin-bottom: 30rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				color: #3D3D3D;

				image {
					width: 65rpx;
					height: 65rpx;
					opacity: 0.8
				}

				text {
					font-size: 28rpx;
				}
			}

			.func_c_b {
				@extend .func_c_a;

				text {
					font-size: 28rpx;
				}
			}
		}

		.func_d {
			height: 100rpx;
			background-color: #FFFFFF;
			border-radius: 20rpx;
			padding: 0rpx 30rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			color: #3D3D3D;

			image {
				width: 34rpx;
				height: 34rpx;
			}
		}
	}
}
</style>