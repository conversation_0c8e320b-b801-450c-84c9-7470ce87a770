<template>
	<view class="log-viewer-page">
		<view class="container">
			<!-- Device Info Card -->
			<view class="device-info-card">
				<view class="device-name">{{ deviceName || '未知设备' }}</view>
				<view class="device-status" :class="connectionStatusClass">{{ connectionStatus }}</view>
				<view class="device-mac" v-if="deviceMac">MAC: {{ deviceMac }}</view>
			</view>

			<!-- Log Control Section -->
			<view class="log-control-section">
				<button class="btn btn-primary" @click="startLogRetrieval" :disabled="isRetrievingLogs || !isConnected"
					v-if="!logListening">
					<view class="btn-content">
						<view class="loading-spinner" v-if="isRetrievingLogs"></view>
						<text class="btn-text">{{ isRetrievingLogs ? '获取中...' : '获取日志' }}</text>
					</view>
				</button>
				<button class="btn btn-warning" @click="stopCurrentLogSession" v-if="logListening && !isRetrievingLogs">
					<text class="btn-text">停止监听</text>
				</button>
				<button class="btn btn-secondary" @click="clearLogs" :disabled="!logContent">
					<text class="btn-text">清空日志</text>
				</button>
			</view>

			<!-- Advanced Log Control Section -->
			<view class="advanced-control-section">
				<button class="btn btn-small btn-outline" @click="toggleTimestamps">
					<text class="btn-text">{{ enableTimestamps ? '禁用时间戳' : '启用时间戳' }}</text>
				</button>
				<button class="btn btn-small btn-outline" @click="exportLogs" :disabled="!logContent">
					<text class="btn-text">导出日志</text>
				</button>
				<view class="log-stats" v-if="logContent">
					<text class="stats-text">条目: {{ logEntryCount }} | 大小: {{ Math.round(logContent.length / 1024 * 100) /
						100 }}KB</text>
				</view>
			</view>

			<!-- Status Messages -->
			<view class="status-section" v-if="statusMessage">
				<view class="status-message" :class="statusType">
					<text class="status-icon">{{ statusIcon }}</text>
					<text class="status-text">{{ statusMessage }}</text>
				</view>
			</view>

			<!-- Log Display Area -->
			<view class="log-display-container">
				<scroll-view class="log-text-area" :scroll-top="scrollTop" scroll-y>
					<text class="log-content" :key="logUpdateCounter">{{ logContent || '暂无日志内容，点击"获取日志"开始获取设备日志...'
					}}</text>
				</scroll-view>
			</view>
		</view>

		<!-- Toast Container -->
		<view class="toast-container" v-if="showToast">
			<view class="toast-message" :class="toastType">{{ toastMessage }}</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			// Device information
			currentDevice: null,
			deviceName: '',
			deviceMac: '',
			isConnected: false,

			// Log state
			isRetrievingLogs: false,
			logContent: '',
			scrollTop: 0,
			isFirstLogPacket: true,
			logListening: false,
			logUpdateCounter: 0, // Counter to force display updates

			// Status messages for user feedback
			statusMessage: '',
			statusType: 'info', // 'info', 'success', 'warning', 'error'
			statusIcon: '',

			// Toast
			showToast: false,
			toastMessage: '',
			toastType: 'default',

			// Local command response handling (fallback)
			localPendingCommands: new Map(),

			// Error handling counters
			malformedPacketCount: 0,
			processingErrorCount: 0,

			// Timeout handles
			commandTimeoutHandle: null,
			connectionHealthInterval: null,

			// Log content management
			logSizeWarningShown: false,
			logStartTime: null,
			enableTimestamps: false,
			logEntryCount: 0,

			// Performance optimization
			renderOptimizationEnabled: true,
			lastRenderTime: 0,
			renderThrottleDelay: 100, // ms

			// Log completion detection
			lastLogDataTime: null,
			logCompletionTimer: null,
			logCompletionTimeout: 3000, // 3 seconds without new data = completion
			hasReceivedLogData: false
		}
	},
	computed: {
		connectionStatus() {
			return this.isConnected ? '已连接' : '未连接';
		},
		connectionStatusClass() {
			return this.isConnected ? 'connected' : 'disconnected';
		}
	},
	onLoad(options) {
		// Get device information from navigation parameters
		if (options.device) {
			try {
				this.currentDevice = JSON.parse(decodeURIComponent(options.device));
				this.deviceName = this.currentDevice.name || '未知设备';
				this.deviceMac = this.currentDevice.macAddress || this.currentDevice.deviceId;
				this.isConnected = true; // Assume connected if we got here from bluetooth page
			} catch (e) {
				console.error('解析设备信息失败:', e);
			}
		}

		// Set page title with device name
		if (this.deviceName) {
			uni.setNavigationBarTitle({
				title: '设备日志 - ' + this.deviceName
			});
		}

		// Setup bluetooth listener for log data
		this.setupBluetoothListener();

		// Setup connection monitoring
		this.setupConnectionMonitoring();
	},
	onUnload() {
		// Ensure proper cleanup when user navigates away from log page
		console.log('页面卸载，执行清理操作');
		this.stopLogListening();
		this.cleanupLogMonitoring();
		this.cleanupPendingCommands();
		this.cleanupBluetoothListener();
		this.cleanupConnectionMonitoring();
		this.cleanupTimeouts();
		this.resetPageState();
	},
	onHide() {
		// Stop log listening when page is hidden but keep listener active
		console.log('页面隐藏，停止日志监听');
		this.stopLogListening();
	},
	onShow() {
		// Resume log listening state when page becomes visible again
		console.log('页面显示，检查日志监听状态');
		// Note: Don't automatically resume log listening, let user manually restart if needed
	},
	methods: {
		showCustomToast(options) {
			this.toastMessage = options.message || '';
			this.toastType = options.type || 'default';
			this.showToast = true;

			// Auto close
			setTimeout(() => {
				this.showToast = false;
			}, 2000);

			// Also use uni.showToast for compatibility
			uni.showToast({
				title: options.message || '',
				icon: options.type === 'error' ? 'error' : (options.type === 'warning' ? 'none' : 'success'),
				duration: 2000
			});
		},

		// Enhanced status message system for better user feedback
		showStatusMessage(message, type = 'info', duration = 3000) {
			this.statusMessage = message;
			this.statusType = type;

			// Set appropriate icon based on type
			switch (type) {
				case 'success':
					this.statusIcon = '✓';
					break;
				case 'warning':
					this.statusIcon = '⚠';
					break;
				case 'error':
					this.statusIcon = '✗';
					break;
				case 'loading':
					this.statusIcon = '⟳';
					break;
				default:
					this.statusIcon = 'ℹ';
			}

			// Auto clear status message
			if (duration > 0) {
				setTimeout(() => {
					this.clearStatusMessage();
				}, duration);
			}
		},

		clearStatusMessage() {
			this.statusMessage = '';
			this.statusType = 'info';
			this.statusIcon = '';
		},

		startLogRetrieval() {
			// Enhanced validation before starting log retrieval
			if (!this.validateConnectionForLogOperations()) {
				return;
			}

			// Check Bluetooth permissions
			if (!this.checkBluetoothPermissions()) {
				return;
			}

			// Prevent multiple simultaneous requests
			if (this.isRetrievingLogs) {
				console.log('日志获取已在进行中，忽略重复请求');
				this.showCustomToast({
					message: '日志获取正在进行中，请稍候',
					type: 'warning'
				});
				return;
			}

			// Additional check for active log listening
			if (this.logListening) {
				console.log('日志监听已激活，停止当前监听后重新开始');
				this.stopLogListening();
			}

			// Reset error counters for new session
			this.malformedPacketCount = 0;
			this.processingErrorCount = 0;

			// Initialize log session tracking
			this.logStartTime = new Date();
			this.logEntryCount = 0;
			this.logSizeWarningShown = false;

			// Add session start marker with timestamp if enabled
			if (this.enableTimestamps) {
				const sessionMarker = `\n=== 日志会话开始 ${this.formatTimestamp(this.logStartTime)} ===\n`;
				this.logContent = this.logContent ? this.logContent + sessionMarker : sessionMarker;
			}

			// Use proper state management to start log listening
			this.startLogListening();

			console.log('开始获取设备日志...');

			// Show loading status message
			this.showStatusMessage('正在发送日志获取指令...', 'loading', 0);

			// Send 0x22 command to device
			this.sendLogCommand();
		},

		/**
		 * Check Bluetooth permissions before operations
		 * @returns {boolean} - True if permissions are available
		 */
		checkBluetoothPermissions() {
			// Check if we're on a platform that requires permission checking
			// #ifdef APP-PLUS
			try {
				// For App platforms, check Bluetooth permissions
				const permissions = ['android.permission.BLUETOOTH', 'android.permission.BLUETOOTH_ADMIN'];

				// This is a simplified check - in real implementation you might want to use
				// uni.requestPermissions or platform-specific permission APIs
				console.log('检查蓝牙权限...');

				// For now, assume permissions are granted if we got this far
				// In a real implementation, you would check actual permissions here
				return true;

			} catch (error) {
				console.error('权限检查失败:', error);
				this.showCustomToast({
					message: '权限检查失败，请确保应用有蓝牙权限',
					type: 'error'
				});
				return false;
			}
			// #endif

			// For other platforms (H5, MP), assume permissions are handled by the platform
			return true;
		},

		sendLogCommand() {
			// Enhanced validation before sending command
			const deviceId = this.currentDevice?.deviceId;
			const serviceId = uni.getStorageSync('writeServiceId');
			const characteristicId = uni.getStorageSync('writeCharacteristicId');

			if (!deviceId || !serviceId || !characteristicId) {
				this.stopLogListening();
				this.showStatusMessage('蓝牙连接信息缺失，无法发送指令', 'error');
				this.showCustomToast({
					message: '蓝牙特征值未找到，请重新连接设备',
					type: 'error'
				});
				return;
			}

			try {
				// Build 0x22 command following standard structure: A5 FE 22 00 00 [CRC]
				const cmd = 0x22;
				const dataLength = 0;

				// Calculate CRC - command + data length bytes
				let crc = cmd;
				crc += (dataLength >> 8) & 0xFF; // High byte of data length
				crc += dataLength & 0xFF;        // Low byte of data length
				crc = crc & 0xFF;                // Take low 8 bits

				// Build command buffer: Header(2) + Command(1) + Length(2) + CRC(1)
				const buffer = new ArrayBuffer(6);
				const dataView = new DataView(buffer);

				dataView.setUint8(0, 0xA5);      // Frame header byte 1
				dataView.setUint8(1, 0xFE);      // Frame header byte 2
				dataView.setUint8(2, cmd);       // Command code
				dataView.setUint16(3, dataLength, false); // Data length (big endian)
				dataView.setUint8(5, crc);       // CRC checksum

				console.log('发送日志获取指令:', this.arrayBufferToHexString(buffer));

				// Send command to device with enhanced error handling
				uni.writeBLECharacteristicValue({
					deviceId: deviceId,
					serviceId: serviceId,
					characteristicId: characteristicId,
					value: buffer,
					success: (res) => {
						console.log('日志指令发送成功:', res);
						this.showStatusMessage('指令发送成功，等待日志数据...', 'loading', 0);
						this.showCustomToast({
							message: '正在获取设备日志...',
							type: 'default'
						});

						// Start monitoring for log data completion based on data reception intervals
						this.startLogDataMonitoring();
					},
					fail: (err) => {
						console.error('日志指令发送失败:', err);
						this.handleLogCommandSendFailure(err);
					}
				});
			} catch (error) {
				console.error('构建日志指令失败:', error);
				this.handleLogCommandBuildFailure(error);
			}
		},

		/**
		 * Handle log command response with comprehensive error checking
		 * @param {Object} result - Command response result
		 */
		handleLogCommandResponse(result) {
			if (result.success) {
				console.log('日志指令响应成功');
				this.showStatusMessage('设备响应成功，开始接收日志...', 'success', 3000);
				// Log streaming will be handled by handleLogData method
			} else {
				console.error('日志指令响应失败:', result);
				this.stopLogListening();

				// Provide specific error messages based on error type
				let errorMessage = '日志获取失败';
				if (result.error === 'timeout') {
					errorMessage = '设备响应超时，请检查设备状态';
				} else if (result.error === 'execution_failed') {
					errorMessage = '设备无法执行日志指令，请重试';
				} else if (result.error === 'command_not_supported') {
					errorMessage = '设备不支持日志功能';
				} else if (result.message) {
					errorMessage = result.message;
				}

				this.showStatusMessage(errorMessage, 'error');
				this.showCustomToast({
					message: errorMessage,
					type: 'error'
				});
			}
		},

		/**
		 * Handle log command send failure
		 * @param {Object} err - Send failure error
		 */
		handleLogCommandSendFailure(err) {
			this.stopLogListening();
			this.cleanupLogMonitoring();

			// Determine specific error message based on error code
			let errorMessage = '指令发送失败';
			if (err.errCode) {
				switch (err.errCode) {
					case 10006:
						errorMessage = '设备连接已断开，请重新连接';
						this.isConnected = false;
						break;
					case 10007:
						errorMessage = '蓝牙特征值操作失败，请重试';
						break;
					case 10008:
						errorMessage = '系统蓝牙异常，请重启应用';
						break;
					default:
						errorMessage = err.errMsg || '未知发送错误';
				}
			} else {
				errorMessage = err.errMsg || '未知发送错误';
			}

			// Show error status message
			this.showStatusMessage(errorMessage, 'error');

			// Use unified error handling mechanism
			this.showCustomToast({
				message: errorMessage,
				type: 'error'
			});
		},

		/**
		 * Handle log command build failure
		 * @param {Error} error - Build failure error
		 */
		handleLogCommandBuildFailure(error) {
			this.stopLogListening();

			const errorMessage = '指令构建失败: ' + (error.message || '未知错误');
			this.showStatusMessage(errorMessage, 'error');
			this.showCustomToast({
				message: errorMessage,
				type: 'error'
			});
		},

		/**
		 * Start monitoring for log data completion based on data reception intervals
		 */
		startLogDataMonitoring() {
			console.log('开始监控日志数据接收完成状态');

			// Reset monitoring state
			this.lastLogDataTime = null;
			this.hasReceivedLogData = false;

			// Clear any existing timer
			if (this.logCompletionTimer) {
				clearTimeout(this.logCompletionTimer);
				this.logCompletionTimer = null;
			}
		},

		/**
		 * Update log data reception timestamp and reset completion timer
		 */
		updateLogDataReception() {
			const currentTime = Date.now();
			this.lastLogDataTime = currentTime;
			this.hasReceivedLogData = true;

			// Clear existing completion timer
			if (this.logCompletionTimer) {
				clearTimeout(this.logCompletionTimer);
			}

			// Set new completion timer
			this.logCompletionTimer = setTimeout(() => {
				this.handleLogCompletionTimeout();
			}, this.logCompletionTimeout);

			console.log(`更新日志数据接收时间: ${new Date(currentTime).toLocaleTimeString()}`);
		},

		/**
		 * Handle log completion when no new data is received for a specified time
		 */
		handleLogCompletionTimeout() {
			console.log('日志数据接收超时，判断为获取完成');

			if (this.hasReceivedLogData) {
				// We received some log data, so completion is normal
				this.showStatusMessage('日志获取完成', 'success', 3000);
				this.showCustomToast({
					message: '日志获取完成',
					type: 'default'
				});

				console.log(`日志获取完成统计: 条目数=${this.logEntryCount}, 总大小=${this.logContent.length}字符`);
			} else {
				// No log data received, might be an issue
				this.showStatusMessage('未收到日志数据，请检查设备状态', 'warning', 5000);
				this.showCustomToast({
					message: '未收到日志数据，请重试',
					type: 'warning'
				});
			}

			// Clean up monitoring state
			this.cleanupLogMonitoring();
		},

		/**
		 * Clean up log monitoring timers and state
		 */
		cleanupLogMonitoring() {
			if (this.logCompletionTimer) {
				clearTimeout(this.logCompletionTimer);
				this.logCompletionTimer = null;
			}

			// Keep log listening active in case user wants to get more logs
			// but stop the retrieval loading state
			this.isRetrievingLogs = false;
		},

		/**
		 * Validate connection for log operations
		 * @returns {boolean} - True if connection is valid
		 */
		validateConnectionForLogOperations() {
			if (!this.isConnected) {
				this.showCustomToast({
					message: '设备未连接，请先连接设备',
					type: 'error'
				});
				return false;
			}

			if (!this.currentDevice) {
				this.showCustomToast({
					message: '设备信息缺失，请重新连接',
					type: 'error'
				});
				return false;
			}

			return true;
		},

		/**
		 * Setup connection monitoring
		 */
		setupConnectionMonitoring() {
			// Basic connection monitoring - can be enhanced
			console.log('设置连接监控');
		},

		/**
		 * Cleanup connection monitoring
		 */
		cleanupConnectionMonitoring() {
			if (this.connectionHealthInterval) {
				clearInterval(this.connectionHealthInterval);
				this.connectionHealthInterval = null;
			}
		},

		/**
		 * Cleanup bluetooth listener
		 */
		cleanupBluetoothListener() {
			try {
				uni.offBLECharacteristicValueChange();
				console.log('清理蓝牙监听器');
			} catch (error) {
				console.error('清理蓝牙监听器失败:', error);
			}
		},

		/**
		 * Cleanup pending commands
		 */
		cleanupPendingCommands() {
			if (this.localPendingCommands) {
				for (const [commandId, commandInfo] of this.localPendingCommands.entries()) {
					if (commandInfo.timeout) {
						clearTimeout(commandInfo.timeout);
					}
				}
				this.localPendingCommands.clear();
				console.log('清理待处理指令');
			}
		},

		/**
		 * Cleanup timeouts
		 */
		cleanupTimeouts() {
			if (this.commandTimeoutHandle) {
				clearTimeout(this.commandTimeoutHandle);
				this.commandTimeoutHandle = null;
			}
		},

		/**
		 * Start log listening state
		 */
		startLogListening() {
			console.log('开始日志监听状态');
			this.logListening = true;
			this.isFirstLogPacket = true;
			this.isRetrievingLogs = true;
		},

		/**
		 * Stop log listening state
		 */
		stopLogListening() {
			console.log('停止日志监听状态');
			this.logListening = false;
			this.isRetrievingLogs = false;
			this.cleanupLogMonitoring();
		},

		/**
		 * Stop current log session manually
		 */
		stopCurrentLogSession() {
			console.log('用户手动停止日志监听');

			// Stop log listening
			this.logListening = false;
			this.isRetrievingLogs = false;

			// Clean up monitoring
			this.cleanupLogMonitoring();

			// Show completion message
			this.showStatusMessage('日志监听已停止', 'info', 3000);
			this.showCustomToast({
				message: '已停止日志监听',
				type: 'default'
			});

			// Log session statistics
			if (this.hasReceivedLogData) {
				console.log(`日志会话统计: 条目数=${this.logEntryCount}, 总大小=${this.logContent.length}字符`);
			}
		},

		/**
		 * Clear all log content
		 */
		clearLogs() {
			console.log('清空日志内容');

			// Confirm with user
			uni.showModal({
				title: '确认清空',
				content: '确定要清空所有日志内容吗？此操作不可撤销。',
				showCancel: true,
				cancelText: '取消',
				confirmText: '清空',
				success: (res) => {
					if (res.confirm) {
						// Clear log content and reset counters
						this.logContent = '';
						this.logEntryCount = 0;
						this.logUpdateCounter++;

						// Reset scroll position
						this.scrollTop = 0;

						// Show confirmation
						this.showCustomToast({
							message: '日志已清空',
							type: 'default'
						});

						console.log('日志内容已清空');
					}
				}
			});
		},

		setupBluetoothListener() {
			// Extend existing onBLECharacteristicValueChange listener to handle log data
			uni.onBLECharacteristicValueChange((res) => {
				if (res.value) {
					console.log('收到蓝牙数据，长度:', res.value.byteLength, '日志监听状态:', this.logListening);

					// First try to handle as global command response
					if (this.tryHandleGlobalCommandResponse(res.value)) {
						console.log('数据包已由全局响应处理机制处理');
						return;
					}

					// Add logic to differentiate between log data and other command responses
					// If we're actively listening for logs, handle as log data
					if (this.logListening && this.isLogStreamingData(res.value)) {
						console.log('识别为日志流数据，进行日志处理');
						this.handleLogData(res.value);
						return;
					}

					// If not log data and not handled globally, log for debugging
					if (!this.logListening) {
						console.log('未在日志监听状态，忽略数据包');
					} else {
						console.log('数据包不符合日志流格式，可能是其他响应');
					}
				}
			});
		},

		/**
		 * Determine if received data is log streaming data
		 * This helps differentiate between log data and other command responses
		 * @param {ArrayBuffer} buffer - The received data buffer
		 * @returns {boolean} - True if this appears to be log streaming data
		 */
		isLogStreamingData(buffer) {
			try {
				const uint8Array = new Uint8Array(buffer);

				// 打印原始数据用于调试
				const hexString = Array.from(uint8Array).map(b => b.toString(16).padStart(2, '0').toUpperCase()).join(' ');
				console.log(`🔍 收到数据包调试信息:`, {
					length: uint8Array.length,
					hex: hexString,
					isFirstPacket: this.isFirstLogPacket,
					logListening: this.logListening
				});

				// 尝试转换为字符串看看是否有可读内容
				try {
					const textContent = this.bufferToString(uint8Array);
					if (textContent && textContent.length > 0) {
						console.log(`📝 数据包文本内容: "${textContent}"`);
					}
				} catch (e) {
					console.log('📝 数据包无法转换为文本');
				}

				// For first log packet, check for various log command response headers
				if (this.isFirstLogPacket) {
					// Check for new format: A5 FE FF FF 22
					if (uint8Array.length >= 5 &&
						uint8Array[0] === 0xA5 &&
						uint8Array[1] === 0xFE &&
						uint8Array[2] === 0xFF &&
						uint8Array[3] === 0xFF &&
						uint8Array[4] === 0x22) {
						console.log('✅ 检测到新格式日志响应包头部: A5FEFFFF22');
						return true;
					}

					// Check for legacy format: A5 FE 22 FF FF
					if (uint8Array.length >= 5 &&
						uint8Array[0] === 0xA5 &&
						uint8Array[1] === 0xFE &&
						uint8Array[2] === 0x22 &&
						uint8Array[3] === 0xFF &&
						uint8Array[4] === 0xFF) {
						console.log('✅ 检测到旧格式日志响应包头部: A5FE22FFFF');
						return true;
					}

					// Check for standard command response: A5 FE 22 [length]
					if (uint8Array.length >= 3 &&
						uint8Array[0] === 0xA5 &&
						uint8Array[1] === 0xFE &&
						uint8Array[2] === 0x22) {
						console.log('✅ 检测到标准日志响应包头部: A5FE22');
						return true;
					}

					// 如果正在监听日志，即使头部不匹配也尝试处理
					if (this.logListening) {
						console.log('⚠️ 头部不匹配但正在监听日志，尝试处理');
						return true;
					}
				} else {
					// For subsequent packets, any data received while log listening is considered log data
					if (this.logListening) {
						console.log('✅ 后续日志数据包，直接处理');
						return true;
					}
				}

				console.log('❌ 数据包不符合日志流格式');
				return false;
			} catch (error) {
				console.error('判断日志数据类型失败:', error);
				return false;
			}
		},

		/**
		 * Convert buffer to string
		 * @param {Uint8Array} uint8Array - Buffer to convert
		 * @returns {string} - Converted string
		 */
		bufferToString(uint8Array) {
			try {
				// Try UTF-8 decoding first
				const decoder = new TextDecoder('utf-8');
				return decoder.decode(uint8Array);
			} catch (error) {
				// Fallback to simple character conversion
				try {
					return String.fromCharCode.apply(null, uint8Array);
				} catch (fallbackError) {
					console.error('Buffer to string conversion failed:', fallbackError);
					return '';
				}
			}
		},

		/**
		 * Convert ArrayBuffer to hex string for debugging
		 * @param {ArrayBuffer} buffer - Buffer to convert
		 * @returns {string} - Hex string representation
		 */
		arrayBufferToHexString(buffer) {
			const uint8Array = new Uint8Array(buffer);
			return Array.from(uint8Array).map(b => b.toString(16).padStart(2, '0').toUpperCase()).join(' ');
		},

		tryHandleGlobalCommandResponse(buffer) {
			// Try to use global response handling from bluetoothConnect page
			const pages = getCurrentPages();
			let bluetoothPage = pages.find(page =>
				page.route === 'pages/bluetooth/bluetoothConnect' ||
				page.route.includes('bluetoothConnect')
			);

			if (bluetoothPage && bluetoothPage.tryHandleGlobalCommandResponse) {
				return bluetoothPage.tryHandleGlobalCommandResponse(buffer);
			}

			// Fallback: try local handling
			return this.tryHandleLocalCommandResponse(buffer);
		},

		tryHandleLocalCommandResponse(buffer) {
			try {
				const uint8Array = new Uint8Array(buffer);

				// Check minimum length and header
				if (uint8Array.length < 6 || uint8Array[0] !== 0xA5 || uint8Array[1] !== 0xFE) {
					return false;
				}

				const commandCode = uint8Array[2];
				const dataLength = (uint8Array[3] << 8) | uint8Array[4];

				// Check if we have a matching local pending command
				if (this.localPendingCommands) {
					for (const [commandId, commandInfo] of this.localPendingCommands.entries()) {
						if (commandInfo.commandCode === commandCode) {
							// Clear timeout and remove from pending
							clearTimeout(commandInfo.timeout);
							this.localPendingCommands.delete(commandId);

							// Extract status code and response data
							const statusCode = dataLength > 0 ? uint8Array[5] : 0x00;
							const responseData = dataLength > 1 ? uint8Array.slice(6, 5 + dataLength) : new Uint8Array(0);

							console.log(`本地处理指令响应: 代码=0x${commandCode.toString(16).toUpperCase()}, 状态=0x${statusCode.toString(16).toUpperCase()}`);

							// Call callback with result
							if (commandInfo.callback) {
								commandInfo.callback({
									success: statusCode === 0x00,
									statusCode: statusCode,
									data: responseData,
									message: statusCode === 0x00 ? '指令执行成功' : '指令执行失败'
								});
							}

							return true;
						}
					}
				}

				return false;
			} catch (error) {
				console.error('本地响应处理异常:', error);
				return false;
			}
		},

		handleLogData(buffer) {
			try {
				// Only process log data if we're actively listening
				if (!this.logListening) {
					console.log('不在日志监听状态，忽略数据包');
					return;
				}

				// Validate buffer before processing
				if (!buffer || buffer.byteLength === 0) {
					console.warn('收到空数据包，跳过处理');
					this.handleMalformedLogData('收到空数据包');
					return;
				}

				const uint8Array = new Uint8Array(buffer);
				console.log('收到数据包，长度:', uint8Array.length, '十六进制:', this.arrayBufferToHexString(buffer));

				// Update log data reception timestamp for completion detection
				this.updateLogDataReception();

				if (this.isFirstLogPacket) {
					// First packet: parse and check for header (A5FEFFFF22)
					const logData = this.parseFirstLogPacket(uint8Array);
					if (logData !== null) {
						// For new format (A5FEFFFF22), first packet only contains header
						// logData will be empty string, which is expected
						if (logData.length === 0) {
							console.log('收到新格式日志头部 A5FEFFFF22，等待后续JSON数据');
							this.isFirstLogPacket = false;

							// Update loading state - header received successfully
							if (this.isRetrievingLogs) {
								this.isRetrievingLogs = false; // Stop showing loading state on button
								this.showStatusMessage('已收到日志头部，等待数据...', 'success', 0);
								this.showCustomToast({
									message: '开始接收日志数据...',
									type: 'default'
								});
							}

							// Initialize empty log content for new format
							this.logContent = '';
						} else {
							// Legacy format with data in first packet
							// Process log data with timestamp formatting if needed
							const processedLogData = this.processLogDataWithTimestamp(logData, true);

							// Initialize log content with first packet data
							this.logContent = processedLogData;
							this.isFirstLogPacket = false;
							this.logEntryCount++;

							// Update loading state - first packet received successfully
							if (this.isRetrievingLogs) {
								this.isRetrievingLogs = false; // Stop showing loading state on button
								this.showStatusMessage('正在接收日志数据...', 'success', 0);
								this.showCustomToast({
									message: '开始接收日志数据...',
									type: 'default'
								});
							}

							console.log('收到首个日志数据包，内容长度:', logData.length, '前100字符:', logData.substring(0, 100));

							// Manage large log content to prevent memory issues
							this.manageLargeLogContent();

							// Trigger real-time display update and auto-scroll
							this.updateLogDisplay();
						}
					} else {
						console.warn('首个数据包解析失败，可能格式不正确');
						this.handleMalformedLogData('首个数据包格式不正确');
						// Don't mark as failed, continue listening for more packets
					}
				} else {
					// Subsequent packets: append raw data directly without header processing
					const additionalData = this.parseSubsequentLogPacket(uint8Array);
					if (additionalData !== null && additionalData.length > 0) {
						// Process additional data with timestamp formatting if needed
						const processedAdditionalData = this.processLogDataWithTimestamp(additionalData, false);

						// Append to existing log content
						this.logContent += processedAdditionalData;
						this.logEntryCount++;

						console.log('收到后续日志数据，长度:', additionalData.length, '总日志长度:', this.logContent.length);

						// Manage large log content to prevent memory issues
						this.manageLargeLogContent();

						// Trigger real-time display update and auto-scroll
						this.updateLogDisplay();
					} else {
						console.log('后续数据包为空或解析失败，继续监听');
						// For subsequent packets, empty data might be normal, don't treat as error
					}
				}

			} catch (error) {
				console.error('处理日志数据失败:', error);
				this.handleLogDataProcessingError(error);

				// Don't stop listening on parsing errors, continue processing subsequent packets
			}
		},

		/**
		 * Handle malformed log data packets gracefully
		 * @param {string} reason - Reason for the malformed data
		 */
		handleMalformedLogData(reason) {
			console.warn('处理异常日志数据:', reason);

			// Count malformed packets to detect persistent issues
			this.malformedPacketCount = (this.malformedPacketCount || 0) + 1;

			// If too many malformed packets, show warning but continue
			if (this.malformedPacketCount >= 5) {
				this.showStatusMessage('检测到多个异常数据包，日志可能不完整', 'warning', 3000);

				// Reset counter to avoid spam
				this.malformedPacketCount = 0;
			}

			// Log the issue for debugging but don't interrupt the user experience
			console.log(`异常数据包计数: ${this.malformedPacketCount}`);
		},

		/**
		 * Handle log data processing errors
		 * @param {Error} error - The error that occurred during processing
		 */
		handleLogDataProcessingError(error) {
			console.error('日志数据处理错误:', error);

			// Determine error type and show appropriate message
			let errorMessage = '日志数据处理失败';
			let shouldStopListening = false;

			if (error.name === 'TypeError') {
				errorMessage = '数据格式错误，继续尝试接收';
			} else if (error.name === 'RangeError') {
				errorMessage = '数据范围错误，可能数据包过大';
			} else if (error.message && error.message.includes('memory')) {
				errorMessage = '内存不足，已清理部分日志';
				// Clear some log content to free memory
				this.handleMemoryPressure();
			} else {
				errorMessage = `处理错误: ${error.message || '未知错误'}`;
				// For unknown errors, consider stopping if they persist
				this.processingErrorCount = (this.processingErrorCount || 0) + 1;
				if (this.processingErrorCount >= 3) {
					shouldStopListening = true;
					errorMessage = '连续处理错误，已停止日志监听';
				}
			}

			this.showStatusMessage(errorMessage, 'warning', 3000);
			this.showCustomToast({
				message: errorMessage,
				type: 'warning'
			});

			// Stop listening if errors are too frequent
			if (shouldStopListening) {
				this.stopLogListening();
				this.processingErrorCount = 0; // Reset counter
			}
		},

		/**
		 * Handle memory pressure by clearing old log content
		 */
		handleMemoryPressure() {
			try {
				const maxEmergencySize = 50000; // 50KB emergency limit
				if (this.logContent.length > maxEmergencySize) {
					const keepSize = 10000; // Keep last 10KB
					this.logContent = '...[内存不足，已清理早期日志]...\n' +
						this.logContent.substring(this.logContent.length - keepSize);
					this.logUpdateCounter++;
					console.log('内存压力处理：清理日志内容，新长度:', this.logContent.length);
				}
			} catch (error) {
				console.error('内存压力处理失败:', error);
				// As last resort, clear all logs
				this.logContent = '[内存不足，日志已清空，请重新获取]';
				this.logUpdateCounter++;
			}
		},

		parseFirstLogPacket(uint8Array) {
			try {
				// Enhanced validation for first log packet
				if (!uint8Array || uint8Array.length === 0) {
					console.warn('首个数据包为空');
					return null;
				}

				// Check minimum length for new header A5FEFFFF22 (5 bytes)
				if (uint8Array.length < 5) {
					console.warn('首个数据包长度不足，期望至少5字节，实际:', uint8Array.length);
					return null;
				}

				// Verify new header format: A5 FE FF FF 22 (new log response format)
				if (uint8Array[0] === 0xA5 &&
					uint8Array[1] === 0xFE &&
					uint8Array[2] === 0xFF &&
					uint8Array[3] === 0xFF &&
					uint8Array[4] === 0x22) {

					console.log('检测到新日志响应头部: A5FEFFFF22');

					// For the new format, the first packet only contains the header
					// The actual log data will come in subsequent packets
					// Return empty string to indicate header received successfully
					return '';

				} else if (uint8Array[0] === 0xA5 &&
					uint8Array[1] === 0xFE &&
					uint8Array[2] === 0x22 &&
					uint8Array[3] === 0xFF &&
					uint8Array[4] === 0xFF) {

					// Legacy format: A5 FE 22 FF FF (old log response format)
					console.log('检测到旧日志响应头部: A5FE22FFFF');

					// Extract data after the 5-byte header (A5FE22FFFF)
					const logDataBuffer = uint8Array.slice(5);
					const logText = this.bufferToString(logDataBuffer);

					console.log('从首个数据包提取日志数据，原始长度:', logDataBuffer.length, '文本长度:', logText.length);
					return logText;

				} else if (uint8Array[0] === 0xA5 &&
					uint8Array[1] === 0xFE &&
					uint8Array[2] === 0x22) {

					// Alternative format: A5 FE 22 + data length + data (standard command response)
					const dataLength = (uint8Array[3] << 8) | uint8Array[4];
					console.log('检测到标准指令响应头部: A5FE22，数据长度:', dataLength);

					// Validate data length to prevent buffer overflow
					if (dataLength > 65535) { // Reasonable maximum
						console.warn('数据长度异常大:', dataLength, '可能是数据包损坏');
						return this.handleCorruptedPacket(uint8Array);
					}

					// Extract data after the 5-byte header, considering actual data length
					const headerSize = 5;
					const availableDataLength = uint8Array.length - headerSize;
					const actualDataLength = Math.min(dataLength, availableDataLength);

					if (actualDataLength <= 0) {
						console.warn('计算的数据长度为0或负数');
						return '';
					}

					const logDataBuffer = uint8Array.slice(headerSize, headerSize + actualDataLength);
					const logText = this.bufferToString(logDataBuffer);

					console.log('从首个数据包提取日志数据，声明长度:', dataLength, '实际长度:', actualDataLength, '文本长度:', logText.length);
					return logText;

				} else {
					console.warn('首个数据包头部格式不匹配，期望A5FEFFFF22、A5FE22FFFF或A5FE22+长度');
					console.log('实际头部:', this.arrayBufferToHexString(uint8Array.slice(0, Math.min(8, uint8Array.length))));

					// Enhanced fallback handling for edge cases
					return this.handleUnknownPacketFormat(uint8Array);
				}

			} catch (error) {
				console.error('解析首个日志数据包失败:', error);
				// Enhanced error handling - try to salvage what we can
				return this.handlePacketParsingError(uint8Array, error);
			}
		},

		/**
		 * Handle corrupted packet data gracefully
		 * @param {Uint8Array} uint8Array - The corrupted packet data
		 * @returns {string|null} - Salvaged data or null
		 */
		handleCorruptedPacket(uint8Array) {
			console.warn('尝试处理损坏的数据包');

			try {
				// Try to find any readable text in the packet
				const readableText = this.extractReadableText(uint8Array);
				if (readableText && readableText.length > 0) {
					console.log('从损坏数据包中提取到可读文本，长度:', readableText.length);
					return '[数据包可能损坏，部分内容]\n' + readableText;
				}
			} catch (error) {
				console.error('处理损坏数据包失败:', error);
			}

			return null;
		},

		/**
		 * Handle unknown packet format
		 * @param {Uint8Array} uint8Array - The packet with unknown format
		 * @returns {string|null} - Extracted data or null
		 */
		handleUnknownPacketFormat(uint8Array) {
			console.log('尝试处理未知格式数据包');

			try {
				// Try to extract data anyway in case it's a different format
				const fallbackText = this.bufferToString(uint8Array);

				// Check if the extracted text seems reasonable
				if (fallbackText && fallbackText.length > 0) {
					const printableRatio = this.calculatePrintableRatio(fallbackText);
					if (printableRatio > 0.3) { // Lower threshold for unknown formats
						console.log('未知格式数据包处理成功，可打印字符比例:', printableRatio);
						return '[未知格式数据包]\n' + fallbackText;
					}
				}

				// If not readable as text, show hex dump for debugging
				const hexDump = this.createHexDump(uint8Array);
				console.log('数据包无法解析为文本，显示十六进制转储');
				return '[二进制数据包]\n' + hexDump;

			} catch (error) {
				console.error('处理未知格式数据包失败:', error);
				return null;
			}
		},

		/**
		 * Handle packet parsing errors
		 * @param {Uint8Array} uint8Array - The packet data
		 * @param {Error} error - The parsing error
		 * @returns {string|null} - Salvaged data or null
		 */
		handlePacketParsingError(uint8Array, error) {
			console.error('数据包解析错误，尝试恢复:', error.message);

			try {
				// Try basic text extraction as last resort
				const basicText = this.extractReadableText(uint8Array);
				if (basicText && basicText.length > 0) {
					return '[解析错误，部分恢复]\n' + basicText;
				}

				// If all else fails, provide hex representation for debugging
				const hexString = this.arrayBufferToHexString(uint8Array.buffer);
				return `[解析失败: ${error.message}]\n十六进制: ${hexString}`;

			} catch (recoveryError) {
				console.error('数据包恢复也失败:', recoveryError);
				return null;
			}
		},

		/**
		 * Extract readable text from binary data
		 * @param {Uint8Array} uint8Array - Binary data
		 * @returns {string} - Readable text portion
		 */
		extractReadableText(uint8Array) {
			let readableText = '';

			for (let i = 0; i < uint8Array.length; i++) {
				const byte = uint8Array[i];
				if (this.isPrintableChar(byte)) {
					readableText += String.fromCharCode(byte);
				} else if (byte === 0x0A || byte === 0x0D) {
					readableText += String.fromCharCode(byte); // Keep line breaks
				} else {
					// Replace non-printable with space if surrounded by printable chars
					if (i > 0 && i < uint8Array.length - 1 &&
						this.isPrintableChar(uint8Array[i - 1]) &&
						this.isPrintableChar(uint8Array[i + 1])) {
						readableText += ' ';
					}
				}
			}

			return readableText.trim();
		},

		/**
		 * Create hex dump representation of binary data
		 * @param {Uint8Array} uint8Array - Binary data
		 * @returns {string} - Formatted hex dump
		 */
		createHexDump(uint8Array) {
			const bytesPerLine = 16;
			let hexDump = '';

			for (let i = 0; i < uint8Array.length; i += bytesPerLine) {
				const lineBytes = uint8Array.slice(i, i + bytesPerLine);
				const hexPart = Array.from(lineBytes)
					.map(b => b.toString(16).padStart(2, '0'))
					.join(' ');
				const textPart = Array.from(lineBytes)
					.map(b => this.isPrintableChar(b) ? String.fromCharCode(b) : '.')
					.join('');

				hexDump += `${i.toString(16).padStart(4, '0')}: ${hexPart.padEnd(48, ' ')} |${textPart}|\n`;
			}

			return hexDump;
		},

		parseSubsequentLogPacket(uint8Array) {
			try {
				// Enhanced validation for subsequent packets
				if (!uint8Array || uint8Array.length === 0) {
					console.log('后续数据包为空，跳过处理');
					return '';
				}

				// For subsequent packets, treat all data as JSON log content
				const rawText = this.bufferToString(uint8Array);

				console.log('解析后续日志数据包，原始长度:', uint8Array.length, '文本长度:', rawText.length);

				// Enhanced validation for subsequent packets
				if (rawText.length === 0) {
					console.log('后续数据包转换为空文本，可能是纯二进制数据');
					// For subsequent packets, even binary data might be valid log content
					// Try to create a readable representation
					const readableContent = this.handleBinaryLogData(uint8Array);
					return readableContent;
				}

				// Try to format JSON log data for better readability
				const formattedLogText = this.formatJsonLogData(rawText);

				// Check for potential data corruption in subsequent packets
				if (this.detectDataCorruption(formattedLogText)) {
					console.warn('检测到后续数据包可能损坏');
					return this.handleCorruptedSubsequentPacket(uint8Array, formattedLogText);
				}

				return formattedLogText;

			} catch (error) {
				console.error('解析后续日志数据包失败:', error);
				// Enhanced error handling for subsequent packets
				return this.handleSubsequentPacketError(uint8Array, error);
			}
		},

		/**
		 * Format JSON log data for better readability
		 * @param {string} rawText - Raw text that may contain JSON
		 * @returns {string} - Formatted log text
		 */
		formatJsonLogData(rawText) {
			try {
				// Check if the text contains JSON data
				if (this.isJsonString(rawText)) {
					const jsonData = JSON.parse(rawText);

					// Format the JSON data for better readability
					const formattedJson = this.formatLogJsonObject(jsonData);

					// Add timestamp if enabled
					if (this.enableTimestamps) {
						const timestamp = this.formatTimestamp(new Date());
						return `[${timestamp}] ${formattedJson}\n`;
					} else {
						return `${formattedJson}\n`;
					}
				} else {
					// Not JSON, return as-is with optional timestamp
					if (this.enableTimestamps) {
						const timestamp = this.formatTimestamp(new Date());
						return `[${timestamp}] ${rawText}\n`;
					} else {
						return `${rawText}\n`;
					}
				}
			} catch (error) {
				console.error('格式化JSON日志数据失败:', error);
				// Return raw text if formatting fails
				return `${rawText}\n`;
			}
		},

		/**
		 * Check if a string is valid JSON
		 * @param {string} str - String to check
		 * @returns {boolean} - True if valid JSON
		 */
		isJsonString(str) {
			try {
				JSON.parse(str);
				return true;
			} catch (e) {
				return false;
			}
		},

		/**
		 * Format a JSON object for log display
		 * @param {Object} jsonObj - JSON object to format
		 * @returns {string} - Formatted string
		 */
		formatLogJsonObject(jsonObj) {
			try {
				// Handle the specific log format from the device
				if (jsonObj.type && jsonObj.timestamp && jsonObj.deviceId && jsonObj.param) {
					let formatted = `类型: ${jsonObj.type}\n`;
					formatted += `时间戳: ${jsonObj.timestamp} (${this.formatUnixTimestamp(jsonObj.timestamp)})\n`;
					formatted += `消息ID: ${jsonObj.mid || 'N/A'}\n`;
					formatted += `设备ID: ${jsonObj.deviceId}\n`;

					// Format parameter data
					if (Array.isArray(jsonObj.param) && jsonObj.param.length > 0) {
						formatted += `参数数据:\n`;
						jsonObj.param.forEach((param, index) => {
							formatted += `  [${index + 1}] `;
							if (typeof param === 'object') {
								// Format each parameter field
								Object.keys(param).forEach(key => {
									const value = param[key];
									let displayValue = value;

									// Special formatting for specific fields
									if (key === 'collectTime') {
										displayValue = value === 0 ? '实时' : this.formatUnixTimestamp(value);
									} else if (key === 'batteryVoltage') {
										displayValue = `${value}mV`;
									} else if (key === 'temperature') {
										displayValue = `${value}°C`;
									} else if (key === 'batteryValue') {
										displayValue = `${value}%`;
									} else if (key === 'rsrp') {
										displayValue = `${value}dBm`;
									} else if (key === 'CH4Val') {
										displayValue = `${value}%LEL`;
									}

									formatted += `${key}: ${displayValue}, `;
								});
								formatted = formatted.slice(0, -2) + '\n'; // Remove last comma and add newline
							} else {
								formatted += `${param}\n`;
							}
						});
					}

					return formatted;
				} else {
					// Generic JSON formatting
					return JSON.stringify(jsonObj, null, 2);
				}
			} catch (error) {
				console.error('格式化JSON对象失败:', error);
				return JSON.stringify(jsonObj);
			}
		},

		/**
		 * Format Unix timestamp to readable date
		 * @param {number} timestamp - Unix timestamp
		 * @returns {string} - Formatted date string
		 */
		formatUnixTimestamp(timestamp) {
			try {
				const date = new Date(timestamp * 1000); // Convert to milliseconds
				return date.toLocaleString('zh-CN', {
					year: 'numeric',
					month: '2-digit',
					day: '2-digit',
					hour: '2-digit',
					minute: '2-digit',
					second: '2-digit'
				});
			} catch (error) {
				console.error('格式化时间戳失败:', error);
				return timestamp.toString();
			}
		},

		/**
		 * Handle binary log data in subsequent packets
		 * @param {Uint8Array} uint8Array - Binary data
		 * @returns {string} - Readable representation
		 */
		handleBinaryLogData(uint8Array) {
			try {
				// Try to extract any readable portions
				const readableText = this.extractReadableText(uint8Array);

				if (readableText && readableText.length > 0) {
					console.log('从二进制数据中提取到可读文本');
					return readableText;
				}

				// If no readable text, create a compact hex representation
				const hexString = Array.from(uint8Array)
					.map(b => b.toString(16).padStart(2, '0'))
					.join('');

				return `[二进制数据: ${hexString}]\n`;

			} catch (error) {
				console.error('处理二进制日志数据失败:', error);
				return '[二进制数据处理失败]\n';
			}
		},

		/**
		 * Detect potential data corruption in log text
		 * @param {string} logText - The log text to check
		 * @returns {boolean} - True if corruption is detected
		 */
		detectDataCorruption(logText) {
			// Check for signs of data corruption
			const corruptionIndicators = [
				// Too many null characters
				(logText.match(/\0/g) || []).length > logText.length * 0.1,
				// Too many non-printable characters
				this.calculatePrintableRatio(logText) < 0.5,
				// Suspicious repeated patterns
				/(.)\1{20,}/.test(logText), // Same character repeated 20+ times
				// Invalid UTF-8 sequences (rough check)
				logText.includes('\uFFFD') // Replacement character
			];

			return corruptionIndicators.some(indicator => indicator);
		},

		/**
		 * Handle corrupted subsequent packet
		 * @param {Uint8Array} uint8Array - Original packet data
		 * @param {string} logText - Corrupted text
		 * @returns {string} - Cleaned or alternative representation
		 */
		handleCorruptedSubsequentPacket(uint8Array, logText) {
			console.log('处理损坏的后续数据包');

			try {
				// Try to clean the text
				const cleanedText = this.cleanCorruptedText(logText);
				if (cleanedText && cleanedText.length > 0) {
					return '[数据可能损坏]\n' + cleanedText;
				}

				// Fall back to binary representation
				return this.handleBinaryLogData(uint8Array);

			} catch (error) {
				console.error('处理损坏后续数据包失败:', error);
				return '[数据包损坏，无法处理]\n';
			}
		},

		/**
		 * Clean corrupted text by removing problematic characters
		 * @param {string} text - Corrupted text
		 * @returns {string} - Cleaned text
		 */
		cleanCorruptedText(text) {
			try {
				// Remove null characters
				let cleaned = text.replace(/\0/g, '');

				// Replace replacement characters
				cleaned = cleaned.replace(/\uFFFD/g, '?');

				// Remove excessive repeated characters
				cleaned = cleaned.replace(/(.)\1{10,}/g, '$1...[重复]');

				// Keep only printable characters and common whitespace
				cleaned = cleaned.replace(/[^\x20-\x7E\x0A\x0D\x09]/g, '?');

				return cleaned.trim();

			} catch (error) {
				console.error('清理损坏文本失败:', error);
				return '';
			}
		},

		/**
		 * Handle errors in subsequent packet processing
		 * @param {Uint8Array} uint8Array - Original packet data
		 * @param {Error} error - The error that occurred
		 * @returns {string} - Error representation or salvaged data
		 */
		handleSubsequentPacketError(uint8Array, error) {
			console.error('后续数据包处理错误:', error.message);

			try {
				// Try to salvage some data
				if (uint8Array && uint8Array.length > 0) {
					const salvaged = this.extractReadableText(uint8Array);
					if (salvaged && salvaged.length > 0) {
						return `[处理错误，部分恢复: ${error.message}]\n${salvaged}\n`;
					}
				}

				// Return error information for debugging
				return `[后续数据包处理失败: ${error.message}]\n`;

			} catch (recoveryError) {
				console.error('后续数据包错误恢复失败:', recoveryError);
				return '[数据包处理完全失败]\n';
			}
		},

		updateLogDisplay() {
			// Optimize text rendering performance for large log content
			const currentTime = Date.now();

			// Throttle rendering updates to improve performance
			if (this.renderOptimizationEnabled &&
				currentTime - this.lastRenderTime < this.renderThrottleDelay) {
				// Skip this update to prevent excessive rendering
				return;
			}

			this.lastRenderTime = currentTime;

			// Trigger real-time log content updates to the display text area
			// Increment counter to force reactivity and ensure UI updates
			this.logUpdateCounter++;

			// Use nextTick to ensure DOM is updated before scrolling
			this.$nextTick(() => {
				// Implement auto-scrolling to show latest log entries
				this.autoScrollToBottom();
			});

			// Also force a small delay to ensure scroll-view updates properly
			setTimeout(() => {
				this.autoScrollToBottom();
			}, 50);
		},

		clearLogs() {
			if (!this.logContent) {
				this.showCustomToast({
					message: '日志内容为空，无需清空',
					type: 'warning'
				});
				return;
			}

			this.logContent = '';
			this.scrollTop = 0;
			this.logUpdateCounter++;
			this.isFirstLogPacket = true; // Reset for next log retrieval

			// Clear status message when logs are cleared
			this.clearStatusMessage();

			this.showCustomToast({
				message: '日志已清空',
				type: 'default'
			});

			console.log('日志内容已清空，重置状态');
		},

		/**
		 * Stop current log streaming session
		 * This allows users to manually stop log streaming
		 */
		stopCurrentLogSession() {
			if (this.logListening || this.isRetrievingLogs) {
				this.stopLogListening();
				this.showStatusMessage('日志监听已停止', 'warning');
				this.showCustomToast({
					message: '日志监听已停止',
					type: 'default'
				});
				console.log('用户手动停止日志监听');
			} else {
				console.log('当前没有活跃的日志监听会话');
			}
		},

		manageLargeLogContent() {
			// Enhanced log content size limits to prevent memory issues
			const maxLogSize = 200000; // 200KB limit for log content (increased from 100KB)
			const trimSize = 50000;    // Keep last 50KB when trimming (increased from 20KB)
			const warningSize = 150000; // Show warning at 150KB

			// Show warning when approaching limit
			if (this.logContent.length > warningSize && this.logContent.length <= maxLogSize) {
				if (!this.logSizeWarningShown) {
					this.showStatusMessage('日志内容较大，接近内存限制', 'warning', 3000);
					this.logSizeWarningShown = true;
				}
			}

			if (this.logContent.length > maxLogSize) {
				console.log('日志内容过大，进行裁剪。当前长度:', this.logContent.length);

				// Add timestamp to trimmed marker for better tracking
				const timestamp = this.formatTimestamp(new Date());
				const trimmedContent = `...[${timestamp} 日志内容过长，已自动裁剪前面部分，保留最近 ${Math.round(trimSize / 1000)}KB]...\n` +
					this.logContent.substring(this.logContent.length - trimSize);

				this.logContent = trimmedContent;
				this.logUpdateCounter++;
				this.logSizeWarningShown = false; // Reset warning flag

				console.log('日志内容已裁剪，新长度:', this.logContent.length);

				// Show notification to user with more details
				this.showCustomToast({
					message: `日志内容过长(${Math.round(maxLogSize / 1000)}KB+)，已自动裁剪`,
					type: 'warning'
				});

				// Trigger garbage collection hint if available
				this.triggerMemoryOptimization();
			}
		},

		/**
		 * Implement state management to track log streaming status
		 * Stop log listening and reset related states
		 */
		stopLogListening() {
			console.log('停止日志监听，当前状态:', {
				logListening: this.logListening,
				isRetrievingLogs: this.isRetrievingLogs,
				isFirstLogPacket: this.isFirstLogPacket
			});

			// Update state management to track log streaming status
			this.logListening = false;
			this.isRetrievingLogs = false;

			// Reset first packet flag for next log session
			this.isFirstLogPacket = true;

			console.log('日志监听已停止');
		},

		/**
		 * Start log listening with proper state management
		 */
		startLogListening() {
			console.log('开始日志监听');

			// Implement state management to track log streaming status
			this.logListening = true;
			this.isRetrievingLogs = true;
			this.isFirstLogPacket = true;

			console.log('日志监听状态已更新:', {
				logListening: this.logListening,
				isRetrievingLogs: this.isRetrievingLogs,
				isFirstLogPacket: this.isFirstLogPacket
			});
		},

		/**
		 * Clean up Bluetooth listener when navigating away
		 * This ensures proper cleanup when user navigates away from log page
		 */
		cleanupBluetoothListener() {
			try {
				// Note: uni.offBLECharacteristicValueChange() would remove ALL listeners
				// Since other pages might also be listening, we don't call it here
				// Instead, we rely on the logListening flag to ignore data when not needed
				console.log('蓝牙监听器清理完成（通过状态标志控制）');
			} catch (error) {
				console.error('清理蓝牙监听器失败:', error);
			}
		},

		/**
		 * Setup connection monitoring to detect device disconnections
		 * This helps handle device disconnection scenarios during log streaming
		 */
		setupConnectionMonitoring() {
			// Monitor Bluetooth adapter state changes
			uni.onBluetoothAdapterStateChange((res) => {
				console.log('蓝牙适配器状态变化:', res);
				if (!res.available) {
					console.log('蓝牙适配器不可用，停止日志监听');
					this.handleBluetoothAdapterUnavailable();
				} else if (res.discovering !== undefined) {
					// Handle discovery state changes if needed
					console.log('蓝牙扫描状态:', res.discovering);
				}
			});

			// Monitor BLE connection state changes with enhanced handling
			uni.onBLEConnectionStateChange((res) => {
				console.log('BLE连接状态变化:', res);
				if (res.deviceId === this.currentDevice?.deviceId) {
					if (!res.connected) {
						console.log('设备连接断开:', res.deviceId);
						this.handleDeviceDisconnection();
					} else {
						console.log('设备重新连接:', res.deviceId);
						this.handleDeviceReconnection();
					}
				}
			});

			// Set up periodic connection health check
			this.startConnectionHealthCheck();
		},

		/**
		 * Handle device reconnection scenario
		 */
		handleDeviceReconnection() {
			console.log('设备重新连接，更新状态');
			this.isConnected = true;

			// Clear any error states
			this.clearStatusMessage();

			// Show reconnection message
			this.showStatusMessage('设备已重新连接', 'success', 3000);
			this.showCustomToast({
				message: '设备已重新连接',
				type: 'default'
			});
		},

		/**
		 * Start periodic connection health check
		 */
		startConnectionHealthCheck() {
			// Check connection health every 30 seconds when log listening is active
			this.connectionHealthInterval = setInterval(() => {
				if (this.logListening || this.isRetrievingLogs) {
					this.checkConnectionHealth();
				}
			}, 30000);
		},

		/**
		 * Check connection health
		 */
		checkConnectionHealth() {
			if (!this.currentDevice?.deviceId) {
				return;
			}

			try {
				// Try to get BLE device services to verify connection
				uni.getBLEDeviceServices({
					deviceId: this.currentDevice.deviceId,
					success: (res) => {
						// Connection is healthy
						console.log('连接健康检查通过');
					},
					fail: (err) => {
						console.warn('连接健康检查失败:', err);
						// Connection might be lost
						if (err.errCode === 10006 || err.errCode === 10004) {
							console.log('检测到连接丢失');
							this.handleDeviceDisconnection();
						}
					}
				});
			} catch (error) {
				console.error('连接健康检查异常:', error);
			}
		},

		/**
		 * Cleanup connection monitoring listeners
		 * This ensures proper cleanup when user navigates away from log page
		 */
		cleanupConnectionMonitoring() {
			try {
				// Clear connection health check interval
				if (this.connectionHealthInterval) {
					clearInterval(this.connectionHealthInterval);
					this.connectionHealthInterval = null;
					console.log('清理连接健康检查定时器');
				}

				// Note: uni.offBluetoothAdapterStateChange() and uni.offBLEConnectionStateChange()
				// would remove ALL listeners, which might affect other pages
				// The listeners will be automatically cleaned up when the page is destroyed
				console.log('连接监听清理完成');
			} catch (error) {
				console.error('清理连接监听失败:', error);
			}
		},

		cleanupPendingCommands() {
			// Clean up local pending commands
			if (this.localPendingCommands) {
				for (const [commandId, commandInfo] of this.localPendingCommands.entries()) {
					clearTimeout(commandInfo.timeout);
				}
				this.localPendingCommands.clear();
				console.log('清理本地待处理指令');
			}
		},

		/**
		 * Clean up timeout handles
		 */
		cleanupTimeouts() {
			if (this.commandTimeoutHandle) {
				clearTimeout(this.commandTimeoutHandle);
				this.commandTimeoutHandle = null;
				console.log('清理指令超时句柄');
			}

			if (this.connectionHealthInterval) {
				clearInterval(this.connectionHealthInterval);
				this.connectionHealthInterval = null;
				console.log('清理连接健康检查定时器');
			}
		},

		/**
		 * Handle device disconnection scenarios during log streaming
		 * This method should be called when device disconnection is detected
		 */
		handleDeviceDisconnection() {
			console.log('检测到设备断开连接，停止日志监听');

			// Stop log listening immediately
			this.stopLogListening();

			// Update connection status
			this.isConnected = false;

			// Show disconnection message with enhanced error handling
			this.showStatusMessage('设备连接已断开，日志监听已停止', 'error', 5000);
			this.showCustomToast({
				message: '设备已断开连接，日志监听已停止',
				type: 'warning'
			});

			// Clear any pending commands
			this.cleanupPendingCommands();

			// Additional cleanup for disconnection scenario
			this.handleDisconnectionCleanup();
		},

		/**
		 * Additional cleanup operations when device disconnects
		 * Ensures proper state reset and resource cleanup
		 */
		handleDisconnectionCleanup() {
			try {
				// Reset log streaming state completely
				this.isFirstLogPacket = true;
				this.logListening = false;
				this.isRetrievingLogs = false;

				// Clear any status messages related to log operations
				this.clearStatusMessage();

				// Log the disconnection event for debugging
				console.log('设备断开连接清理完成，状态已重置');

			} catch (error) {
				console.error('设备断开连接清理失败:', error);
				// Don't show user error for cleanup failures, just log them
			}
		},

		/**
		 * Validate connection before allowing log operations
		 * Enhanced validation with comprehensive error checking
		 * @returns {boolean} - True if device is connected and ready for log operations
		 */
		validateConnectionForLogOperations() {
			// Check basic connection status
			if (!this.isConnected) {
				this.showStatusMessage('设备未连接，无法进行日志操作', 'error');
				this.showCustomToast({
					message: '设备未连接，无法进行日志操作',
					type: 'error'
				});
				return false;
			}

			// Validate device information
			const deviceId = this.currentDevice?.deviceId;
			if (!deviceId) {
				this.showStatusMessage('设备信息缺失，请重新连接设备', 'error');
				this.showCustomToast({
					message: '设备信息缺失，请重新连接设备',
					type: 'error'
				});
				return false;
			}

			// Validate Bluetooth service and characteristic IDs
			const serviceId = uni.getStorageSync('writeServiceId');
			const characteristicId = uni.getStorageSync('writeCharacteristicId');

			if (!serviceId || !characteristicId) {
				this.showStatusMessage('蓝牙特征值信息不完整，请重新连接设备', 'error');
				this.showCustomToast({
					message: '蓝牙连接信息不完整，请重新连接设备',
					type: 'error'
				});
				return false;
			}

			// Check if Bluetooth adapter is still available
			try {
				uni.getBluetoothAdapterState({
					success: (res) => {
						if (!res.available) {
							console.log('蓝牙适配器不可用');
							this.handleBluetoothAdapterUnavailable();
						}
					},
					fail: (err) => {
						console.error('检查蓝牙适配器状态失败:', err);
						this.handleBluetoothAdapterError(err);
					}
				});
			} catch (error) {
				console.error('蓝牙适配器状态检查异常:', error);
				this.showCustomToast({
					message: '蓝牙状态检查失败，请重试',
					type: 'error'
				});
				return false;
			}

			return true;
		},

		/**
		 * Handle Bluetooth adapter unavailable scenario
		 */
		handleBluetoothAdapterUnavailable() {
			this.isConnected = false;
			this.stopLogListening();

			this.showStatusMessage('蓝牙适配器不可用，请检查设备蓝牙设置', 'error');
			this.showCustomToast({
				message: '蓝牙不可用，请开启设备蓝牙',
				type: 'error'
			});
		},

		/**
		 * Handle Bluetooth adapter errors
		 * @param {Object} error - The error object from Bluetooth API
		 */
		handleBluetoothAdapterError(error) {
			console.error('蓝牙适配器错误:', error);

			let errorMessage = '蓝牙操作失败';

			// Handle specific error codes
			if (error.errCode) {
				switch (error.errCode) {
					case 10001:
						errorMessage = '蓝牙未初始化，请重新连接设备';
						break;
					case 10002:
						errorMessage = '蓝牙适配器不可用，请检查设备蓝牙';
						break;
					case 10003:
						errorMessage = '连接失败，请检查设备状态';
						break;
					case 10004:
						errorMessage = '没有找到指定设备，请重新扫描';
						break;
					case 10005:
						errorMessage = '连接已断开，请重新连接';
						break;
					case 10006:
						errorMessage = '当前连接已断开，请重新连接';
						break;
					case 10007:
						errorMessage = '特征值操作失败，请重试';
						break;
					case 10008:
						errorMessage = '系统上报异常，请重启应用';
						break;
					case 10009:
						errorMessage = '系统版本过低，不支持BLE';
						break;
					default:
						errorMessage = error.errMsg || '未知蓝牙错误';
				}
			}

			this.showStatusMessage(errorMessage, 'error');
			this.showCustomToast({
				message: errorMessage,
				type: 'error'
			});
		},

		autoScrollToBottom() {
			// Implement auto-scrolling functionality to show latest log entries
			this.$nextTick(() => {
				// Calculate scroll position to bottom
				// Use a large number to ensure scrolling to the very bottom
				const maxScrollTop = 999999;

				// Update scroll position to trigger scroll to bottom
				// Use alternating values to ensure scroll event is triggered even with same content
				if (this.scrollTop >= maxScrollTop - 1) {
					this.scrollTop = maxScrollTop - 2;
				} else {
					this.scrollTop = maxScrollTop;
				}

				console.log('自动滚动到底部，scrollTop:', this.scrollTop, '日志内容长度:', this.logContent.length);
			});
		},

		// Global command response handling methods
		generateCommandId() {
			// Generate unique command ID for tracking
			return `log_cmd_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
		},

		registerPendingCommand(commandId, commandCode, commandType, callback, timeout = 10000) {
			// Try to use global response handling from bluetoothConnect page
			const pages = getCurrentPages();
			let bluetoothPage = pages.find(page =>
				page.route === 'pages/bluetooth/bluetoothConnect' ||
				page.route.includes('bluetoothConnect')
			);

			if (bluetoothPage && bluetoothPage.registerPendingCommand) {
				console.log('使用全局响应处理机制注册指令:', commandId);
				bluetoothPage.registerPendingCommand(commandId, commandCode, commandType, callback, timeout);
			} else {
				console.warn('全局响应处理机制不可用，使用本地处理');
				// Fallback: use local timeout handling
				this.localPendingCommands = this.localPendingCommands || new Map();
				const timeoutHandle = setTimeout(() => {
					if (this.localPendingCommands.has(commandId)) {
						this.localPendingCommands.delete(commandId);
						console.log(`指令 ${commandId} 响应超时`);
						if (callback) {
							callback({
								success: false,
								error: 'timeout',
								message: '指令处理超时'
							});
						}
					}
				}, timeout);

				this.localPendingCommands.set(commandId, {
					commandCode: commandCode,
					commandType: commandType,
					callback: callback,
					timeout: timeoutHandle
				});
			}
		},

		clearPendingCommand(commandId) {
			// Try to clear from global response handling
			const pages = getCurrentPages();
			let bluetoothPage = pages.find(page =>
				page.route === 'pages/bluetooth/bluetoothConnect' ||
				page.route.includes('bluetoothConnect')
			);

			if (bluetoothPage && bluetoothPage.pendingCommands && bluetoothPage.pendingCommands.has(commandId)) {
				const commandInfo = bluetoothPage.pendingCommands.get(commandId);
				clearTimeout(commandInfo.timeout);
				bluetoothPage.pendingCommands.delete(commandId);
				console.log('从全局响应处理机制清除指令:', commandId);
			}

			// Also clear from local pending commands if exists
			if (this.localPendingCommands && this.localPendingCommands.has(commandId)) {
				const commandInfo = this.localPendingCommands.get(commandId);
				clearTimeout(commandInfo.timeout);
				this.localPendingCommands.delete(commandId);
				console.log('从本地响应处理清除指令:', commandId);
			}
		},

		// Utility methods
		arrayBufferToHexString(buffer) {
			const uint8Array = new Uint8Array(buffer);
			let hexString = '';
			for (let i = 0; i < uint8Array.length; i++) {
				hexString += uint8Array[i].toString(16).padStart(2, '0').toUpperCase();
			}
			return hexString;
		},

		bufferToString(buffer) {
			// Convert buffer to string, handling potential encoding issues for log data
			try {
				const uint8Array = new Uint8Array(buffer);

				// First try UTF-8 decoding
				try {
					const decoder = new TextDecoder('utf-8', { fatal: true });
					const decoded = decoder.decode(buffer);
					// Check if decoded string contains mostly printable characters
					const printableRatio = this.calculatePrintableRatio(decoded);
					if (printableRatio > 0.8) {
						return decoded;
					}
				} catch (e) {
					// UTF-8 decoding failed, continue to fallback
				}

				// Fallback: mixed text/hex representation for better log readability
				let result = '';
				let consecutiveText = '';

				for (let i = 0; i < uint8Array.length; i++) {
					const byte = uint8Array[i];

					if (this.isPrintableChar(byte)) {
						// Accumulate printable characters
						consecutiveText += String.fromCharCode(byte);
					} else {
						// Flush accumulated text
						if (consecutiveText.length > 0) {
							result += consecutiveText;
							consecutiveText = '';
						}

						// Handle special characters
						if (byte === 0x0A) {
							result += '\n'; // Line feed
						} else if (byte === 0x0D) {
							result += '\r'; // Carriage return
						} else if (byte === 0x09) {
							result += '\t'; // Tab
						} else if (byte === 0x00) {
							result += '\\0'; // Null character
						} else {
							// Non-printable, show as hex
							result += `\\x${byte.toString(16).padStart(2, '0')}`;
						}
					}
				}

				// Flush any remaining text
				if (consecutiveText.length > 0) {
					result += consecutiveText;
				}

				return result;

			} catch (error) {
				console.error('字符串转换失败:', error);
				// Ultimate fallback: hex dump
				const uint8Array = new Uint8Array(buffer);
				return Array.from(uint8Array)
					.map(byte => byte.toString(16).padStart(2, '0'))
					.join(' ');
			}
		},

		isPrintableChar(byte) {
			// Check if byte represents a printable character
			// Include extended ASCII range for better international character support
			return (byte >= 32 && byte <= 126) || // Standard ASCII printable
				(byte >= 128 && byte <= 255) || // Extended ASCII
				byte === 0x0A || byte === 0x0D || byte === 0x09; // Line feed, carriage return, tab
		},

		calculatePrintableRatio(str) {
			// Calculate ratio of printable characters in string
			if (str.length === 0) return 0;

			let printableCount = 0;
			for (let i = 0; i < str.length; i++) {
				const charCode = str.charCodeAt(i);
				if (this.isPrintableChar(charCode)) {
					printableCount++;
				}
			}

			return printableCount / str.length;
		},

		/**
		 * Process log data with timestamp formatting if needed
		 * @param {string} logData - Raw log data
		 * @param {boolean} isFirstPacket - Whether this is the first packet
		 * @returns {string} - Processed log data with timestamps if enabled
		 */
		processLogDataWithTimestamp(logData, isFirstPacket = false) {
			if (!this.enableTimestamps) {
				return logData;
			}

			try {
				// Split log data into lines for timestamp processing
				const lines = logData.split('\n');
				const processedLines = [];

				for (let i = 0; i < lines.length; i++) {
					const line = lines[i];

					// Skip empty lines or lines that already have timestamps
					if (line.trim() === '' || this.hasTimestamp(line)) {
						processedLines.push(line);
						continue;
					}

					// Add timestamp to non-empty lines
					const timestamp = this.formatTimestamp(new Date());
					const timestampedLine = `[${timestamp}] ${line}`;
					processedLines.push(timestampedLine);
				}

				return processedLines.join('\n');

			} catch (error) {
				console.error('时间戳处理失败:', error);
				// Return original data if timestamp processing fails
				return logData;
			}
		},

		/**
		 * Format timestamp for log entries
		 * @param {Date} date - Date object to format
		 * @returns {string} - Formatted timestamp string
		 */
		formatTimestamp(date) {
			try {
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				const seconds = String(date.getSeconds()).padStart(2, '0');
				const milliseconds = String(date.getMilliseconds()).padStart(3, '0');

				return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
			} catch (error) {
				console.error('时间戳格式化失败:', error);
				return new Date().toISOString();
			}
		},

		/**
		 * Check if a line already has a timestamp
		 * @param {string} line - Log line to check
		 * @returns {boolean} - True if line already has timestamp
		 */
		hasTimestamp(line) {
			// Check for common timestamp patterns
			const timestampPatterns = [
				/^\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d{3}\]/, // [YYYY-MM-DD HH:mm:ss.SSS]
				/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/, // YYYY-MM-DD HH:mm:ss
				/^\[\d{2}:\d{2}:\d{2}\]/, // [HH:mm:ss]
				/^\d{2}:\d{2}:\d{2}/ // HH:mm:ss
			];

			return timestampPatterns.some(pattern => pattern.test(line.trim()));
		},

		/**
		 * Trigger memory optimization hints
		 */
		triggerMemoryOptimization() {
			try {
				// Force garbage collection if available (mainly for debugging)
				if (typeof window !== 'undefined' && window.gc) {
					window.gc();
					console.log('触发垃圾回收');
				}

				// Clear any cached data that's no longer needed
				this.clearUnusedCaches();

			} catch (error) {
				console.error('内存优化失败:', error);
			}
		},

		/**
		 * Clear unused caches and temporary data
		 */
		clearUnusedCaches() {
			try {
				// Clear old pending commands that might be stuck
				if (this.localPendingCommands && this.localPendingCommands.size > 10) {
					console.log('清理过多的待处理指令缓存');
					// Keep only the most recent 5 commands
					const entries = Array.from(this.localPendingCommands.entries());
					const recentEntries = entries.slice(-5);

					// Clear old entries
					for (const [commandId] of entries.slice(0, -5)) {
						const commandInfo = this.localPendingCommands.get(commandId);
						if (commandInfo && commandInfo.timeout) {
							clearTimeout(commandInfo.timeout);
						}
						this.localPendingCommands.delete(commandId);
					}
				}

			} catch (error) {
				console.error('清理缓存失败:', error);
			}
		},

		/**
		 * Toggle timestamp functionality
		 */
		toggleTimestamps() {
			this.enableTimestamps = !this.enableTimestamps;

			const message = this.enableTimestamps ? '已启用时间戳' : '已禁用时间戳';
			this.showCustomToast({
				message: message,
				type: 'default'
			});

			console.log('时间戳功能状态:', this.enableTimestamps);
		},

		/**
		 * Export log content to file (basic implementation)
		 * Consider implementing log export or save functionality
		 */
		exportLogs() {
			try {
				if (!this.logContent || this.logContent.trim() === '') {
					this.showCustomToast({
						message: '没有日志内容可导出',
						type: 'warning'
					});
					return;
				}

				// Generate filename with timestamp
				const timestamp = this.formatTimestamp(new Date()).replace(/[:\s]/g, '-');
				const deviceName = this.deviceName || '未知设备';
				const filename = `${deviceName}_日志_${timestamp}.txt`;

				// Prepare log content with metadata
				const exportContent = this.prepareLogExportContent();

				// Use uni-app file system API to save log
				this.saveLogToFile(filename, exportContent);

			} catch (error) {
				console.error('导出日志失败:', error);
				this.showCustomToast({
					message: '导出日志失败: ' + error.message,
					type: 'error'
				});
			}
		},

		/**
		 * Prepare log content for export with metadata
		 * @returns {string} - Formatted log content for export
		 */
		prepareLogExportContent() {
			const metadata = [
				'=== 设备日志导出 ===',
				`设备名称: ${this.deviceName || '未知设备'}`,
				`设备MAC: ${this.deviceMac || '未知'}`,
				`导出时间: ${this.formatTimestamp(new Date())}`,
				`日志会话开始: ${this.logStartTime ? this.formatTimestamp(this.logStartTime) : '未知'}`,
				`日志条目数: ${this.logEntryCount}`,
				`日志大小: ${Math.round(this.logContent.length / 1024 * 100) / 100} KB`,
				'========================\n'
			].join('\n');

			return metadata + this.logContent;
		},

		/**
		 * Save log content to file using uni-app file system
		 * @param {string} filename - Name of the file to save
		 * @param {string} content - Content to save
		 */
		saveLogToFile(filename, content) {
			// #ifdef APP-PLUS
			try {
				// For App platforms, use file system API
				const fs = uni.getFileSystemManager();
				const filePath = `${uni.env.USER_DATA_PATH}/${filename}`;

				fs.writeFile({
					filePath: filePath,
					data: content,
					encoding: 'utf8',
					success: (res) => {
						console.log('日志文件保存成功:', filePath);
						this.showCustomToast({
							message: `日志已保存到: ${filename}`,
							type: 'default'
						});

						// Show file path in status message
						this.showStatusMessage(`日志已导出: ${filePath}`, 'success', 5000);
					},
					fail: (err) => {
						console.error('日志文件保存失败:', err);
						this.showCustomToast({
							message: '保存日志文件失败',
							type: 'error'
						});
					}
				});
			} catch (error) {
				console.error('文件系统操作失败:', error);
				this.fallbackLogExport(filename, content);
			}
			// #endif

			// #ifdef H5
			// For H5 platform, use download
			this.downloadLogFile(filename, content);
			// #endif

			// #ifdef MP
			// For mini-program, show content in modal or copy to clipboard
			this.showLogInModal(content);
			// #endif
		},

		/**
		 * Fallback log export method
		 * @param {string} filename - Name of the file
		 * @param {string} content - Content to export
		 */
		fallbackLogExport(filename, content) {
			// Copy to clipboard as fallback
			uni.setClipboardData({
				data: content,
				success: () => {
					this.showCustomToast({
						message: '日志内容已复制到剪贴板',
						type: 'default'
					});
				},
				fail: () => {
					this.showCustomToast({
						message: '导出失败，请手动复制日志内容',
						type: 'error'
					});
				}
			});
		},

		/**
		 * Download log file for H5 platform
		 * @param {string} filename - Name of the file
		 * @param {string} content - Content to download
		 */
		downloadLogFile(filename, content) {
			try {
				const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
				const url = URL.createObjectURL(blob);
				const link = document.createElement('a');
				link.href = url;
				link.download = filename;
				document.body.appendChild(link);
				link.click();
				document.body.removeChild(link);
				URL.revokeObjectURL(url);

				this.showCustomToast({
					message: '日志文件下载已开始',
					type: 'default'
				});
			} catch (error) {
				console.error('H5下载失败:', error);
				this.fallbackLogExport(filename, content);
			}
		},

		/**
		 * Show log content in modal for mini-program
		 * @param {string} content - Log content to show
		 */
		showLogInModal(content) {
			const truncatedContent = content.length > 1000 ?
				content.substring(0, 1000) + '\n...[内容过长，已截断]' :
				content;

			uni.showModal({
				title: '日志内容',
				content: truncatedContent,
				showCancel: true,
				cancelText: '关闭',
				confirmText: '复制',
				success: (res) => {
					if (res.confirm) {
						uni.setClipboardData({
							data: content,
							success: () => {
								this.showCustomToast({
									message: '日志内容已复制到剪贴板',
									type: 'default'
								});
							}
						});
					}
				}
			});
		},

		/**
		 * Reset page state when returning to main page
		 * Ensures proper state reset when navigating away from log page
		 */
		resetPageState() {
			console.log('重置页面状态');

			try {
				// Reset all log-related state
				this.isRetrievingLogs = false;
				this.logListening = false;
				this.isFirstLogPacket = true;
				this.logUpdateCounter = 0;

				// Reset error counters
				this.malformedPacketCount = 0;
				this.processingErrorCount = 0;

				// Reset log management state
				this.logSizeWarningShown = false;
				this.logStartTime = null;
				this.logEntryCount = 0;
				this.lastRenderTime = 0;

				// Clear status messages
				this.clearStatusMessage();

				// Reset scroll position
				this.scrollTop = 0;

				// Clear unused caches
				this.clearUnusedCaches();

				// Clear log content if needed (optional - user might want to keep logs)
				// this.logContent = '';

				console.log('页面状态重置完成');

			} catch (error) {
				console.error('重置页面状态失败:', error);
			}
		},

		/**
		 * Generate unique command ID
		 * @returns {string} - Unique command ID
		 */
		generateCommandId() {
			return `log_cmd_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
		},

		/**
		 * Auto scroll to bottom of log display
		 */
		autoScrollToBottom() {
			try {
				// Calculate scroll position to bottom
				const maxScrollTop = Math.max(0, this.logContent.length * 0.5); // Rough estimate
				this.scrollTop = maxScrollTop;

				// Force update in next tick
				this.$nextTick(() => {
					this.scrollTop = maxScrollTop + 1; // Trigger scroll update
				});
			} catch (error) {
				console.error('自动滚动失败:', error);
			}
		},

		/**
		 * Manage large log content to prevent memory issues
		 */
		manageLargeLogContent() {
			const maxLogSize = 100000; // 100KB limit
			const warningSize = 80000;  // 80KB warning

			if (this.logContent.length > maxLogSize) {
				// Trim old content, keep recent data
				const keepSize = 50000; // Keep last 50KB
				const trimmedContent = '...[日志过长，已清理早期内容]...\n' +
					this.logContent.substring(this.logContent.length - keepSize);
				this.logContent = trimmedContent;
				this.logUpdateCounter++;

				console.log('日志内容过长，已自动清理');
				this.showCustomToast({
					message: '日志内容过长，已自动清理早期内容',
					type: 'warning'
				});
			} else if (this.logContent.length > warningSize && !this.logSizeWarningShown) {
				// Show warning once
				this.logSizeWarningShown = true;
				this.showStatusMessage('日志内容较大，可能影响性能', 'warning', 3000);
			}
		}
	}
}
</script>

<style scoped>
.log-viewer-page {
	background-color: #f6f6f6;
	min-height: 100vh;
}

.container {
	padding: 20rpx;
	display: flex;
	flex-direction: column;
	height: 100vh;
}

/* Device Info Card */
.device-info-card {
	background: white;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.device-name {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.device-status {
	font-size: 28rpx;
	margin-bottom: 8rpx;
}

.device-status.connected {
	color: #4cd964;
}

.device-status.disconnected {
	color: #ff3b30;
}

.device-mac {
	font-size: 24rpx;
	color: #666;
}

/* Log Control Section */
.log-control-section {
	display: flex;
	gap: 20rpx;
	margin-bottom: 16rpx;
	flex-wrap: wrap;
}

/* Advanced Log Control Section */
.advanced-control-section {
	display: flex;
	gap: 16rpx;
	margin-bottom: 20rpx;
	flex-wrap: wrap;
	align-items: center;
	padding: 16rpx;
	background: rgba(255, 255, 255, 0.8);
	border-radius: 12rpx;
	border: 1rpx solid #e9ecef;
}

.btn {
	flex: 1;
	min-width: 200rpx;
	height: 88rpx;
	border-radius: 12rpx;
	border: none;
	font-size: 32rpx;
	font-weight: 500;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.btn-content {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 16rpx;
}

.btn-text {
	font-size: 32rpx;
	font-weight: 500;
}

.loading-spinner {
	width: 32rpx;
	height: 32rpx;
	border: 4rpx solid rgba(255, 255, 255, 0.3);
	border-top: 4rpx solid white;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

.btn-primary {
	background: linear-gradient(135deg, #47afff 0%, #3b9eff 100%);
	color: white;
	box-shadow: 0 4rpx 12rpx rgba(71, 175, 255, 0.3);
}

.btn-primary:disabled {
	background: #ccc;
	color: #999;
	box-shadow: none;
}

.btn-secondary {
	background: #f8f9fa;
	color: #666;
	border: 2rpx solid #e9ecef;
}

.btn-secondary:disabled {
	background: #f1f3f4;
	color: #9aa0a6;
	border-color: #dadce0;
}

.btn-warning {
	background: linear-gradient(135deg, #ff9500 0%, #ff8c00 100%);
	color: white;
	box-shadow: 0 4rpx 12rpx rgba(255, 149, 0, 0.3);
}

.btn:active:not(:disabled) {
	transform: scale(0.98);
}

.btn-small {
	height: 64rpx;
	min-width: 140rpx;
	font-size: 26rpx;
	padding: 0 24rpx;
}

.btn-small .btn-text {
	font-size: 26rpx;
}

.btn-outline {
	background: transparent;
	color: #47afff;
	border: 2rpx solid #47afff;
}

.btn-outline:disabled {
	background: transparent;
	color: #ccc;
	border-color: #ccc;
}

.log-stats {
	margin-left: auto;
	padding: 8rpx 16rpx;
	background: rgba(71, 175, 255, 0.1);
	border-radius: 8rpx;
	border: 1rpx solid rgba(71, 175, 255, 0.2);
}

.stats-text {
	font-size: 24rpx;
	color: #47afff;
	font-weight: 500;
}

/* Status Messages Section */
.status-section {
	margin-bottom: 20rpx;
}

.status-message {
	display: flex;
	align-items: center;
	gap: 16rpx;
	padding: 24rpx;
	border-radius: 12rpx;
	font-size: 28rpx;
	animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
	from {
		opacity: 0;
		transform: translateY(-20rpx);
	}

	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.status-message.info {
	background: #e3f2fd;
	color: #1976d2;
	border-left: 8rpx solid #2196f3;
}

.status-message.success {
	background: #e8f5e8;
	color: #2e7d32;
	border-left: 8rpx solid #4caf50;
}

.status-message.warning {
	background: #fff3e0;
	color: #f57c00;
	border-left: 8rpx solid #ff9800;
}

.status-message.error {
	background: #ffebee;
	color: #c62828;
	border-left: 8rpx solid #f44336;
}

.status-message.loading {
	background: #f3e5f5;
	color: #7b1fa2;
	border-left: 8rpx solid #9c27b0;
}

.status-icon {
	font-size: 32rpx;
	font-weight: bold;
	min-width: 32rpx;
}

.status-message.loading .status-icon {
	animation: spin 2s linear infinite;
}

.status-text {
	flex: 1;
	line-height: 1.4;
}

/* Log Display Area */
.log-display-container {
	flex: 1;
	background: #000000;
	border-radius: 12rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
	border: 2rpx solid #333;
	min-height: 400rpx;
}

.log-text-area {
	height: 100%;
	padding: 24rpx;
}

.log-content {
	color: #ffffff;
	font-family: 'Courier New', 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
	font-size: 26rpx;
	line-height: 1.6;
	white-space: pre-wrap;
	word-break: break-all;
	letter-spacing: 0.5rpx;
	text-shadow: 0 0 1rpx rgba(255, 255, 255, 0.1);
	padding-bottom: 40rpx;
}

/* Empty state styling */
.log-content:empty::before {
	content: '暂无日志内容，点击"获取日志"开始获取设备日志...';
	color: #666;
	font-style: italic;
}

/* Toast */
.toast-container {
	position: fixed;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	z-index: 9999;
}

.toast-message {
	background: rgba(0, 0, 0, 0.8);
	color: white;
	padding: 20rpx 40rpx;
	border-radius: 8rpx;
	font-size: 28rpx;
	max-width: 600rpx;
	text-align: center;
}

.toast-message.error {
	background: rgba(255, 59, 48, 0.9);
}

.toast-message.warning {
	background: rgba(255, 149, 0, 0.9);
}

/* Responsive Design for Different Screen Sizes */

/* Small screens (phones in portrait) */
@media screen and (max-width: 750rpx) {
	.container {
		padding: 16rpx;
	}

	.device-info-card {
		padding: 24rpx;
		margin-bottom: 16rpx;
	}

	.device-name {
		font-size: 32rpx;
	}

	.device-status {
		font-size: 26rpx;
	}

	.device-mac {
		font-size: 22rpx;
	}

	.log-control-section {
		gap: 16rpx;
		margin-bottom: 12rpx;
	}

	.advanced-control-section {
		gap: 12rpx;
		margin-bottom: 16rpx;
		padding: 12rpx;
	}

	.btn-small {
		height: 56rpx;
		min-width: 120rpx;
		font-size: 24rpx;
	}

	.btn-small .btn-text {
		font-size: 24rpx;
	}

	.stats-text {
		font-size: 22rpx;
	}

	.btn {
		height: 80rpx;
		font-size: 28rpx;
		min-width: 180rpx;
	}

	.btn-text {
		font-size: 28rpx;
	}

	.loading-spinner {
		width: 28rpx;
		height: 28rpx;
		border-width: 3rpx;
	}

	.status-message {
		padding: 20rpx;
		font-size: 26rpx;
		gap: 12rpx;
	}

	.status-icon {
		font-size: 28rpx;
	}

	.log-text-area {
		padding: 20rpx;
	}

	.log-content {
		font-size: 24rpx;
		line-height: 1.5;
	}
}

/* Large screens (tablets, large phones in landscape) */
@media screen and (min-width: 1200rpx) {
	.container {
		padding: 32rpx;
		max-width: 1200rpx;
		margin: 0 auto;
	}

	.device-info-card {
		padding: 40rpx;
		margin-bottom: 32rpx;
	}

	.device-name {
		font-size: 40rpx;
	}

	.device-status {
		font-size: 32rpx;
	}

	.device-mac {
		font-size: 28rpx;
	}

	.log-control-section {
		gap: 24rpx;
		margin-bottom: 24rpx;
	}

	.advanced-control-section {
		gap: 20rpx;
		margin-bottom: 32rpx;
		padding: 20rpx;
	}

	.btn-small {
		height: 72rpx;
		min-width: 160rpx;
		font-size: 28rpx;
	}

	.btn-small .btn-text {
		font-size: 28rpx;
	}

	.stats-text {
		font-size: 26rpx;
	}

	.btn {
		height: 96rpx;
		font-size: 36rpx;
		min-width: 240rpx;
	}

	.btn-text {
		font-size: 36rpx;
	}

	.loading-spinner {
		width: 36rpx;
		height: 36rpx;
		border-width: 5rpx;
	}

	.status-message {
		padding: 32rpx;
		font-size: 32rpx;
		gap: 20rpx;
	}

	.status-icon {
		font-size: 36rpx;
	}

	.log-text-area {
		padding: 32rpx;
	}

	.log-content {
		font-size: 28rpx;
		line-height: 1.7;
	}
}

/* Very small screens (older phones) */
@media screen and (max-width: 600rpx) {
	.log-control-section {
		flex-direction: column;
	}

	.advanced-control-section {
		flex-direction: column;
		align-items: stretch;
	}

	.log-stats {
		margin-left: 0;
		margin-top: 12rpx;
		text-align: center;
	}

	.btn {
		width: 100%;
		min-width: auto;
	}

	.device-info-card {
		padding: 20rpx;
	}

	.device-name {
		font-size: 30rpx;
	}

	.log-content {
		font-size: 22rpx;
	}
}

/* Landscape orientation adjustments */
@media screen and (orientation: landscape) {
	.container {
		height: 100vh;
		padding: 16rpx;
	}

	.device-info-card {
		margin-bottom: 16rpx;
		padding: 24rpx;
	}

	.log-control-section {
		margin-bottom: 16rpx;
	}

	.status-section {
		margin-bottom: 16rpx;
	}
}
</style>