<template>

	<!-- 首页 -->
	<view class="homePage">


		<HeadNav   @getHeadHeight="getHeadHeight" ></HeadNav>


		<!-- <view class="head-logo">
			//style="border-radius: 32rpx;"



		</view> -->
		<view class="headImg">
				    <!-- 显示加载状态 -->
				    <view v-if="isLoadingImages" class="carousel-loading">
				      <text class="loading-text">加载中...</text>
				    </view>
				    <!-- 轮播图组件 -->
				    <swiper v-else class="swiper-box" style="height: 300rpx;" autoplay="true" interval="3000" circular="true"  indicator-dots="true" indicator-color="#ffffff" indicator-active-color="#ff0000" >
				      <swiper-item class="swiper-item" v-for="(imageUrl, index) in carouselImages" :key="index">
				        <image 
				          :src="imageUrl" 
				          mode="aspectFill" 
				          style="width: 100%;height: 100%;border-radius: 32rpx;"
				          @error="handleImageError(index)"
				        ></image>
				      </swiper-item>
				    </swiper>
		</view>
		
		<!-- 特定人群登录提示 -->
		<view class="auth-notice" v-if="showAuthNotice">
			<view class="auth-notice-content">
				<text class="auth-notice-icon">ℹ️</text>
				<text class="auth-notice-text">仅限授权用户使用</text>
				<text class="auth-notice-close" @click="closeAuthNotice">×</text>
			</view>
		</view>
		
		<view class="noticeView">
			<!-- <u-notice-bar :text="noticeText" bgColor="#ecf5ff" color='#2b85e4'></u-notice-bar> -->

			<!-- <u-notice-bar mode="horizontal" :list="list11"></u-notice-bar> -->

			<u-notice-bar mode="horizontal" :is-circular="true" :text="noticeText"></u-notice-bar>

			<!-- <u-notice-bar mode="vertical" :list="list11"></u-notice-bar> -->
		</view>

		<view class="funtionView" v-if="userSign=='pt'">
			<view class="funtion1" @click="goAddDev">
				<view class="backimg11">
				<image class="backImg" src="@/static/image/scan.png" mode=""></image>
				</view>
				<text class="text1">添加设备</text>
			</view>
			<view class="funtion2" @click="goMyDev">
				<view class="backimg12">
				<image class="backImg" src="@/static/image/设备管理.png" mode=""></image>
				</view>
				<text class="text1">设备管理</text>
			</view>
			<view class="funtion3" @click="goAlarmInform">
				<view class="backimg13">
				<image class="backImg" src="@/static/image/燃气报警.png" mode=""></image>
				</view>
				<text class="text1">报警信息</text>
			</view>
			<view class="funtion4" @click="goReportFault">
				<view class="backimg14">
				<image class="backImg" src="@/static/image/上报故障.png" mode=""></image>
				</view>
				<text class="text1">上报故障</text>
			</view>
		</view>

	<view class="viewOne">
			<!-- <view class="viewOne_b" v-if="userSign=='yw' || userSign=='admin'">
				<view class="viewOne_b_a" @click="goDeviceManage">
					<image class="backImg" src="@/static/homePage/myDevIco.png" mode=""></image>
					<text >设备管理</text>
				</view>
			</view> -->
			<view class="viewOne_b" v-if="userSign=='yw' || userSign=='admin'">
				<view class="viewOne_b_a" @click="goInstallationList">
					<image class="backImg" src="@/static/image/设备管理.png" mode=""></image>
					<text>设备安装工单</text>
				</view>
			</view>
			<view class="viewOne_b" v-if="userSign=='yw' || userSign=='admin'">
				<view class="viewOne_b_a" @click="goSceneManage">
					<image class="backImg" src="@/static/homePage/cjIcon.png" mode=""></image>
					<text>场景管理</text>
				</view>
			</view>
			<view class="viewOne_b" v-if="userSign=='yw' || userSign=='admin'">
				<view class="viewOne_b_a" @click="goUserManage">
					<image class="backImg" src="@/static/homePage/yhIcon.png" mode=""></image>
					<text>用户管理</text>
				</view>
			</view>
			<view class="viewOne_b" v-if="userSign=='yw' || userSign=='admin'">
				<view class="viewOne_b_a" @click="goDefaultManage">
					<image class="backImg" src="@/static/image/故障.png" mode=""></image>
					<text>故障处理</text>
				</view>
			</view>
				<view class="viewOne_b" v-if="userSign == 'yw' || userSign == 'admin'">
				<view class="viewOne_b_a" @click="goBluetoothConnect">
					<image class="backImg" src="@/static/image/scan.png" mode=""></image>
					<text>蓝牙连接</text>
				</view>
			</view>
			<!-- <view class="viewOne_b" v-if="userSign == 'yw' || userSign == 'admin'">
				<view class="viewOne_b_a" @click="goCommandHistory">
					<image class="backImg" src="@/static/image/上报故障.png" mode=""></image>
					<text>指令历史</text>
				</view>
			</view> -->

	</view>



		<!-- </view> -->
		<view class="viewTwo" v-if="userSign=='pt'">
			<text class="viewTitle">当前监测</text>
			<view class="custom-tab-bar">
			</view>
		</view>

	<view  v-if= "this.deviceList.length > 0 && userSign=='pt'">
		<view class="devList" v-if="userSign=='pt'">
			<view class="devView" v-for="(item,index) in deviceList" :key="index" @click="goDevicePage(item)" :style="{backgroundColor:signList[index]}">
				<!-- code 编号   status  状态 00正常 01光路故障  02温度获取异常  03温控故障   equipType 类型 -->
				  <view class="grid-item1">
				     <!-- <view class="sign"></view>
					<view class="null1"></view> -->

					<!-- <view class="null4" >
						<image class="connectimage"  v-if="isConnect[index] == 0" src="@/static/image/未连接.png" ></image>

						<image v-if="item.status == '正常工作'"  src="@/static/image/点赞.png" ></image>
						<image v-else src="@/static/image/故障1.png" ></image> -->
						<!-- <view class="is_Connect" v-else></view> -->

					 <!-- </view> -->

					  <text>设备名称:</text>
					  <!-- <text>设备编号:</text> -->
					  <text>设备类型:</text>
					  <text>设备状态:</text>
					  <text>实时浓度:</text>
					  <text>设备场景:</text>
					 <!-- <text>更新时间:</text> -->


				  </view>
				  <view class="grid-item2">
				  <!-- <view class="null2"></view> -->
				      <text>{{item.name}}</text>
				     <!-- <text>{{item.code}}</text> -->
				      <text  v-if="item.equipType=='001'">家用型可燃气体探测器</text>
				      <text  v-else-if="item.equipType=='002'">地下空间燃气泄漏监测仪</text>
				      <text  v-else-if="item.equipType=='003'">工商业可燃气体探测器</text>
				      <text  v-else-if="item.equipType=='004'">地埋式燃气泄漏监测仪</text>
					  <text v-if="item.status == '正常工作'">{{item.status}}</text>
					  <text  v-else style="color: #ff0000;">{{item.status}}</text>
					  <template >
					     <!-- <text v-if="isConnect[index] == 0">0%LEL</text>
						 <text v-else-if="isConnect[index] == 1">{{item.chroma}}%LEL</text> -->
						 <text >{{item.chroma}}%LEL</text>

					  </template>
				       <text >{{item.sceneName}}</text>
					  <!-- {{item.chroma}} -->
					  <!-- <template v-for="(item2, index) in conduitList" >
					     <text v-if="item2.id == item.sceneId">{{item2.sceneName}}</text>
					  </template> -->

				     <!-- <text>{{item.updateTime}}</text> -->
				  </view>
				  <view class="grid-item3">
					  <view class="null3" >
						<image style="padding: 3%;width: 45rpx;height: 40rpx;margin-right: 3%;" class="connectimage"  v-if="isConnect[index] == 0" src="@/static/image/unSignal1.png" ></image>
						<image style="padding: 3%;width: 45rpx;height: 45rpx;margin-right: 3%;" class="connectimage"  v-else src="@/static/image/信号 (1).png" ></image>
						<!-- <image style="padding: 3%;width: 50rpx;height: 45rpx;margin-left: 5rpx;" v-if="item.status == '正常工作'"  src="@/static/image/正常 (5).png" ></image> -->
						<image style="padding: 3%;width: 50rpx;height: 45rpx;" v-if="item.status != '正常工作' && isConnect[index] == 1" src="@/static/image/333.png" ></image>

						<image style="padding: 2%;width: 60rpx;height: 45rpx;" v-if="signList[index] == '#ff5500' && isConnect[index] == 1"  src="@/static/image/222.png" ></image>
						<!-- <image v-else src="@/static/image/报警消息.png" ></image> -->
						<!-- <view class="is_Connect" v-else></view> -->

					  </view>
					  <view class="dev_content">
						  <!-- <view class="dev_image"> -->
					  		<image src="@/static/product/fajing.png" mode="" v-if="item.equipType=='002'"></image>
					  		<image src="@/static/homePage/dmsIcon.png" mode="" v-else-if="item.equipType=='004'"></image>
							<template v-else-if="item.equipType=='001'">
					  		<!-- <image style="height: 80%;width: 100%;" src="https://www.mtgas.cn:8090/applet_rqaq/profile/upload/2024/08/12/gpocQpv0Xmeybfe86ce322fe350a9c505be127d839f7_20240812094228A011.PNG" mode="" v-if=" item.code.indexOf('82') == 0"></image> -->
							<image  src="@/static/product/yuanxing.png" mode="" v-if=" item.code.indexOf('82') == 0"></image>
							<image src="@/static/product/fangxing.png" mode="" v-else-if=" item.code.indexOf('81') == 0"></image>
		                    <image  src="@/static/product/home.png" mode="" v-else></image>
							</template>
					  		<image src="@/static/product/gongshang.png" mode="" v-else-if="item.equipType=='003'"></image>
							<!-- </view> -->
					  </view>


				</view>
			</view>
		</view>
	</view>
	<view v-else-if= "this.deviceList.length <= 0 && userSign=='pt'" class="noDevices">
		<!-- <view class="noDevices_1"> -->
		<image  src="@/static/image/暂无设备.png" class="noDevices_image"></image>
		<!-- </view> -->
		<!-- <view class="noDevices_2"> -->
		<text class="noDevices_text">暂无设备</text>
		<!-- </view> -->
	</view>


		<u-toast ref="uToast"></u-toast>

	</view>
</template>

<script>
	import {
		devAllList,
		sceneListAll,
		deviceInfoList,
		equipUnbind,
		noticeList,
		handle,
		devDetail,
		getPicList
	} from "@/network/api.js";
	
	import { generateCarouselPlaceholder } from "@/utils/svgPlaceholder.js";

	import HeadNavVue from "../../components/HeadNav/HeadNav.vue";
import { Static } from "vue";
	export default {
		components: {  HeadNavVue}, // 2. 注册组件
		data() {
			return {
				num_ber: 0,
				deviceList: [],
				dev_iceList: [],
				showAuthNotice: true, // 控制授权提示显示状态
				carouselImages: [], // 轮播图片数组
				isLoadingImages: false, // 图片加载状态
				useDefaultImages: false, // 是否使用默认占位图
				modelPatient: {
					userInfo: {
						equipTypeE: '',
						code: '',
						name: '',
						statusE: '',
						chroma: '',
						temp: '',
						signal: '',
						pwr: '',
						location: '',
						updateTime: ''
					},
				},
				list11: [
				          '寒雨连江夜入吴',
				          '平明送客楚山孤',
				          '洛阳亲友如相问',
				          '一片冰心在玉壶'
				        ],
				dataDevicechroma: [],
				resDataList: [{"chroma":'',
				"time":''},],
				conduitList: [],
				dataDevicetime: [],
				list3: [
					'http://**************:8051/prod-api/profile/upload/2024/05/31/5e1359b39533eee72b82a10433e0cbf7.png',

				],
				signList: [],
				isConnect: [],
				noticeText: '',
				activeTabIndex: 0 ,

				userSign: '',
				userId: '',
				delShowModal: false,
				times: null,
				dateTime: null,
				navHeight: 0,
				ig: '',
				HeadNavHeight: '',
			};
		},
		onLoad() {
			console.log('[生命周期] onLoad - 页面加载');
			this.userSign = uni.getStorageSync('userType');
			this.userId = uni.getStorageSync('userId');
			// 检查是否已经隐藏了授权提示
			const authNoticeHidden = uni.getStorageSync('authNoticeHidden');
			if (authNoticeHidden) {
				this.showAuthNotice = false;
			}
			console.log("2323",this.userSign)
			console.log('123')
			// 初始加载轮播图片
			console.log('[生命周期] onLoad - 开始获取轮播图片');
			this.fetchCarouselImages();
		},
		onShareAppMessage() {
		    const promise = new Promise(resolve => {
		      setTimeout(() => {
		        resolve({
		          title: '燃气安全管家'
		        })
		      }, 2000)
		    })
		    return {
		      title: '燃气安全管家',
		      path: '/pages/loginRegister/welcomePage',
		      promise
		    }
		  },
		  onShareTimeline(){
		      return {
		        title: '燃气安全管家首页',
		        path: '/pages/loginRegister/welcomePage',
		        // imageUrl: 'xxx.jpg'
		      }
		    },
		onShow() {
			console.log('[生命周期] onShow - 页面显示');
			this.pageFunc();
			this.startTimer();
			this.getConfigsList();
			this.getDevList();
			// 重新获取轮播图片以确保最新内容
			console.log('[生命周期] onShow - 刷新轮播图片');
			this.fetchCarouselImages();
		},
		onHide() {
			this.clearTimer();
		},
		methods: {
			/**
			 * 获取轮播图片列表
			 * 从API获取图片并处理错误情况
			 */
			async fetchCarouselImages() {
				console.log('[轮播图片] 开始获取轮播图片列表');
				this.isLoadingImages = true;
				
				try {
					const response = await getPicList();
					console.log('[轮播图片] API响应:', response);
					
					// 检查API响应是否成功
					if (response && response.code === 200 && response.data && Array.isArray(response.data)) {
						if (response.data.length > 0) {
							this.carouselImages = response.data;
							this.useDefaultImages = false;
							console.log('[轮播图片] 成功获取轮播图片:', {
								count: this.carouselImages.length,
								images: this.carouselImages
							});
						} else {
							// API返回空数组，使用占位图
							console.warn('[轮播图片] API返回空的图片数组，使用占位图');
							this.setPlaceholderImages();
						}
					} else {
						// API响应格式不正确，使用占位图
						console.warn('[轮播图片] API响应格式不正确:', {
							response: response,
							expectedFormat: 'code: 200, data: Array'
						});
						this.setPlaceholderImages();
					}
				} catch (error) {
					// API调用失败，使用占位图
					console.error('[轮播图片] 获取轮播图片失败:', {
						error: error,
						message: error.message,
						stack: error.stack
					});
					this.setPlaceholderImages();
				} finally {
					this.isLoadingImages = false;
					console.log('[轮播图片] 图片加载完成，loading状态已重置');
				}
			},
			
			/**
			 * 设置占位图片
			 * 生成SVG占位图作为fallback
			 */
			setPlaceholderImages() {
				console.log('[轮播图片] 设置占位图片');
				this.useDefaultImages = true;
				// 生成4个占位图片，与原来的硬编码图片数量保持一致
				const placeholderSvg = generateCarouselPlaceholder();
				this.carouselImages = [
					placeholderSvg,
					placeholderSvg,
					placeholderSvg,
					placeholderSvg
				];
				console.log('[轮播图片] SVG占位图片已设置:', {
					count: this.carouselImages.length,
					useDefaultImages: this.useDefaultImages
				});
			},
			
			/**
			 * 处理单个图片加载错误
			 * 当某个图片加载失败时，替换为占位图
			 */
			handleImageError(index) {
				console.warn('[轮播图片] 单个图片加载失败:', {
					index: index,
					originalUrl: this.carouselImages[index],
					useDefaultImages: this.useDefaultImages
				});
				
				if (!this.useDefaultImages && this.carouselImages[index]) {
					// 只替换失败的图片为占位图
					const placeholderSvg = generateCarouselPlaceholder();
					this.$set(this.carouselImages, index, placeholderSvg);
					console.log('[轮播图片] 已将失败图片替换为占位图:', {
						index: index,
						replacedWith: 'SVG placeholder'
					});
				}
			},
			
			getHeight(navHeight){
				console.log('navHeight',navHeight);
				this.navHeight = navHeight;
			},
			getConfigsList() {
				let query = {
					userType: uni.getStorageSync('userType'),
					id: uni.getStorageSync('userId')
				}
				sceneListAll(query).then(res => {
					console.log(res,'场景列表');
						this.conduitList = res.data;
						console.log(this.conduitList,'conduitList');

						//this.getDevicList(res.data[0].id)
					})
					.catch((err) => {})
			},
			devicDetail() {


			},
			getHeadHeight(e) {
							this.HeadNavHeight = e
							console.log(this.HeadNavHeight);
						},
			getDevList() {
				let query = {
					customerId: uni.getStorageSync('userId'),
					token: uni.getStorageSync('token'),
				}
				devAllList(query).then(res => {
					console.log(query.token,'tcken');
					this.deviceList = res.data;

					for(var j = 0;j<this.deviceList.length;j++)
							{

								 if(this.deviceList[j].online == false)
								 {
									this.isConnect[j] = 0;
									this.signList[j] = "#dfe1e2"
									 //this.signList[j] = "#ff5500"
								}
								else{
									this.isConnect[j] = 1;
									this.signList[j] = "#ecf5ff"

								}


							if(this.deviceList[j].chroma >= 8 && this.isConnect[j] == 1 && this.deviceList[j].equipType == '001')
							{
								 this.signList[j] = "#ff5500"

							}
							else if(this.deviceList[j].chroma >= 3 && this.isConnect[j] == 1 && this.deviceList[j].chroma < 8 && this.deviceList[j].equipType == '001'){
								 this.signList[j] = "#ffaa00"

							}
							else if(this.deviceList[j].chroma < 3 && this.isConnect[j] == 1 && this.deviceList[j].equipType == '001')
							{
								this.signList[j] = "#ecf5ff"

							}

							if(this.deviceList[j].chroma >= 10 && this.isConnect[j] == 1 && this.deviceList[j].equipType == '003')
							{
							       this.signList[j] = "#ff5500"
							}
							else if(this.deviceList[j].chroma >= 3 && this.isConnect[j] == 1 && this.deviceList[j].equipType == '003'&& this.deviceList[j].chroma < 10){
									 this.signList[j] = "#ffaa00"
							}
							else if(this.deviceList[j].chroma < 3 && this.isConnect[j] == 1 && this.deviceList[j].equipType == '003')
							{
								this.signList[j] = "#ecf5ff"
							}

							if(this.deviceList[j].chroma >= 3 && this.isConnect[j] == 1 && this.deviceList[j].equipType == '002')
							{
															 this.signList[j] = "#ff5500"
							}
							else if(this.deviceList[j].chroma >= 1 && this.isConnect[j] == 1 && this.deviceList[j].equipType == '002'&& this.deviceList[j].chroma < 3){
															 this.signList[j] = "#ffaa00"
							}
							else if(this.deviceList[j].chroma < 1 && this.isConnect[j] == 1 && this.deviceList[j].equipType == '002')
							{
								this.signList[j] = "#ecf5ff"
							}

							if(this.deviceList[j].chroma >= 3 && this.isConnect[j] == 1 && this.deviceList[j].equipType == '004')
							{
															 this.signList[j] = "#ff5500"
							}
							else if(this.deviceList[j].chroma >= 1 && this.isConnect[j] == 1 && this.deviceList[j].equipType == '004'&& this.deviceList[j].chroma < 3){
															 this.signList[j] = "#ffaa00"
							}
							else if(this.deviceList[j].chroma < 1 && this.isConnect[j] == 1 && this.deviceList[j].equipType == '004')
							{
								this.signList[j] = "#ecf5ff"
							}


						}

					})
					.catch((err) => {})

			},
			pageFunc() {
				noticeList().then(res => {
					if (res.data.length > 0) {
						this.noticeText = res.data[0].noticeTitle
					}
				})
			},
			goDevicePage(item) {
				uni.navigateTo({
					url: '/pages/deviceManage/devicePage?params=' + JSON.stringify(item)
				})
			},
			startTimer() {
				 // this.aleamInfo();
				//this.timer = setInterval(this.aleamInfo, 5000); // 每5秒调用一次接口
				this.timer = setInterval(this.getDevList, 5000); // 每5秒调用一次接口
				//this.timer = setInterval(this.devicDetail, 5000);



			},
			clearTimer() {
				clearInterval(this.timer);
			},
			aleamInfo(){
				this.dateTime = Date.now();
				console.log(this.dateTime,'time');
				let dateTime = uni.getStorageSync("dateTime");
				if (this.dateTime - 180000 > dateTime) {
					uni.setStorageSync("aleamInfo", "1");
				}
				let aleamInfo = uni.getStorageSync("aleamInfo");
				let userType = uni.getStorageSync('userType');
				if (aleamInfo == 1 && userType == 'pt') {
					let data = {};
					data.customerId = this.userId;
					data.userType = this.userSign;
					handle(data).then(res => {
						console.log("🚀 ~ handle ~ res:", res.data)
						if (res.data > 0) {
							this.delShowModal = true;
						}
					})
				}

			},

			publicMethod(time){
			   return (new Date(time)).getTime()
			},

			delConfirm(){
				this.delShowModal = false;
				uni.setStorageSync("aleamInfo", "0");
				uni.setStorageSync("dateTime", Date.now());
			},
			cancelModal(){
				this.delShowModal = false;
				uni.setStorageSync("aleamInfo", "0");
				uni.setStorageSync("dateTime", Date.now());
				uni.navigateTo({
					url: '../basicInform/alarmInform'
				})
			},
			goToast(index){
				this.activeTabIndex = index;
				this.$refs.uToast.show({
					message:'功能开发中',
					position:'bottom'
				})
			},
			goAddDev() {
				uni.navigateTo({
					url: '/pages/deviceManage/addDevice'
				})
			},
			goSceneManage() {
				uni.navigateTo({
					url: '/pages/userManage/sceneManage'
				})
			},
			goUserManage() {
				uni.navigateTo({
					url: '/pages/userManage/userManage'
				})
			},
			goDefaultManage() {
				uni.navigateTo({
					url: '../basicInform/DefaultHandle'
				})
			},
			goMyDev() {
				uni.navigateTo({
					url: '/pages/deviceManage/myDevice'
				})
			},
			goDeviceManage() {
				uni.navigateTo({
					url: '/pages/deviceManage/deviceManage'
				})
			},
			goReportFault() {
				uni.navigateTo({
					url: '../basicInform/reportFault'
				})
			},
			goAlarmInform() {
				uni.navigateTo({
					url: '../basicInform/alarmInform'
				})
			},
			goHistoryList() {
				uni.navigateTo({
					url: '../basicInform/historicalFaultList'
				})
			},
			goBluetoothConnect() {
				// Implement the logic for going to Bluetooth connect page
				console.log("Going to Bluetooth connect page");
				uni.navigateTo({
					url: '../bluetooth/bluetoothConnect'
				})
			},
			goInstallationList() {
				// 导航到设备安装工单列表页面
				console.log("Going to installation list page");
				uni.navigateTo({
					url: '/pages/deviceManage/installationList'
				})
			},
			// goCommandHistory() {
			// 	// Implement the logic for going to command history page
			// 	console.log("Going to command history page");
			// 	uni.navigateTo({
			// 		url: '../bluetooth/commandHistory'
			// 	})
			// },
			closeAuthNotice() {
				this.showAuthNotice = false;
				// 可以选择将关闭状态保存到本地存储
				uni.setStorageSync('authNoticeHidden', true);
			}
		}
	};
</script>

<style lang="scss" scoped>
	// .navbarEE {
	// 	position: fixed;
	// 	top: 0;
	// 	left: 0;
	// 	right: 0;
	// 	z-index: 999;
	// }
	.swiper-box {

		border-radius: 30rpx;
		overflow: hidden;
		/* 兼容IOS，否则在swiper组件内的布局都不受border-radius和overflow的约束 */
		transform: translateY(0);
	}
	.swiper-item {
		width: calc(100vw - 60rpx);
		height: calc(50vw - 30rpx);
	}

	.homePage
	{
		// .navbarEE {
		// 		position: fixed;
		// 		top: 0;
		// 		left: 0;

		// 		z-index: 999;
		// }
	}
	.back-logoPhone{
		display: flex;
		height: 100%;

	}
	.status_bar
	{
		height: var(--status-bar-height);
		width: 100%;
	}
	.head-logo {
		width: 100%;
		height: 50rpx;
		display: flex;
		margin-bottom: 2rpx;
		// flex-direction: column;
		 //justify-content: center;

		}

	.head-logo image {
		 margin-left: 3%;
		text-align: center;
		width: 30%;
		height: 40rpx;

	}
	.head-logo text {
		margin-right: 3%;
		width: 70%;
		height: 30rpx;
		text-align: right;
		white-space: nowrap;

	}

	// .status_content
	// {
	// 	height:var(navHeight);
	// 	width: 100%;
	// }
		.homePage {

		}

		.headImg {
			margin-left: 25rpx;
			height: 300rpx;
			width: 700rpx;

			.carousel-loading {
				display: flex;
				align-items: center;
				justify-content: center;
				height: 100%;
				background-color: #f5f5f5;
				border-radius: 32rpx;
				
				.loading-text {
					color: #999;
					font-size: 28rpx;
				}
			}
		}

		.noticeView {
			padding: 20rpx;
		}

		.funtionView {
			height: 150rpx;
			display: flex;
			margin-left: 3%;
			width: 94%;
			background-color: #ecf5ff;
		}

		.funtion1 {

		    padding: 15rpx;
			width: 25%;
			display: flex;
			flex-direction: column;
			text-align: center;
			.backimg11{
				width: 100%;
				height:  70rpx;
				.backImg {

					width: 70rpx;
					height: 70rpx;

				}
			}

			.text1 {
				width: 100%;
				height: 50%;
			}
		}
		.funtion2 {

			padding: 15rpx;
			width: 25%;
			display: flex;
			flex-direction: column;
			text-align: center;
			.backimg12{
				width: 100%;
				height:  70rpx;
			.backImg {

				width: 70rpx;
				height: 70rpx;

			}
			}
			.text1 {
				width: 100%;
				height: 50%;
			}
		}
		.funtion3 {
			padding: 15rpx;
			width: 25%;
			display: flex;
			flex-direction: column;
			text-align: center;
			.backimg13{
				width: 100%;
				height:  70rpx;
			.backImg {

				width: 70rpx;
				height: 70rpx;

			}
			}
			.text1 {
				width: 100%;
				height: 50%;
			}
		}
		.funtion4 {
			padding: 15rpx;
			width: 25%;
			display: flex;
			flex-direction: column;
			text-align: center;
			.backimg14{
				width: 100%;
				height:  70rpx;
			.backImg {

				width: 80rpx;
				height: 80rpx;

			}
			}
			.text1 {
				width: 100%;
				height: 50%;
			}
		}

		.viewOne {
			padding: 26rpx 56rpx;

			.viewOne_a {
				width: 100%;
				padding: 20rpx 0;
				display: flex;
				justify-content: space-between;

				.viewOne_a_a {
					display: flex;
					align-items: center;
					width: 62%;
					padding: 30rpx 20rpx;
					background-image: url(@/static/homePage/box_sel.png);
					background-size: 100% 100%;

					.backImg {
						width: 70rpx;
						height: 70rpx;
						margin: 0 20rpx 0;
					}
				}

				.viewOne_a_b {
					display: flex;
					flex-direction: column;
					align-items: center;
					width: 20%;
					padding: 30rpx 20rpx;
					border: 4rpx solid #cdcdcd;
					border-radius: 20rpx;

					.backImg {
						width: 50rpx;
						height: 50rpx;
						margin: 0 0 20rpx;
					}
				}
			}

			.viewOne_b {
				width: 100%;
				padding: 20rpx 0;

				.backImg {
					width: 50rpx;
					height: 50rpx;
				}

				.viewOne_b_a {
					display: flex;
					align-items: center;
					padding: 30rpx 20rpx;
					border: 4rpx solid #47afff;
					border-radius: 20rpx;

					.backImg {
						margin: 0 20rpx 0;
					}
				}
			}

			.viewOne_c {
				width: 100%;
				padding: 20rpx 0;

				.backImg {
					width: 50rpx;
					height: 50rpx;
				}

				.viewOne_c_a {
					margin: 0 0 20rpx;
					display: flex;
					align-items: center;
					justify-content: flex-end;

					.backImg {
						width: 40rpx;
						height: 40rpx;
						margin-right: 10rpx;
					}

					text {
						color: #39A1FF;
						font-size: 24rpx;
					}
				}

				.viewOne_c_b {
					display: flex;
					align-items: center;
					padding: 30rpx 20rpx;
					border: 4rpx solid #cdcdcd;
					border-radius: 20rpx;

					.backImg {
						margin: 0 20rpx 0;
					}
				}
			}
		}

		// 特定人群登录提示样式
		.auth-notice {
			margin: 20rpx;
			padding: 16rpx 20rpx;
			background: rgba(240, 248, 255, 0.8);
			border: 1rpx solid #e3f2fd;
			border-radius: 8rpx;
			box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
			
			.auth-notice-content {
				display: flex;
				align-items: center;
				justify-content: space-between;
				
				.auth-notice-icon {
					font-size: 24rpx;
					margin-right: 12rpx;
					color: #2196f3;
				}
				
				.auth-notice-text {
					flex: 1;
					font-size: 24rpx;
					color: #666;
					line-height: 1.3;
				}
				
				.auth-notice-close {
					font-size: 32rpx;
					color: #999;
					padding: 0 8rpx;
					cursor: pointer;
					
					&:hover {
						color: #666;
					}
				}
			}
		}




		.viewTwo {
			// margin-top: 3%;
			margin-left: 3%;
			width: 94%;

			.viewTitle {
				 font-size: 30rpx;
				color: #111111;
				 line-height: 34rpx;
				text-align: right;
				 display: inline-block;
				 font-weight: 600;
				margin: 10rpx 0 32rpx;
			}

			.custom-tab-bar {
				display: flex;
				justify-content: space-around;
				align-items: center;
			}

			.tab-item {
				display: flex;
				flex-direction: column;
				align-items: center;

				.tab-view {
					width: 100rpx;
					height: 100rpx;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
				}

				.tabA {
					background-image: linear-gradient(90deg, #4ED1FF, #287BFF);
					border: 1px solid #4ED1FF;
				}

				.tabB {
					background-color: #ffffff;
					border: 1px solid #cdcdcd;
				}
			}

			.tab-icon {
				width: 50rpx;
				height: 50rpx;
			}

			.tab-text {
				font-size: 24rpx;
				margin-top: 20rpx;
				color: #333333;
			}


	}
	.devList {
		padding: 0rpx 20rpx;
		display: flex;
		flex-wrap: wrap;


		.devView {
			width: 700rpx;
			 min-height: 330rpx;
			//background-color: #ecf5ff;
			border-radius: 32rpx;
			padding: 1.5%;
			margin: 10rpx 0;
			display: grid;
			grid-template-columns: 20% 55% 25%;


		.grid-item1 {

			border-radius: 22rpx 0 0 22rpx;

		  background-color: rgba(255, 255, 255, 0.8);


		  display: flex;
		  flex-wrap: wrap;
		  // width: max-content;


		  text {
			  margin-top: 10rpx;
		  color: #98968d;
		  white-space: nowrap;
		  }
		  .null4 {
			  margin-left: 10rpx;
			  image {
				  padding: 5rpx;
				  width: 42rpx;
				  height: 42rpx;
			  }
		  }

		}
		.grid-item2 {

		  background-color: rgba(255, 255, 255, 0.8);
		  // padding: 2.5%;
		  display: flex;
		  flex-wrap: wrap;

		   text {
		   width:100%;
		   color: #000000;
		   margin-top: 10rpx;
		   white-space: nowrap;
		   }
		}
		.grid-item3 {
			// border-style: 0px dashed red;
		  background-color: rgba(255, 255, 255, 0.8);
		   border-radius: 0 22rpx 22rpx 0;
		display: flex;
		flex-wrap: wrap;

		.dev_content {
			// margin-left: 20%;

			height: 150rpx;
			width: 150rpx;
			text-align: center;
						image {

							width: 180rpx;
							height: 180rpx;

						}
           }

		  .null3{

			   margin-top: 7rpx;
		  	 width: 100%;
			  height: 30%;
			  display: flex;
			  flex-direction: row-reverse;
			  image{
				  padding: 3%;
				 width: 60rpx;
				 height: 50rpx; /* 高度根据宽度自动调整 */

			  }
			  text{
				  width: 80%;
				  height: 80%;
			  }

				.is_Connect{
					margin-top: 25%;
					margin-left: 70%;
					width: 20rpx;
					height: 20rpx;
					border-radius: 10rpx;
					background-color: #00ff00;
				}

		  }

		}


		}
	}
		.noDevices{
			// margin-left: 3%;
			// width: 94%;
			height: 500rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			flex-direction: column;




			.noDevices_image{

				height: 40%;
				width: 40%;

			}
			.noDevices_text {
				color: #cdcdcd;
				text-align: center;
			}

		}


	// .noDevices_1:image{

	// 	height: 20rpx;
	// }



</style>