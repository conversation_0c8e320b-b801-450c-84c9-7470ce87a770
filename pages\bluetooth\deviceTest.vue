<template>
	<view class="test-page">
		<view class="container">
			<view class="header-card">
				<view class="test-count">当前设备测试记录：{{ testRecords.length }}条</view>
				<view class="action-buttons">
					<button class="btn btn-warning" @click="clearRecords">清空记录</button>
					<button class="btn btn-success" @click="exportToExcel">导出Excel</button>
					<button class="btn btn-primary" @click="copyToClipboard">复制到剪贴板</button>
				</view>
			</view>
			
			<view class="record-list" v-if="testRecords.length > 0">
				<view class="record-item" v-for="(record, index) in testRecords" :key="index">
					<view class="record-header">
						<view class="device-id">设备编号：{{ record.deviceNumber }}</view>
						<view :class="['test-result', record.isNormal ? 'success' : 'error']">
							{{ record.isNormal ? '正常' : '异常' }}
						</view>
					</view>
					<view class="record-info">
						<view class="info-item">
							<text class="label">MAC地址：</text>
							<text class="value">{{ record.macAddress }}</text>
						</view>
						<view class="info-item">
							<text class="label">测试指令：</text>
							<text class="value command">{{ record.command }}</text>
						</view>
						<view class="info-item">
							<text class="label">测试时间：</text>
							<text class="value">{{ formatTimestamp(record.timestamp) }}</text>
						</view>
					</view>
				</view>
			</view>
			
			<view class="empty-records" v-if="testRecords.length === 0">
				<image src="@/static/image/暂无设备.png" class="empty-image"></image>
				<text class="empty-text">暂无测试记录，请进行设备测试</text>
			</view>
		</view>
		
		<!-- Toast提示 -->
		<view class="toast-container" v-if="showToast">
			<view class="toast-message" :class="toastType">{{ toastMessage }}</view>
		</view>
		
		<!-- 确认对话框 -->
		<view class="modal-overlay" v-if="showConfirm">
			<view class="modal-content confirm-modal">
				<view class="confirm-title">确认操作</view>
				<view class="confirm-message">{{ confirmMessage }}</view>
				<view class="confirm-actions">
					<button class="btn btn-secondary" @click="cancelAction">取消</button>
					<button class="btn btn-primary" @click="confirmAction">确定</button>
				</view>
			</view>
		</view>
		
		<!-- 隐藏的canvas，用于生成图片 -->
		<!-- #ifdef MP-WEIXIN -->
		<canvas canvas-id="dataCanvas" style="position: fixed; top: -9999px; left: -9999px; width: 750px; height: 1000px;"></canvas>
		<!-- #endif -->
	</view>
</template>

<script>
// 导入moment.js
import moment from 'moment'

// 导入excel处理库
// #ifdef APP-PLUS
// const ExcelJS = require('exceljs'); // 如果已安装exceljs库
// #endif

export default {
	data() {
		return {
			testRecords: [],
			showToast: false,
			toastMessage: '',
			toastType: 'default',
			showConfirm: false,
			confirmMessage: '',
			confirmCallback: null
		}
	},
	onLoad() {
		// 从本地存储加载测试记录
		this.loadTestRecords();
	},
	onShow() {
		// 页面显示时刷新记录
		this.loadTestRecords();
	},
	methods: {
		// 使用moment.js格式化时间戳
		formatTimestamp(timestamp) {
			if (!timestamp) return '';
			
			// 如果timestamp已经是YYYY-MM-DD HH:mm:ss格式，直接返回
			if (typeof timestamp === 'string' && timestamp.match(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/)) {
				return timestamp;
			}
			
			// 使用moment.js格式化
			return moment(timestamp).format('YYYY-MM-DD HH:mm:ss');
		},
		
		// 格式化当前时间用于导出
		formatCurrentTime() {
			return moment().format('YYYY-MM-DD HH:mm:ss');
		},
		loadTestRecords() {
			try {
				const records = uni.getStorageSync('deviceTestRecords');
				if (records) {
					this.testRecords = JSON.parse(records);
				}
			} catch (e) {
				console.error('加载测试记录失败:', e);
				this.showCustomToast({
					message: '加载记录失败',
					type: 'error'
				});
			}
		},
		clearRecords() {
			if (this.testRecords.length === 0) {
				this.showCustomToast({
					message: '没有记录可清空',
					type: 'warning'
				});
				return;
			}
			
			this.showConfirmDialog('确定要清空所有测试记录吗？此操作不可撤销。', () => {
				// 清空记录
				this.testRecords = [];
				uni.setStorageSync('deviceTestRecords', JSON.stringify(this.testRecords));
				
				// 重置设备编号计数器
				uni.setStorageSync('deviceTestCounter', 0);
				
				// 清空MAC地址映射表
				uni.setStorageSync('deviceMacMapping', JSON.stringify({}));
				
				this.showCustomToast({
					message: '已清空所有测试记录',
					type: 'default'
				});
			});
		},
		exportToExcel() {
			if (this.testRecords.length === 0) {
				this.showCustomToast({
					message: '没有记录可导出',
					type: 'warning'
				});
				return;
			}
			
			// #ifdef APP-PLUS
			this.exportExcelForApp();
			// #endif
			
			// #ifdef MP-WEIXIN
			this.exportForWeixin();
			// #endif
			
			// #ifndef APP-PLUS || MP-WEIXIN
			this.showCustomToast({
				message: '当前环境不支持Excel导出',
				type: 'warning'
			});
			// #endif
		},
		exportExcelForApp() {
			// 创建工作簿和工作表
			try {
				// 创建CSV内容作为替代方案
				let csvContent = "设备编号,MAC地址,测试结果,测试指令,测试时间\n";
				
				this.testRecords.forEach(record => {
					const formattedTime = this.formatTimestamp(record.timestamp);
					csvContent += `${record.deviceNumber},${record.macAddress},${record.isNormal ? '正常' : '异常'},${record.command},${formattedTime}\n`;
				});
				
				// 保存为CSV文件
				const fileName = `设备测试记录_${new Date().getTime()}.csv`;
				
				// #ifdef APP-PLUS
				// 检查平台
				uni.getSystemInfo({
					success: (sysInfo) => {
						if (sysInfo.platform === 'android') {
							// Android平台：保存到Downloads目录
							this.saveToDownloadsAndroid(csvContent, fileName);
						} else if (sysInfo.platform === 'ios') {
							// iOS平台：使用原有方案
							this.saveToDocumentsIOS(csvContent, fileName);
						} else {
							// 其他平台使用默认方案
							this.saveToDefaultLocation(csvContent, fileName);
						}
					},
					fail: () => {
						// 获取系统信息失败，使用默认方案
						this.saveToDefaultLocation(csvContent, fileName);
					}
				});
				// #endif
				
			} catch (error) {
				console.error('Excel导出失败', error);
				this.showCustomToast({
					message: '导出失败：' + error.message,
					type: 'error'
				});
			}
		},
		
		// Android平台：保存到Downloads目录
		saveToDownloadsAndroid(csvContent, fileName) {
			try {
				// 请求存储权限
				const permissions = [
					'android.permission.WRITE_EXTERNAL_STORAGE',
					'android.permission.READ_EXTERNAL_STORAGE'
				];
				
				plus.android.requestPermissions(permissions, (result) => {
					console.log('权限申请结果:', result);
					
					// 检查权限是否获取成功
					const hasPermission = result.granted && result.granted.length > 0;
					
					if (hasPermission) {
						// 有权限，保存到Downloads目录
						this.writeFileToDownloads(csvContent, fileName);
					} else {
						// 没有权限，降级到应用私有目录
						console.log('没有外部存储权限，使用应用私有目录');
						this.saveToDefaultLocation(csvContent, fileName);
					}
				}, (error) => {
					console.error('权限申请失败:', error);
					// 权限申请失败，降级到应用私有目录
					this.saveToDefaultLocation(csvContent, fileName);
				});
				
			} catch (error) {
				console.error('Android保存失败:', error);
				this.saveToDefaultLocation(csvContent, fileName);
			}
		},
		
		// 写入文件到Downloads目录
		writeFileToDownloads(csvContent, fileName) {
			try {
				// 获取Android Environment类
				const Environment = plus.android.importClass('android.os.Environment');
				const File = plus.android.importClass('java.io.File');
				const FileOutputStream = plus.android.importClass('java.io.FileOutputStream');
				const OutputStreamWriter = plus.android.importClass('java.io.OutputStreamWriter');
				
				// 获取Downloads目录
				const downloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS);
				const file = new File(downloadsDir, fileName);
				
				// 创建文件输出流
				const fos = new FileOutputStream(file);
				const osw = new OutputStreamWriter(fos, 'UTF-8');
				
				// 写入内容
				osw.write(csvContent);
				osw.flush();
				osw.close();
				fos.close();
				
				// 获取文件路径
				const filePath = file.getAbsolutePath();
				console.log('文件保存成功:', filePath);
				
				// 通知媒体扫描器更新文件
				this.notifyMediaScanner(filePath);
				
				// 显示成功提示
				uni.showModal({
					title: '导出成功',
					content: `文件已保存到手机Downloads目录：\n${fileName}\n\n您可以通过以下方式查看：\n1. 打开手机"文件管理器"\n2. 进入"下载"或"Downloads"文件夹\n3. 找到文件并打开`,
					confirmText: '打开文件夹',
					cancelText: '我知道了',
					success: (res) => {
						if (res.confirm) {
							// 尝试打开Downloads目录
							this.openDownloadsFolder();
						}
					}
				});
				
			} catch (error) {
				console.error('写入Downloads目录失败:', error);
				// 降级到应用私有目录
				this.saveToDefaultLocation(csvContent, fileName);
			}
		},
		
		// 通知媒体扫描器更新文件
		notifyMediaScanner(filePath) {
			try {
				const Intent = plus.android.importClass('android.content.Intent');
				const Uri = plus.android.importClass('android.net.Uri');
				const File = plus.android.importClass('java.io.File');
				
				const file = new File(filePath);
				const intent = new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE);
				intent.setData(Uri.fromFile(file));
				
				const main = plus.android.runtimeMainActivity();
				main.sendBroadcast(intent);
				
				console.log('媒体扫描器通知发送成功');
			} catch (error) {
				console.error('通知媒体扫描器失败:', error);
			}
		},
		
		// 尝试打开Downloads文件夹
		openDownloadsFolder() {
			try {
				const Intent = plus.android.importClass('android.content.Intent');
				const Uri = plus.android.importClass('android.net.Uri');
				const Environment = plus.android.importClass('android.os.Environment');
				
				const downloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS);
				const intent = new Intent(Intent.ACTION_VIEW);
				intent.setDataAndType(Uri.fromFile(downloadsDir), 'resource/folder');
				
				const main = plus.android.runtimeMainActivity();
				main.startActivity(intent);
				
			} catch (error) {
				console.error('打开Downloads文件夹失败:', error);
				this.showCustomToast({
					message: '无法自动打开文件夹，请手动查找',
					type: 'warning'
				});
			}
		},
		
		// iOS平台：保存到Documents目录
		saveToDocumentsIOS(csvContent, fileName) {
			try {
				// iOS使用uni.saveFile API
				const tempFilePath = `${plus.io.convertLocalFileSystemURL('_doc/')}temp_${fileName}`;
				
				// 先写入临时文件
				plus.io.resolveLocalFileSystemURL(plus.io.convertLocalFileSystemURL('_doc/'), (entry) => {
					entry.getFile(`temp_${fileName}`, { create: true, exclusive: false }, (fileEntry) => {
						fileEntry.createWriter((writer) => {
							writer.onwrite = () => {
								// 使用uni.saveFile让用户选择保存位置
								uni.saveFile({
									tempFilePath: fileEntry.toLocalURL(),
									success: (res) => {
										console.log('iOS文件保存成功:', res.savedFilePath);
										this.showCustomToast({
											message: '文件已保存，可在"文件"应用中查看',
											type: 'default'
										});
									},
									fail: (err) => {
										console.error('iOS保存失败:', err);
										this.showCustomToast({
											message: '保存失败，请重试',
											type: 'error'
										});
									}
								});
							};
							writer.onerror = (e) => {
								console.error('iOS写入失败:', e);
								this.showCustomToast({
									message: '文件写入失败',
									type: 'error'
								});
							};
							writer.write(new Blob([csvContent], { type: 'text/csv' }));
						});
					});
				});
				
			} catch (error) {
				console.error('iOS保存失败:', error);
				this.saveToDefaultLocation(csvContent, fileName);
			}
		},
		
		// 默认保存方案（应用私有目录）
		saveToDefaultLocation(csvContent, fileName) {
			try {
				// 获取应用存储路径
				const documentsDir = plus.io.convertLocalFileSystemURL('_doc/');
				const filePath = `${documentsDir}${fileName}`;
				
				// 写入文件
				plus.io.resolveLocalFileSystemURL(documentsDir, (entry) => {
					entry.getFile(fileName, { create: true, exclusive: false }, (fileEntry) => {
						fileEntry.createWriter((writer) => {
							writer.onwrite = () => {
								this.showCustomToast({
									message: `文件已保存到应用目录：${filePath}`,
									type: 'default'
								});
								
								// 提示用户文件位置
								setTimeout(() => {
									uni.showModal({
										title: '文件保存位置',
										content: `文件已保存到应用私有目录。\n\n注意：此目录下的文件只能通过应用内部访问，无法在文件管理器中直接查看。\n\n建议使用"复制到剪贴板"功能将数据粘贴到其他应用中。`,
										confirmText: '复制数据',
										cancelText: '我知道了',
										success: (res) => {
											if (res.confirm) {
												this.copyToClipboard();
											}
										}
									});
								}, 1000);
							};
							writer.onerror = (e) => {
								console.error('写入文件失败', e);
								this.showCustomToast({
									message: '导出失败，写入文件错误',
									type: 'error'
								});
							};
							writer.write(new Blob([csvContent], { type: 'text/csv' }));
						}, (error) => {
							console.error('创建writer失败', error);
							this.showCustomToast({
								message: '导出失败，创建writer错误',
								type: 'error'
							});
						});
					}, (error) => {
						console.error('获取文件失败', error);
						this.showCustomToast({
							message: '导出失败，获取文件错误',
							type: 'error'
						});
					});
				}, (error) => {
					console.error('获取目录失败', error);
					this.showCustomToast({
						message: '导出失败，获取目录错误',
						type: 'error'
					});
				});
			} catch (error) {
				console.error('默认保存失败', error);
				this.showCustomToast({
					message: '导出失败：' + error.message,
					type: 'error'
				});
			}
		},
		exportForWeixin() {
			try {
				// 显示加载中提示
				uni.showLoading({
					title: '正在处理...'
				});
				
				// 创建数据内容（使用Tab分隔的TXT格式，简单通用）
				let txtContent = "设备编号\tMAC地址\t测试结果\t测试指令\t测试时间\n";
				
				this.testRecords.forEach(record => {
					const formattedTime = this.formatTimestamp(record.timestamp);
					txtContent += `${record.deviceNumber}\t${record.macAddress}\t${record.isNormal ? '正常' : '异常'}\t${record.command}\t${formattedTime}\n`;
				});
				
				// 隐藏加载提示
				uni.hideLoading();
				
				// 直接提供多种选择方案
				uni.showModal({
					title: '选择导出方式',
					content: '微信小程序限制了文件导出功能，请选择以下方式之一：\n\n1. 复制到剪贴板：可粘贴到Excel/WPS\n2. 分享给好友：通过微信发送数据\n3. 保存到相册：生成图片保存',
					confirmText: '复制数据',
					cancelText: '更多选项',
					success: (res) => {
						if (res.confirm) {
							// 复制到剪贴板
							this.copyDataToClipboard(txtContent);
						} else {
							// 显示更多选项
							this.showMoreExportOptions(txtContent);
						}
					}
				});
				
			} catch (error) {
				console.error('导出准备失败', error);
				uni.hideLoading();
				this.showCustomToast({
					message: '导出失败：' + error.message,
					type: 'error'
				});
			}
		},
		
		// 复制数据到剪贴板
		copyDataToClipboard(txtContent) {
			uni.setClipboardData({
				data: txtContent,
				success: () => {
					uni.showModal({
						title: '复制成功',
						content: '数据已复制到剪贴板！\n\n使用方法：\n1. 打开Excel或WPS表格应用\n2. 新建空白表格\n3. 点击A1单元格\n4. 长按选择"粘贴"\n\n数据将自动分列显示',
						confirmText: '我知道了',
						showCancel: false
					});
				},
				fail: (error) => {
					console.error('复制失败:', error);
					this.showCustomToast({
						message: '复制失败，请重试',
						type: 'error'
					});
				}
			});
		},
		
		// 显示更多导出选项
		showMoreExportOptions(txtContent) {
			uni.showActionSheet({
				itemList: ['复制到剪贴板', '生成图片保存', '分享数据'],
				success: (res) => {
					switch (res.tapIndex) {
						case 0:
							// 复制到剪贴板
							this.copyDataToClipboard(txtContent);
							break;
						case 1:
							// 生成图片
							this.generateImageFromData();
							break;
						case 2:
							// 分享数据
							this.shareData(txtContent);
							break;
					}
				}
			});
		},
		
		// 生成图片从数据
		generateImageFromData() {
			try {
				// 创建canvas绘制数据表格
				const ctx = uni.createCanvasContext('dataCanvas', this);
				
				// 设置canvas尺寸
				const canvasWidth = 750;
				const canvasHeight = Math.max(600, this.testRecords.length * 80 + 200);
				
				// 设置背景
				ctx.setFillStyle('#ffffff');
				ctx.fillRect(0, 0, canvasWidth, canvasHeight);
				
				// 设置标题
				ctx.setFillStyle('#333333');
				ctx.setFontSize(32);
				ctx.setTextAlign('center');
				ctx.fillText('设备测试记录', canvasWidth / 2, 50);
				
				// 绘制表头
				const headers = ['设备编号', 'MAC地址', '测试结果', '测试时间'];
				const colWidths = [120, 280, 120, 230];
				let startX = 0;
				const headerY = 120;
				
				ctx.setFillStyle('#f5f5f5');
				ctx.fillRect(0, headerY - 30, canvasWidth, 60);
				
				ctx.setFillStyle('#333333');
				ctx.setFontSize(24);
				ctx.setTextAlign('center');
				
				headers.forEach((header, index) => {
					const x = startX + colWidths[index] / 2;
					ctx.fillText(header, x, headerY);
					startX += colWidths[index];
				});
				
				// 绘制数据行
				ctx.setFontSize(20);
				this.testRecords.forEach((record, index) => {
					const y = headerY + 60 + index * 60;
					startX = 0;
					
					// 交替行背景
					if (index % 2 === 1) {
						ctx.setFillStyle('#fafafa');
						ctx.fillRect(0, y - 25, canvasWidth, 50);
					}
					
					// 绘制数据
					const formattedTime = this.formatTimestamp(record.timestamp);
					const displayTime = formattedTime.split(' ')[0]; // 只显示日期部分
					
					const rowData = [
						record.deviceNumber.toString(),
						record.macAddress.substring(0, 20) + (record.macAddress.length > 20 ? '...' : ''),
						record.isNormal ? '正常' : '异常',
						displayTime
					];
					
					rowData.forEach((data, colIndex) => {
						const x = startX + colWidths[colIndex] / 2;
						ctx.setFillStyle(colIndex === 2 ? (record.isNormal ? '#4cd964' : '#ff3b30') : '#333333');
						ctx.fillText(data, x, y);
						startX += colWidths[colIndex];
					});
				});
				
				// 绘制完成，保存图片
				ctx.draw(false, () => {
					setTimeout(() => {
						uni.canvasToTempFilePath({
							canvasId: 'dataCanvas',
							success: (res) => {
								// 保存到相册
								uni.saveImageToPhotosAlbum({
									filePath: res.tempFilePath,
									success: () => {
										this.showCustomToast({
											message: '图片已保存到相册',
											type: 'default'
										});
									},
									fail: (err) => {
										console.error('保存图片失败:', err);
										this.showCustomToast({
											message: '保存图片失败，请检查相册权限',
											type: 'error'
										});
									}
								});
							},
							fail: (err) => {
								console.error('生成图片失败:', err);
								this.showCustomToast({
									message: '生成图片失败',
									type: 'error'
								});
							}
						}, this);
					}, 1000);
				});
				
			} catch (error) {
				console.error('生成图片失败:', error);
				this.showCustomToast({
					message: '生成图片失败：' + error.message,
					type: 'error'
				});
			}
		},
		
		// 分享数据
		shareData(txtContent) {
			// 创建分享内容
			const currentTime = this.formatCurrentTime();
			const shareContent = `设备测试记录\n生成时间：${currentTime}\n\n${txtContent.replace(/\t/g, ' | ')}`;
			
			// 复制到剪贴板供分享
			uni.setClipboardData({
				data: shareContent,
				success: () => {
					uni.showModal({
						title: '准备分享',
						content: '数据已复制到剪贴板，您可以：\n\n1. 打开微信聊天窗口\n2. 长按输入框选择"粘贴"\n3. 发送给好友或群组\n\n或者粘贴到其他应用中',
						confirmText: '我知道了',
						showCancel: false
					});
				},
				fail: () => {
					this.showCustomToast({
						message: '准备分享失败',
						type: 'error'
					});
				}
			});
		},
		copyToClipboard() {
			if (this.testRecords.length === 0) {
				this.showCustomToast({
					message: '没有记录可复制',
					type: 'warning'
				});
				return;
			}
			
			try {
				// 创建Tab分隔的文本（可以粘贴到Excel）
				let tsvContent = "设备编号\tMAC地址\t测试结果\t测试指令\t测试时间\n";
				
				this.testRecords.forEach(record => {
					const formattedTime = this.formatTimestamp(record.timestamp);
					tsvContent += `${record.deviceNumber}\t${record.macAddress}\t${record.isNormal ? '正常' : '异常'}\t${record.command}\t${formattedTime}\n`;
				});
				
				// 复制到剪贴板
				uni.setClipboardData({
					data: tsvContent,
					success: () => {
						// 显示复制成功的提示框，指导用户如何操作
						uni.showModal({
							title: '复制成功',
							content: '数据已复制到剪贴板，请按以下步骤操作：\n1. 打开Excel或WPS表格\n2. 新建空白表格\n3. 右键单击A1单元格\n4. 选择"粘贴"\n\n表格数据会自动填充到相应列中',
							confirmText: '我知道了',
							showCancel: false
						});
					},
					fail: (error) => {
						console.error('复制到剪贴板失败', error);
						this.showCustomToast({
							message: '复制失败，无法访问剪贴板',
							type: 'error'
						});
					}
				});
			} catch (error) {
				console.error('处理数据失败', error);
				this.showCustomToast({
					message: '复制失败：' + error.message,
					type: 'error'
				});
			}
		},
		showCustomToast(options) {
			this.toastMessage = options.message || '';
			this.toastType = options.type || 'default';
			this.showToast = true;
			
			// 自动关闭
			setTimeout(() => {
				this.showToast = false;
			}, 2000);
			
			// 为了兼容性，同时使用uni.showToast
			uni.showToast({
				title: options.message || '',
				icon: options.type === 'error' ? 'error' : (options.type === 'warning' ? 'none' : 'success'),
				duration: 2000
			});
		},
		showConfirmDialog(message, callback) {
			this.confirmMessage = message;
			this.confirmCallback = callback;
			this.showConfirm = true;
		},
		confirmAction() {
			this.showConfirm = false;
			if (typeof this.confirmCallback === 'function') {
				this.confirmCallback();
			}
		},
		cancelAction() {
			this.showConfirm = false;
		}
	}
}
</script>

<style lang="scss" scoped>
.test-page {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.container {
	padding: 30rpx;
}

.header-card {
	background-color: #ffffff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}

.test-count {
	font-size: 32rpx;
	font-weight: 500;
	color: #333333;
}

.action-buttons {
	display: flex;
	justify-content: space-between;
	gap: 30rpx;
}

.btn {
	flex: 1;
	height: 80rpx;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 20rpx;
	font-weight: 500;
}

.btn-warning {
	background-color: #ff9500;
	color: white;
}

.btn-success {
	background-color: #4cd964;
	color: white;
}

.btn-primary {
	background-color: #47afff;
	color: white;
}

.btn-secondary {
	background-color: #f5f5f5;
	color: #666666;
	border: 2rpx solid #e0e0e0;
}

.record-list {
	margin-top: 30rpx;
}

.record-item {
	background-color: #ffffff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.record-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
	padding-bottom: 20rpx;
	border-bottom: 2rpx solid #f5f5f5;
}

.device-id {
	font-size: 32rpx;
	font-weight: 500;
	color: #333333;
}

.test-result {
	padding: 10rpx 30rpx;
	border-radius: 100rpx;
	font-size: 24rpx;
	
	&.success {
		background-color: #e6f9eb;
		color: #4cd964;
	}
	
	&.error {
		background-color: #ffebeb;
		color: #ff3b30;
	}
}

.record-info {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.info-item {
	display: flex;
	font-size: 28rpx;
	line-height: 1.5;
}

.label {
	color: #666666;
	min-width: 160rpx;
}

.value {
	color: #333333;
	flex: 1;
	
	&.command {
		font-family: monospace;
		word-break: break-all;
	}
}

.empty-records {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 60rpx 0;
	margin-top: 60rpx;
	background-color: #ffffff;
	border-radius: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.empty-image {
	width: 240rpx;
	height: 240rpx;
	margin-bottom: 30rpx;
}

.empty-text {
	font-size: 30rpx;
	color: #999999;
	text-align: center;
	padding: 0 40rpx;
	line-height: 1.5;
}

/* Toast样式 */
.toast-container {
	position: fixed;
	bottom: 100rpx;
	left: 50%;
	transform: translateX(-50%);
	z-index: 9999;
}

.toast-message {
	padding: 20rpx 40rpx;
	background-color: rgba(0, 0, 0, 0.7);
	color: #ffffff;
	border-radius: 10rpx;
	font-size: 28rpx;
	
	&.error {
		background-color: rgba(255, 59, 48, 0.9);
	}
	
	&.warning {
		background-color: rgba(255, 204, 0, 0.9);
	}
}

/* 确认对话框样式 */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 9999;
}

.modal-content {
	background-color: #ffffff;
	border-radius: 20rpx;
	padding: 40rpx;
	width: 560rpx;
}

.confirm-modal {
	display: flex;
	flex-direction: column;
}

.confirm-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333333;
	margin-bottom: 20rpx;
	text-align: center;
}

.confirm-message {
	font-size: 28rpx;
	color: #666666;
	margin-bottom: 40rpx;
	text-align: center;
}

.confirm-actions {
	display: flex;
	justify-content: space-between;
	gap: 30rpx;
}
</style>