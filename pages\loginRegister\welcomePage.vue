<template>
	<view class="welcomeView">
		<view class="welcome_top">
			<view class="logo">
				<image src="@/static/login/logo.png" mode=""></image>
			</view>
			<text class="textWelcome">燃气安全管家</text>
		</view>

		<view class="login_container" v-if="!userToken">
			<view class="login_title">快捷登录</view>
			<view class="login_desc">授权后将使用您的头像和昵称</view>

			<view class="role-selector">
				<text class="role-label">选择角色</text>
				<view class="role-options">
					<view v-for="(role, index) in roleList" :key="index" class="role-item"
						:class="{ 'role-item-active': selectedRoleIndex === index }" @click="selectRole(index)">
						{{ role.name }}
					</view>
				</view>
			</view>

			<view class="privacy_agreement">
				<view class="checkbox_container" @click="togglePrivacyAgreement">
					<view class="checkbox" :class="{ 'checked': isPrivacyAgreed }">
						<text class="checkmark" v-if="isPrivacyAgreed">✓</text>
					</view>
					<text class="agreement_text">
						我已阅读并同意<text class="link" @click.stop="showPrivacy">《用户协议与隐私政策》</text>
					</text>
				</view>
			</view>

			<button class="wx_login_btn" type="primary" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber"
				:disabled="!isPrivacyAgreed" :class="{ 'disabled': !isPrivacyAgreed }">
				一键登录
			</button>
		</view>

		<view class="loadView" v-if="userToken">
			<u-loading-icon mode="circle" size="30" color="#0165FC"></u-loading-icon>
		</view>

		<xc-privacyPopup ref="privacyComponent" position="center" @allowPrivacy="allowPrivacy"></xc-privacyPopup>
	</view>
</template>

<script>
// 1. 导入组件
import navbar from '@/components/navbar/navbar.vue';
import xcPrivacyPopupVue from '../../components/xc-privacyPopup/xc-privacyPopup.vue';
import ChoiceSelectedVue from '../../components/ChoiceSelected/ChoiceSelected.vue';
import {
	toApplet
} from "@/network/api.js"
import {
	baseURL,
	baseURL_a
} from '@/network/base.js';
import { debugLog } from '@/utils/debug.js';
export default {
	components: { navbar, xcPrivacyPopupVue, ChoiceSelectedVue }, // 2. 注册组件
	data() {
		return {
			login: {
				show: false,
				avatar: '@/static/homePage/userIcon.png',
			},
			avatarUrlLL: "https://www.ahhsiot.com/api/file/image?name=userIcon.png",
			modelAdd: {
				userInfo: {
					avatarUrl: "../../static/image/用户头像 (1).png",
					userName: "",
					UserType: '普通用户',
				},
			},
			test: '',
			paymode: [
				{
					type: '普通用户',
					typeid: 1
				},
				{
					type: '运维人员',
					typeid: 2
				}
				// , {
				// 	type: '管理员',
				// 	typeid: 3
				// }
			],
			choiceList: [{
				choiceItemId: "0",
				choiceItemContent: "普通用户"
			},
			{
				choiceItemId: "1",
				choiceItemContent: "运维人员"
			},
			// {
			// 	choiceItemId: "2",
			// 	choiceItemContent: "管理员"
			// },


			],

			choiceContent: "普通用户",
			choiceIndex: 0,
			addRules: {

				'userInfo.ywName': {
					type: 'string',
					required: true,
					message: '请输入真实姓名',
					trigger: ['blur', 'change']
				},
				'userInfo.ywMobile': {
					type: 'string',
					required: true,
					message: '请输入手机号码',
					trigger: ['blur', 'change']
				}

			},
			paytype: '',
			payChannelid: '',
			isSelect: false,//展示类型？
			types: ['类型一', '类型二'],//公司/商户类型
			type: "",//公司/商户类型
			userToken: false,
			primaryBtnCss: {
				width: '574rpx',
				height: '88rpx',
				background: '#0165FC',
				boxShadow: '0rpx 4rpx 24rpx 0rpx rgba(54,150,255,0.4)',
				borderRadius: '44rpx',
				fontSize: '28rpx',
				fontFamily: 'Microsoft YaHei UI-Regular, Microsoft YaHei UI',
				color: '#FFFFFF',
				lineHeight: '28rpx',
				marginBottom: '50rpx'
			},
			wxBtnCss: {
				width: '574rpx',
				height: '88rpx',
				background: '#04BE02',
				borderRadius: '44rpx',
				fontSize: '28rpx',
				color: '#FFFFFF',
				lineHeight: '28rpx'
			},
			infoBtnCss: {
				width: '574rpx',
				height: '88rpx',
				background: '#FFFFFF',
				borderRadius: '44rpx',
				border: '2rpx solid #C1DFFF',
				fontSize: '28rpx',
				fontFamily: 'Microsoft YaHei UI-Regular, Microsoft YaHei UI',
				color: '#333333',
				lineHeight: '28rpx'
			},
			userSign: 'pt',
			roleList: [
				{ name: '普通用户', value: 'pt' },
				{ name: '运维人员', value: 'yw' },
				// { name: '管理员', value: 'admin' }
			],
			selectedRoleIndex: 0,
			isPrivacyAgreed: false // 隐私协议同意状态
		};
	},

	onLoad() {
		try {
			// 安全地设置头像URL
			if (this.modelAdd && this.modelAdd.userInfo && this.modelAdd.userInfo.avatarUrl) {
				uni.setStorageSync("avatarUrl", this.modelAdd.userInfo.avatarUrl);
			}
			console.log(this.modelAdd.userInfo, 'this.modelAdd.userInfo')

			let tokenVal = uni.getStorageSync('token');
			if (tokenVal !== "" && tokenVal != null) {
				this.userToken = true;
				setTimeout(() => {
					uni.switchTab({
						url: "/pages/homePage/homePage"
					})
				}, 1000)
			}

			// 检查隐私授权状态
			this.checkPrivacyStatus();

			// 设置默认角色 - 添加安全检查
			if (this.roleList && this.roleList.length > 0 && this.selectedRoleIndex >= 0 && this.selectedRoleIndex < this.roleList.length) {
				this.userSign = this.roleList[this.selectedRoleIndex].value;
			} else {
				this.userSign = 'pt'; // 默认值
			}
		} catch (error) {
			console.error('页面加载错误:', error);
			// 设置默认值
			this.userSign = 'pt';
		}
	},
	methods: {
		// 检查隐私授权状态 - 暂时禁用以避免触发隐私检查
		checkPrivacyStatus() {
			// 暂时注释掉隐私检查，避免触发后台获取
			// 用户需要手动勾选隐私协议
			console.log('隐私检查已禁用，用户需手动同意隐私协议');
		},
		goLogin() {
			uni.navigateTo({
				url: './login'
			})
		},
		onChoiceClick(e) {
			console.log('选中', e)
			if (this.choiceList && e >= 0 && e < this.choiceList.length) {
				this.choiceIndex = e
				this.choiceContent = this.choiceList[e].choiceItemContent
				this.modelAdd.userInfo.UserType = this.choiceContent
				console.log('this.choiceIndex', this.choiceIndex)
				console.log('this.choiceContent', this.choiceContent)
			} else {
				console.error('选择索引超出范围:', e);
			}
		},
		moreChange(e) {
			console.log('选中', e.target.value)
		},
		getName(e) {
			console.log(e, 'e')

			this.modelAdd.userInfo.userName = e.detail.value;
			console.log(this.modelAdd.userInfo, 'this.modelAdd.userInfo')
		},
		submit() {
			uni.setStorageSync("nkiname", this.modelAdd.userInfo.userName);
			if (this.modelAdd.userInfo.UserType == '普通用户') {
				this.userSign = 'pt';
			}
			else if (this.modelAdd.userInfo.UserType == '运维人员') {
				this.userSign = 'yw';
			}
			// else if (this.modelAdd.userInfo.UserType == '管理员') {
			// 	this.userSign = 'admin';
			// }
		},

		goRegister() {
			uni.navigateTo({
				url: './register'
			})
		},
		onChooseAvatar(e) {

			console.log(e.detail)
			let result = this.uploadFilePromise(e.detail.avatarUrl)
			// let optData = JSON.parse(result.data);
			console.log("ii", this.test)
			uni.setStorageSync("avatarUrl", e.detail.avatarUrl);
			this.modelAdd.userInfo.avatarUrl = e.detail.avatarUrl
			console.log(this.modelAdd.userInfo.avatarUrl, 'this.modelAdd.userInfo.avatarUrl')
		},
		uploadFilePromise(url) {
			return new Promise((resolve, reject) => {
				let a = uni.uploadFile({
					url: baseURL_a + '/file/picUpload', // 仅为示例，非真实的接口地址
					filePath: url,
					name: 'file',
					// formData: {
					// 	user: 'test'
					// },
					success: (res) => {
						console.log("i", res, typeof (res.data))
						this.test = JSON.parse(res.data).data;
						console.log("iii", this.test)
						setTimeout(() => {
							resolve(res.data)
						}, 1000)
					},
					fail: (res) => {
						console.log('upload fail', res)
					}
				});
			})
		},
		saveSign(sign) {
			this.userSign = sign;
		},
		CheckChange(item) {
			console.log(JSON.stringify(item));
		},
		selectitem(index, item) {
			this.payChannelid = item.typeid;

			if (index >= 0 && this.paymode && index < this.paymode.length) {
				this.paytype = this.paymode[index].typeid;
				this.modelAdd.userInfo.UserType = this.paymode[index].type;

				console.log(this.modelAdd.userInfo, 'this.modelAdd.userInfo')
			} else {
				this.paytype = ""
				console.error('支付模式索引超出范围:', index);
			}
		},
		// 选择角色
		selectRole(index) {
			if (this.roleList && index >= 0 && index < this.roleList.length) {
				this.selectedRoleIndex = index;
				this.userSign = this.roleList[index].value;
				console.log('选择角色:', this.roleList[index].name, this.userSign);
			} else {
				console.error('角色选择索引超出范围:', index);
			}
		},
		// 显示隐私协议
		showPrivacy() {
			this.$refs.privacyComponent.openPrivacy();
		},

		// 同意隐私协议
		allowPrivacy() {
			console.log('同意隐私授权')
		},

		// 切换隐私协议同意状态
		togglePrivacyAgreement() {
			this.isPrivacyAgreed = !this.isPrivacyAgreed;
			console.log('隐私协议同意状态:', this.isPrivacyAgreed);
		},

		// 处理手机号获取及登录
		getPhoneNumber(e) {
			try {
				// 检查是否同意隐私协议
				if (!this.isPrivacyAgreed) {
					uni.showToast({
						title: '请先同意用户协议与隐私政策',
						icon: 'none'
					});
					return;
				}

				// 检查事件对象是否有效
				if (!e || !e.detail) {
					uni.showToast({
						title: '获取手机号失败，请重试',
						icon: 'none'
					});
					return;
				}

				// 如果没有授权手机号，直接返回
				if (e.detail.errMsg && e.detail.errMsg.indexOf('deny') > -1) {
					uni.showToast({
						title: '需授权手机号才能登录',
						icon: 'none'
					});
					return;
				}

				// 检查是否获取到手机号授权码
				if (!e.detail.code) {
					uni.showToast({
						title: '获取手机号失败，请重试',
						icon: 'none'
					});
					return;
				}

				// 显示加载状态
				uni.showLoading({
					title: '登录中...'
				});

				uni.login({
					provider: "weixin",
					success: (loginRes) => {
						try {
							console.log('微信登录成功:', loginRes);

							// 安全地获取用户信息
							const userAvatar = (this.modelAdd && this.modelAdd.userInfo && this.modelAdd.userInfo.avatarUrl) ? this.modelAdd.userInfo.avatarUrl : '';
							const userName = (this.modelAdd && this.modelAdd.userInfo && this.modelAdd.userInfo.userName) ? this.modelAdd.userInfo.userName : '用户';

							// 直接调用登录接口，不再使用已废弃的 wx.getUserInfo
							const loginData = {
								code: loginRes.code,
								phoneCode: e.detail.code,
								encrypData: e.detail.encryptedData,
								iv: e.detail.iv,
								userType: this.userSign || 'pt',
								avatar: userAvatar,
								name: userName
							};

							console.log('准备发送登录数据:', loginData);

							toApplet(loginData).then(res => {
								console.log('登录接口响应:', res);

								if (res && res.data) {
									// 保存用户信息到本地
									uni.setStorageSync("userType", this.userSign || 'pt');
									uni.setStorageSync("token", res.data.token);
									uni.setStorageSync("userId", res.data.id);
									uni.setStorageSync("userPhone", res.data.mobile);
									uni.setStorageSync("avatarUrl", loginData.avatar);
									uni.setStorageSync("nkiname", loginData.name);
									uni.setStorageSync("aleamInfo", "1");
									uni.setStorageSync("dateTime", Date.now());
									uni.setStorageSync("userS", res.data.userType);

									// 关闭加载并跳转
									uni.hideLoading();

									// 延迟跳转，确保数据保存完成
									setTimeout(() => {
										uni.switchTab({
											url: "/pages/homePage/homePage"
										});
									}, 100);
								} else {
									throw new Error('登录响应数据格式错误');
								}
							}).catch(err => {
								console.error('登录失败:', err);
								uni.hideLoading();
								uni.showToast({
									title: err.message || '登录失败，请重试',
									icon: 'none'
								});
							});
						} catch (error) {
							console.error('登录处理错误:', error);
							uni.hideLoading();
							uni.showToast({
								title: '登录处理失败',
								icon: 'none'
							});
						}
					},
					fail: (err) => {
						console.error('微信登录失败:', err);
						uni.hideLoading();
						uni.showToast({
							title: '微信登录失败',
							icon: 'none'
						});
					}
				});
			} catch (error) {
				console.error('获取手机号处理错误:', error);
				uni.hideLoading();
				uni.showToast({
					title: '登录处理失败',
					icon: 'none'
				});
			}
		}
	}
}
</script>

<style lang="scss">
page {
	background-color: #f8f8f8;
	height: 100%;
}

.welcomeView {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 0 40rpx;
	height: 100vh;
}

.welcome_top {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-top: 120rpx;
	margin-bottom: 80rpx;

	.logo {
		margin-bottom: 40rpx;

		image {
			width: 280rpx;
			height: 80rpx;
		}
	}

	.textWelcome {
		font-size: 50rpx;
		font-family: Microsoft YaHei UI-Bold, Microsoft YaHei UI;
		font-weight: bold;
		color: #333333;
		line-height: 60rpx;
	}
}

.login_container {
	width: 100%;
	background-color: #ffffff;
	border-radius: 20rpx;
	padding: 50rpx 40rpx;
	box-shadow: 0 0 20rpx rgba(0, 0, 0, 0.05);
	margin-bottom: 60rpx;

	.login_title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 20rpx;
	}

	.login_desc {
		font-size: 28rpx;
		color: #999999;
		margin-bottom: 40rpx;
	}

	.role-selector {
		width: 100%;
		margin-bottom: 40rpx;

		.role-label {
			font-size: 28rpx;
			color: #333333;
			margin-bottom: 20rpx;
			display: block;
		}

		.role-options {
			display: flex;
			justify-content: space-between;

			.role-item {
				flex: 1;
				height: 80rpx;
				line-height: 80rpx;
				text-align: center;
				border: 1px solid #e5e5e5;
				border-radius: 10rpx;
				margin: 0 10rpx;
				font-size: 28rpx;
				color: #666666;
				transition: all 0.3s;

				&:first-child {
					margin-left: 0;
				}

				&:last-child {
					margin-right: 0;
				}

				&.role-item-active {
					background-color: #e6f3ff;
					border-color: #0165FC;
					color: #0165FC;
				}
			}
		}
	}

	.privacy_agreement {
		margin-bottom: 30rpx;

		.checkbox_container {
			display: flex;
			align-items: center;
			cursor: pointer;

			.checkbox {
				width: 32rpx;
				height: 32rpx;
				border: 2rpx solid #ddd;
				border-radius: 6rpx;
				margin-right: 16rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				transition: all 0.3s;

				&.checked {
					background-color: #0165FC;
					border-color: #0165FC;

					.checkmark {
						color: #ffffff;
						font-size: 20rpx;
						font-weight: bold;
					}
				}
			}

			.agreement_text {
				font-size: 24rpx;
				color: #666666;
				flex: 1;

				.link {
					color: #0165FC;
					text-decoration: underline;
				}
			}
		}
	}

	.wx_login_btn {
		width: 100%;
		height: 90rpx;
		border-radius: 45rpx;
		background-color: #07c160;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 32rpx;
		transition: all 0.3s;

		&.disabled {
			background-color: #cccccc;
			color: #999999;
		}

		.wx_icon {
			margin-right: 10rpx;
		}
	}
}

.loadView {
	width: 100%;
	height: 200rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-top: 100rpx;
}
</style>