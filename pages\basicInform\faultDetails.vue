<template>
	<!-- 故障详情 -->
	<view class="faultDetail">
		<view class="form_view rightForm">
			<u--form labelPosition="left" :model="modelPatient" ref="patientForm" labelWidth="120"
				:labelStyle="labelStyle">
				<u-form-item label="故障设备" prop="userInfo.name" borderBottom>
					<u--input v-model="modelPatient.userInfo.name" disabled disabledColor="#ffffff" placeholder="故障设备"
						border="none" inputAlign="right"></u--input>
				</u-form-item>
				<u-form-item label="设备类型" prop="userInfo.equipType" borderBottom>
					<u--input v-model="modelPatient.userInfo.type" disabled disabledColor="#ffffff"
						inputAlign="right" border="none" placeholder="设备类型"></u--input>
				</u-form-item>
				<u-form-item label="设备编号" prop="userInfo.code" borderBottom>
					<u--input v-model="modelPatient.userInfo.code" type="number" disabled disabledColor="#ffffff"
						inputAlign="right" border="none" placeholder="设备编号"></u--input>
				</u-form-item>
				<!-- <u-form-item label="燃气浓度" prop="userInfo.chroma" borderBottom>
					<u--input v-model="modelPatient.userInfo.chroma" disabled disabledColor="#ffffff" inputAlign="right"
						border="none" placeholder="燃气浓度"></u--input>
				</u-form-item> -->
				<u-form-item label="地址" prop="userInfo.address" borderBottom>
					<u--input v-model="modelPatient.userInfo.address" disabled disabledColor="#ffffff"
						inputAlign="right" border="none" placeholder="地址"></u--input>
				</u-form-item>
				<u-form-item label="上报时间" prop="userInfo.createTime" borderBottom>
					<u--input v-model="modelPatient.userInfo.createTime" disabled disabledColor="#ffffff"
						inputAlign="right" border="none" placeholder="请选择上报时间"></u--input>
				</u-form-item>
				<u-form-item label="故障描述" prop="userInfo.description" borderBottom>
					<u--input v-model="modelPatient.userInfo.description" disabled disabledColor="#ffffff"
						inputAlign="right" border="none" placeholder="故障描述"></u--input>
				</u-form-item>
				<u-form-item label="故障照片" prop="userInfo.picUrl" borderBottom>
					<view class="disabledUpload">
						<u-upload :fileList="fileList1" name="1" disabled multiple :maxCount="3"
						:previewFullImage="true" :deletable='false' useBeforeRead></u-upload>
					</view>
				</u-form-item>
			</u--form>
		</view>
	</view>
</template>

<script>
	import {
		cstFault
	} from "@/network/api.js"
	export default {
		data() {
			return {
				showModal: false,
				primaryBtnCss: {
					width: '574rpx',
					height: '88rpx',
					background: '#0165FC',
					boxShadow: '0rpx 4rpx 24rpx 0rpx rgba(54,150,255,0.4)',
					borderRadius: '200rpx',
					fontSize: '28rpx',
					fontFamily: 'Microsoft YaHei UI-Regular, Microsoft YaHei UI',
					color: '#FFFFFF',
					lineHeight: '28rpx'
				},
				disabled: false,
				labelStyle: {
					// fontSize: '28rpx',
					// fontFamily: 'Microsoft YaHei UI-Regular, Microsoft YaHei UI',
					// lineHeight: '28rpx'
				},
				nowDate: Number(new Date()),
				modelPatient: {
					userInfo: {
						name: '',
						equipDev: '',
						equipType: '',
						code: '',
						chroma: '',
						address: '',
						createTime: '',
						description: '',
						picUrl: "",
						type: ''
					},
				},
				fileList1: []
			};
		},
		onLoad(opt) {
			this.init(opt)
		},
		methods: {
			init(opt) { // 初始化数据
				console.log(opt,'opt');
				this.devicDetail(opt.gzId)
			},
			devicDetail(id) {
				cstFault(id).then(res => {
						let resData = res.data;
						if (resData.picUrl != '') {
							let picUrlArry = (resData.picUrl).split(',');
							let fileArry = [];
							for (let i = 0; i < picUrlArry.length; i++) {
								fileArry.push({
									url: picUrlArry[i]
								})
							}
							console.log("21121", fileArry)
							this.fileList1 = fileArry;
						}
						this.modelPatient.userInfo = res.data
						if(this.modelPatient.userInfo.equipType == '001')
						{
							this.modelPatient.userInfo.type = '家用型可燃气体探测器';
						}
						else if(this.modelPatient.userInfo.equipType == '002')
						{
							this.modelPatient.userInfo.type = '地下空间燃气泄漏监测仪';
						}
						else if(this.modelPatient.userInfo.equipType == '003')
						{
							this.modelPatient.userInfo.type = '工商业可燃气体探测器';
						}
						else if(this.modelPatient.userInfo.equipType == '004')
						{
							this.modelPatient.userInfo.type = '地埋式燃气泄漏监测仪';
						}
					})
					.catch((err) => {})
			},
		},
		onReady() {

		}
	};
</script>

<style lang="scss" scoped>
	.faultDetail {
		padding: 0 0 100rpx;
	}
</style>