<script>
	export default {
		onLaunch: function() {
			console.log('App Launch');

			// 全局监听隐私授权
			if (uni.onNeedPrivacyAuthorization) {
				uni.onNeedPrivacyAuthorization((resolve) => {
					console.log('全局隐私授权监听触发');
					// 直接同意隐私授权，避免阻塞
					if (typeof resolve === 'function') {
						try {
							resolve({
								event: 'agree',
								buttonId: 'agree-btn'
							});
							console.log('全局隐私授权已同意');
						} catch (error) {
							console.error('全局隐私授权处理失败:', error);
						}
					}
				});
			}

			// 检查隐私设置
			if (uni.getPrivacySetting) {
				uni.getPrivacySetting({
					success: (res) => {
						console.log('应用启动时隐私设置:', res);
					},
					fail: (err) => {
						console.error('获取隐私设置失败:', err);
					}
				});
			}
		},
		onShow: function() {
			console.log('App Show');
		},
		onHide: function() {
			console.log('App Hide');
		}
	}
</script>

<style lang="scss">
	/* 注意要写在第一行，同时给style标签加入lang="scss"属性 */
	@import "uview-ui/index.scss";

   
	.cityPage {
		.u-index-list__letter__item {
			width: auto !important;
		}

		.u-index-list__letter {
			top: 10px !important;
		}
	}

	.u-cell__body {
		padding: 40rpx !important;
	}

	.btnXFView {
		width: 100%;
		position: fixed;
		bottom: 112rpx;
		left: 0;
	}

	.rightForm {
		.u-form-item__body__right__message {
			text-align: right !important;
			
		}
	}

	.my-list-view {
		.u-cell__body {
			padding: 40rpx 15rpx !important;
		}
	}

  
	.u-form-item__body {
		padding: 30rpx 0 !important;
		
		
		
	}

	
	
	// .u-form-item__body__right {
	// 	text-align: right !important;
	// 	}

	.form_view {
		// padding: 80rpx 50rpx;
		padding: 20rpx 50rpx 60rpx;

		.u-icon__icon {
			margin-left: 24rpx !important;
		}
		.u-upload__button .u-icon__icon {
			margin-left: 0rpx !important;
		}
	}

	.topTabs {
		.u-tabs__wrapper__nav__line {
		// 	width: 32rpx !important;
		// 	height: 4rpx !important;
			background: #0165FC !important;
		// 	border-radius: 52rpx 52rpx 52rpx 52rpx !important;
		}
	}

	.u-popup__content {
		border-radius: 24rpx !important;
	}

	.listView {
		.u-cell__title-text {
			font-size: 32rpx !important;
			line-height: 56rpx !important;
		}

		.u-cell__label {
			font-size: 30rpx !important;
			line-height: 42rpx !important;
		}
	}
	.disabledUpload{
		/* 隐藏所有的上传图标 */
		.u-upload__button {
			display: none !important;
		}
	}
</style>