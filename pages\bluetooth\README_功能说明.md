# 蓝牙连接页面功能说明

## 页面概述
本页面为微信小程序的蓝牙设备连接和控制页面，采用苹果风格设计，提供简洁美观的用户界面。

## 功能按钮布局

### 第一行：主要功能
1. **指令配置** (⚙️)
   - 功能：跳转到指令配置页面
   - 颜色：蓝色渐变 (#007AFF → #5AC8FA)
   - 点击事件：`goToCommandPage()`

2. **OTA升级** (🔄)
   - 功能：启动设备固件升级流程
   - 颜色：橙色渐变 (#FF9500 → #FFCC02)
   - 点击事件：`startOTAUpgrade()`

3. **设备测试** (🔧)
   - 功能：执行设备功能测试
   - 颜色：紫色渐变 (#5856D6 → #AF52DE)
   - 点击事件：`testDevice()`

### 第二行：新增功能
1. **读取** (📖)
   - 功能：读取设备当前状态和数据
   - 颜色：绿色渐变 (#34C759 → #30D158)
   - 指令格式：A5 FE 02 00 00 [CRC]
   - 点击事件：`readDeviceData()`

2. **开关机** (🔌)
   - 功能：控制设备电源状态（开机/关机/重启）
   - 颜色：灰色渐变 (#8E8E93 → #AEAEB2)
   - 指令格式：A5 FE 03 00 01 [action] [CRC]
   - 点击事件：`toggleDevicePower()`

3. **数据上报** (📊)
   - 功能：请求设备主动上报数据
   - 颜色：粉色渐变 (#FF2D92 → #FF375F)
   - 指令格式：A5 FE 04 00 00 [CRC]
   - 点击事件：`reportDeviceData()`

## 设计特点

### 苹果风格设计
- 圆角矩形按钮 (border-radius: 20rpx)
- 渐变色背景
- 柔和的阴影效果
- 平滑的动画过渡
- 响应式触摸反馈

### 用户体验优化
- 按钮按下时缩放效果 (scale: 0.95)
- 悬停时轻微上浮效果
- 清晰的图标和文字标识
- 合理的间距和布局
- 响应式设计适配不同屏幕

### 功能保障
- 连接状态检查
- 蓝牙特征值验证
- 错误处理和用户提示
- 指令构建和CRC校验
- 完整的日志记录

## 隐藏功能
- 查看测试记录：已隐藏，可通过代码恢复
- 指令历史：已隐藏，可通过代码恢复

## 技术实现
- Vue.js 组件化开发
- 微信小程序蓝牙API
- CSS3动画和渐变
- 响应式布局设计
- 模块化代码结构

## 注意事项
1. 所有功能按钮仅在设备连接状态下显示
2. OTA升级过程中隐藏功能按钮
3. 新增功能已预留完整的点击事件处理
4. 指令格式遵循设备通信协议
5. 包含完整的错误处理机制
