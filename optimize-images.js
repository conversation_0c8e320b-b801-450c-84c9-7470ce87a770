const fs = require('fs');
const path = require('path');

// 图片优化建议
const imageOptimizationSuggestions = {
  // 大图片文件 - 建议压缩或使用网络图片
  largeImages: [
    'static/product/fajing.png', // 71.67 KB
    'static/product/yuanxing.png', // 67.13 KB  
    'static/product/fangxing.png', // 61.16 KB
    'static/product/gongshang.png', // 55.78 KB
    'static/product/home.png', // 54.22 KB
    'static/image/自检中.gif', // 44.03 KB
    'static/image/已处理.png', // 21.73 KB
    'static/homePage/dmsIcon.png', // 12.31 KB
    'static/image/暂无设备.png' // 7.11 KB
  ],
  
  // 可以移到分包的图片
  subpackageImages: {
    deviceManage: [
      'static/image/设备正常.png',
      'static/image/设备离线.png', 
      'static/image/设备管理.png',
      'static/image/详情.png',
      'static/image/自检.png',
      'static/image/自检中.gif',
      'static/image/故障.png',
      'static/image/故障信息.png',
      'static/image/关阀.png',
      'static/image/消音.png',
      'static/image/燃气报警.png'
    ],
    userManage: [
      'static/image/用户头像 (1).png',
      'static/image/上报故障.png',
      'static/image/正在审批.png',
      'static/image/已处理.png'
    ]
  }
};

// 创建分包静态资源目录
function createSubpackageStaticDirs() {
  const dirs = [
    'pages/deviceManage/static',
    'pages/userManage/static'
  ];
  
  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`创建目录: ${dir}`);
    }
  });
}

// 移动图片到分包
function moveImagesToSubpackages() {
  Object.keys(imageOptimizationSuggestions.subpackageImages).forEach(subpackage => {
    const images = imageOptimizationSuggestions.subpackageImages[subpackage];
    
    images.forEach(imagePath => {
      const fileName = path.basename(imagePath);
      const targetPath = `pages/${subpackage}/static/${fileName}`;
      
      if (fs.existsSync(imagePath)) {
        try {
          fs.copyFileSync(imagePath, targetPath);
          console.log(`移动图片: ${imagePath} -> ${targetPath}`);
        } catch (error) {
          console.error(`移动图片失败: ${imagePath}`, error.message);
        }
      }
    });
  });
}

// 生成优化报告
function generateOptimizationReport() {
  console.log('\n=== 小程序主包优化报告 ===\n');
  
  console.log('1. 分包结构优化:');
  console.log('   - 主包页面从14个减少到4个');
  console.log('   - 新增设备管理分包 (pages/deviceManage)');
  console.log('   - 新增用户管理分包 (pages/userManage)');
  console.log('   - 保留基础信息分包 (pages/basicInform)');
  console.log('   - 保留蓝牙功能分包 (pages/bluetooth)\n');
  
  console.log('2. 图片资源优化建议:');
  console.log('   大图片文件 (建议压缩或使用CDN):');
  imageOptimizationSuggestions.largeImages.forEach(img => {
    console.log(`   - ${img}`);
  });
  
  console.log('\n   已移动到分包的图片:');
  Object.keys(imageOptimizationSuggestions.subpackageImages).forEach(subpackage => {
    console.log(`   ${subpackage}分包:`);
    imageOptimizationSuggestions.subpackageImages[subpackage].forEach(img => {
      console.log(`   - ${img}`);
    });
  });
  
  console.log('\n3. 进一步优化建议:');
  console.log('   - 使用图片压缩工具压缩大图片');
  console.log('   - 考虑将产品图片上传到CDN');
  console.log('   - 使用webp格式替代png格式');
  console.log('   - 移除未使用的图片资源');
  console.log('   - 考虑使用字体图标替代小图标\n');
}

// 执行优化
function runOptimization() {
  console.log('开始小程序主包优化...\n');
  
  createSubpackageStaticDirs();
  moveImagesToSubpackages();
  generateOptimizationReport();
  
  console.log('优化完成！请重新编译小程序查看效果。');
}

// 如果直接运行此脚本
if (require.main === module) {
  runOptimization();
}

module.exports = {
  imageOptimizationSuggestions,
  createSubpackageStaticDirs,
  moveImagesToSubpackages,
  generateOptimizationReport,
  runOptimization
};
