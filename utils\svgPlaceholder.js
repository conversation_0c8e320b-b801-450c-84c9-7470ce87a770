/**
 * SVG Placeholder Generator
 * Generates SVG placeholder images as data URIs for carousel fallbacks
 */

/**
 * Default configuration for SVG placeholders
 */
const DEFAULT_CONFIG = {
  width: 400,
  height: 200,
  backgroundColor: '#f0f2f5',
  iconColor: '#d9d9d9',
  textColor: '#999999',
  borderRadius: 32 // matches carousel border-radius: 32rpx
}

/**
 * Generate SVG placeholder content as data URI
 * @param {Object} options - Configuration options for the placeholder
 * @param {number} options.width - Width of the placeholder (default: 400)
 * @param {number} options.height - Height of the placeholder (default: 200)
 * @param {string} options.backgroundColor - Background color (default: '#f0f2f5')
 * @param {string} options.iconColor - Icon color (default: '#d9d9d9')
 * @param {string} options.textColor - Text color (default: '#999999')
 * @param {number} options.borderRadius - Border radius in pixels (default: 32)
 * @returns {string} Data URI string for the SVG placeholder
 */
export function generateSvgPlaceholder(options = {}) {
  const config = { ...DEFAULT_CONFIG, ...options }
  
  // Calculate responsive dimensions for icon and text
  const iconSize = Math.min(config.width, config.height) * 0.15
  const centerX = config.width / 2
  const centerY = config.height / 2
  const iconY = centerY - iconSize * 0.5
  const textY = centerY + iconSize * 1.2
  const fontSize = Math.min(config.width, config.height) * 0.08
  
  const svgContent = `
    <svg viewBox="0 0 ${config.width} ${config.height}" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <style>
          .placeholder-bg { fill: ${config.backgroundColor}; }
          .placeholder-icon { fill: ${config.iconColor}; }
          .placeholder-text { 
            fill: ${config.textColor}; 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: ${fontSize}px;
            text-anchor: middle;
            dominant-baseline: middle;
          }
        </style>
      </defs>
      
      <!-- Background with rounded corners -->
      <rect width="100%" height="100%" rx="${config.borderRadius}" ry="${config.borderRadius}" class="placeholder-bg"/>
      
      <!-- Image icon -->
      <g transform="translate(${centerX - iconSize/2}, ${iconY - iconSize/2})">
        <rect width="${iconSize}" height="${iconSize * 0.75}" rx="4" class="placeholder-icon"/>
        <circle cx="${iconSize * 0.25}" cy="${iconSize * 0.25}" r="${iconSize * 0.08}" fill="${config.backgroundColor}"/>
        <polygon points="${iconSize * 0.15},${iconSize * 0.5} ${iconSize * 0.4},${iconSize * 0.3} ${iconSize * 0.65},${iconSize * 0.45} ${iconSize * 0.85},${iconSize * 0.25} ${iconSize * 0.85},${iconSize * 0.65} ${iconSize * 0.15},${iconSize * 0.65}" fill="${config.backgroundColor}"/>
      </g>
      
      <!-- Loading text -->
      <text x="${centerX}" y="${textY}" class="placeholder-text">图片加载中...</text>
    </svg>
  `.trim()
  
  // Encode SVG content as data URI
  const encodedSvg = encodeURIComponent(svgContent)
  return `data:image/svg+xml,${encodedSvg}`
}

/**
 * Generate carousel-specific SVG placeholder
 * Pre-configured for carousel dimensions and styling
 * @param {Object} options - Optional configuration overrides
 * @returns {string} Data URI string for the carousel SVG placeholder
 */
export function generateCarouselPlaceholder(options = {}) {
  const carouselConfig = {
    width: 700,  // matches carousel width in rpx converted to px
    height: 300, // matches carousel height in rpx converted to px
    ...options
  }
  
  return generateSvgPlaceholder(carouselConfig)
}

/**
 * Generate multiple placeholder variants for different scenarios
 * @returns {Object} Object containing different placeholder variants
 */
export function getPlaceholderVariants() {
  return {
    // Standard carousel placeholder
    carousel: generateCarouselPlaceholder(),
    
    // Network error placeholder
    networkError: generateSvgPlaceholder({
      backgroundColor: '#fff2f0',
      iconColor: '#ffccc7',
      textColor: '#ff4d4f'
    }),
    
    // Loading placeholder
    loading: generateSvgPlaceholder({
      backgroundColor: '#f6ffed',
      iconColor: '#b7eb8f',
      textColor: '#52c41a'
    }),
    
    // Small thumbnail placeholder
    thumbnail: generateSvgPlaceholder({
      width: 100,
      height: 100,
      borderRadius: 8
    })
  }
}

/**
 * Validate placeholder configuration
 * @param {Object} config - Configuration to validate
 * @returns {boolean} True if configuration is valid
 */
export function validatePlaceholderConfig(config) {
  if (!config || typeof config !== 'object') {
    return false
  }
  
  const requiredNumericFields = ['width', 'height']
  for (const field of requiredNumericFields) {
    if (config[field] !== undefined && (typeof config[field] !== 'number' || config[field] <= 0)) {
      console.warn(`Invalid ${field}: must be a positive number`)
      return false
    }
  }
  
  const colorFields = ['backgroundColor', 'iconColor', 'textColor']
  const colorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
  for (const field of colorFields) {
    if (config[field] !== undefined && !colorRegex.test(config[field])) {
      console.warn(`Invalid ${field}: must be a valid hex color`)
      return false
    }
  }
  
  return true
}