# 蓝牙功能模块开发指南

本模块实现了通过小程序连接蓝牙设备，并向设备发送指令的功能，主要包含以下页面和功能：

1. 蓝牙连接页面（bluetoothConnect.vue）- 用于扫描、连接蓝牙设备
2. 指令下发页面（commandForm.vue）- 用于填写和发送指令给已连接的蓝牙设备
3. 指令历史页面（commandHistory.vue）- 用于查看历史发送的指令记录

## 当前实现状态

目前已完成了UI界面和交互逻辑的静态实现，包括：

- 页面布局和样式
- 表单验证
- 页面间导航逻辑
- 模拟的蓝牙扫描、连接和指令发送流程
- 本地记录保存机制

## 待实现功能清单

以下功能需要使用微信小程序提供的蓝牙API来实现：

### 1. 蓝牙基础功能
- [x] 检查设备蓝牙是否可用 `wx.openBluetoothAdapter`
- [x] 获取蓝牙适配器状态 `wx.getBluetoothAdapterState`
- [x] 监听蓝牙适配器状态变化 `wx.onBluetoothAdapterStateChange`
- [x] 开始搜寻附近的蓝牙设备 `wx.startBluetoothDevicesDiscovery`
- [x] 获取已发现的蓝牙设备 `wx.getBluetoothDevices`
- [x] 监听新设备的发现事件 `wx.onBluetoothDeviceFound`
- [x] 停止搜寻附近的蓝牙设备 `wx.stopBluetoothDevicesDiscovery`

### 2. 蓝牙设备连接
- [x] 连接低功耗蓝牙设备 `wx.createBLEConnection`
- [x] 断开与低功耗蓝牙设备的连接 `wx.closeBLEConnection`
- [x] 获取蓝牙设备所有服务 `wx.getBLEDeviceServices`
- [x] 获取蓝牙设备某个服务中所有特征值 `wx.getBLEDeviceCharacteristics`

### 3. 数据读写与通知
- [x] 监听特征值变化 `wx.notifyBLECharacteristicValueChange`
- [x] 读取低功耗蓝牙设备的特征值 `wx.readBLECharacteristicValue`
- [x] 向低功耗蓝牙设备特征值中写入数据 `wx.writeBLECharacteristicValue`
- [x] 监听低功耗蓝牙连接状态的改变 `wx.onBLEConnectionStateChange`

### 4. 后端数据同步
- [x] 实现指令记录与后端的同步 API
- [x] 处理同步失败的重试机制
- [x] 在线/离线状态处理

## 注意事项

1. 微信小程序的蓝牙功能需要在真机上调试，模拟器不支持蓝牙功能
2. 需要在 `app.json` 中声明使用蓝牙相关功能权限
3. 用户首次使用蓝牙功能时，需要授权小程序使用蓝牙功能
4. iOS 设备连接蓝牙设备时可能需要用户在系统设置中确认

## 实现参考

微信小程序蓝牙API文档：https://developers.weixin.qq.com/miniprogram/dev/api/device/bluetooth/wx.openBluetoothAdapter.html

基本实现流程：
1. 初始化蓝牙模块
2. 扫描设备
3. 连接设备
4. 获取设备服务和特征
5. 监听/读写数据
6. 断开连接并清理资源

## 错误处理

常见错误及处理方式：
- 蓝牙不可用：提示用户开启蓝牙
- 扫描超时：建议用户检查设备是否开启
- 连接失败：建议用户将设备靠近手机并重试
- 通信失败：检查设备是否断开连接，如有必要重新连接设备 