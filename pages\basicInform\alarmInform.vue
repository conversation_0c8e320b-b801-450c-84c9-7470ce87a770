<template>
	<!-- 报警信息 -->
	<view class="alarmInfrom">
		<!-- 查询筛选 -->
		<view class="queryView">
			<view class="query_a">
				<view class="query_a_m" v-if='!showQuerySheet'>
					<text v-for="(item,index) in modelQuery.queryForm" :key="index">
						{{item}}
					</text>
				</view>
			</view>
			<u-button type="primary" :customStyle="primaryBtnCss" text="筛选" @click="showQuerySheet = true"></u-button>
		</view>
		<u-action-sheet :show="showQuerySheet" title="筛选" @close="showQuerySheet = false">
			<view class="form_view rightForm">
				<u--form labelPosition="left" :model="modelQuery" ref="queryForm" labelWidth="100"
					:labelStyle="labelStyle">
					<u-form-item label="设备类型" prop="queryForm.equipTypeName" borderBottom @click="typeShow = true;">
						<u--input v-model="modelQuery.queryForm.equipTypeName" disabled disabledColor="#ffffff"
							inputAlign="right" border="none" placeholder="请选择设备类型"></u--input>
						<u-icon slot="right" name="arrow-right"></u-icon>
					</u-form-item>
					<u-form-item label="设备编号" prop="queryForm.code" borderBottom>
						<u--input v-model="modelQuery.queryForm.code" maxlength="30" border="none" placeholder="请输入设备编号"
							inputAlign="right"></u--input>
					</u-form-item>
				</u--form>
				<u-picker itemHeight="60" :show="typeShow" :columns="typeColumns" keyName="name" @confirm="typeConfirm"
					@cancel="typeCancel"></u-picker>
				<view class="twoBtn">
					<u-button type="info" text="重置" :customStyle="otherBtnCss" @click="resetQuery"></u-button>
					<u-button type="primary" text="确定" :customStyle="otherBtnCss" @click="saveQuery"></u-button>
				</view>
			</view>
		</u-action-sheet>
		<view class="tabsView">
			<p v-for="(tabs,index) in tabsList" :class="currentTab===index?'activeTabs':'tabsClass'"
				@click="cutTabs(index)">{{tabs}}</p>
		</view>
		<view class="listView">
			<view class="listModule" v-for="(item,index) in bjDataList" :key='index' @click="goFaultDetail(item)">
				<view class="list_left">
					<text>报警</text>
					<image src="@/static/homePage/bjlIcon.png" mode=""></image>
				</view>
				<view class="list_right">
					<view class="list_a">
						<text>{{item.equipCode}}</text>
						<text class="list_a_b">{{item.chroma}}%LEL</text>
					</view>
					<view class="list_b">
						<text>{{item.equipName}}</text>
						<text class="list_b_b">{{item.alarmTime}}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		cstAlarmList
	} from "@/network/api.js"
	export default {
		data() {
			return {
				tabsList: ['未处置', '已解除'],
				currentTab: 0,
				showQuerySheet: false,
				primaryBtnCss: {
					width: '100rpx',
					height: '58rpx',
					// background: '#0165FC',
					borderRadius: '60rpx',
					fontSize: '28rpx',
					// color: '#FFFFFF',
					lineHeight: '28rpx',
					margin: '0'
				},
				otherBtnCss: {
					width: '260rpx',
					borderRadius: '60rpx',
					fontSize: '28rpx',
					fontFamily: 'Microsoft YaHei UI-Regular, Microsoft YaHei UI',
					lineHeight: '28rpx'
				},
				typeShow: false,
				typeColumns: [
					[{
							name: '家用型可燃气体探测器',
							id: '001'
						},
						{
							name: '地下空间燃气泄漏监测仪',
							id: '002'
						},
						{
							name: '工商业可燃气体探测器',
							id: '003'
						},
						{
							name: '地埋式燃气泄漏监测仪',
							id: '004'
						}
					]
				],
				modelQuery: {
					queryForm: {
						equipTypeName: '',
						code: ''
					},
				},
				equipType: '',
				bjDataList: [], //数据
				total: null, //总条数
				pageNum: 1, //第几页
				pageSize: 15 //显示多少条
			};
		},
		onLoad(opt) { // 初始化
			this.init(opt);
			this.getList()
		},
		methods: {
			init(opt) { // 初始化数据
				// console.log(JSON.parse(opt.params));
			},
			resetQuery() {
				this.modelQuery.queryForm.equipTypeName = '';
				this.equipType = '';
				this.modelQuery.queryForm.code = '';
				this.pageNum = 1;
				this.bjDataList = [];
				this.getList();
				this.showQuerySheet = false;
			},
			saveQuery() {
				this.showQuerySheet = false;
				this.pageNum = 1;
				this.bjDataList = [];
				this.getList();
			},
			typeConfirm(e) {
				this.modelQuery.queryForm.equipTypeName = e.value[0].name
				this.equipType = e.value[0].id
				this.typeShow = false;
			},
			typeCancel() {
				this.typeShow = false;
			},
			cutTabs(index) {
				this.currentTab = index;
				this.bjDataList = [];
				this.getList();
			},
			getList() { //获取数据
				let params = {
					alarmStatus: this.currentTab,
					userType: uni.getStorageSync('userType'),
					customerId: uni.getStorageSync('userId'),
					equipType: this.equipType,
					code: this.modelQuery.queryForm.code,
					pageNum: this.pageNum,
					pageSize: this.pageSize
				}
				cstAlarmList(params).then(res => {
					
						this.bjDataList = [...this.bjDataList, ...res.data.list]
					
					this.total = res.total
				})
			},
			goFaultDetail(item) {
				uni.navigateTo({
					url: './alarmDetails?equipmentId=' + item.id
				})
			}
		},
		onReachBottom() { //触底事件
			if (this.pageNum * this.pageSize >= this.total) {
				uni.showToast({
					title: '没有更多数据了',
					icon: 'none',
					duration: 1000
				});
			} else {
				if (this.pageNum <= this.pageNum - 1) {
					setTimeout(() => {
						uni.hideLoading()
					}, 500)
				} else {
					uni.showLoading({
						title: '加载中'
					});
					this.pageNum++
					this.getList()
				}
				setTimeout(() => {
					uni.hideLoading()
				}, 500)
			}
		},
		onReady() {},
	};
</script>
<style>
	page {
		background-color: #F6F6F6;
	}
</style>
<style lang="scss" scoped>
	.alarmInfrom {
		.tabsView {
			width: 100%;
			border-bottom: 2rpx solid #E7E7E7;
			background-color: #ffffff;
			margin-top: 96rpx;
			display: flex;
			justify-content: center;

			.activeTabs {
				width: 150rpx;
				font-size: 30rpx;
				font-family: PingFang SC-Medium, PingFang SC;
				font-weight: 500;
				color: #0052D9;
				line-height: 72rpx;
				border-bottom: 4rpx solid #0052D9;
				margin: 0 60rpx;
				text-align: center;
			}

			.tabsClass {
				width: 150rpx;
				font-size: 30rpx;
				font-family: PingFang SC-Regular, PingFang SC;
				font-weight: 400;
				color: rgba(0, 0, 0, 0.6);
				line-height: 72rpx;
				border-bottom: 4rpx solid transparent;
				margin: 0 60rpx;
				text-align: center;
			}
		}

		.queryView {
			width: 92%;
			padding: 10rpx 4%;
			background-color: #ffffff;
			margin: 10rpx 0;
			position: fixed;
			top: 0;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.query_a {
				.query_a_m {
					display: flex;

					text {
						padding: 0 16rpx;
						font-size: 26rpx;
						color: #87898f;
						font-weight: 600;
						display: block;
					}
				}
			}
		}

		.twoBtn {
			margin-top: 30rpx;
			display: flex;
		}

		.listView {
			margin-top: 10rpx;

			.listModule {
				display: flex;
				background-color: #ffffff;
				margin-bottom: 12rpx;

				.list_left {
					width: 102rpx;
					padding: 26rpx 0 26rpx 32rpx;
					background-image: linear-gradient(to right, rgba(255, 0, 0, 0.5) 1%, rgba(216, 216, 216, 0) 84%);

					text {
						font-weight: 600;
						font-size: 28rpx;
						color: #FF0000;
						line-height: 44rpx;
						display: block;
					}

					image {
						width: 40rpx;
						height: 40rpx;
						margin-top: 12rpx;
					}
				}

				.list_right {
					flex: 1;
					display: flex;
					flex-direction: column;
					justify-content: center;
					padding-right: 32rpx;

					text {
						font-weight: 500;
						font-size: 28rpx;
						line-height: 44rpx;
					}

					.list_a {
						color: #3D3D3D;
						display: flex;
						justify-content: space-between;
						align-items: center;
						margin-bottom: 10rpx;

						.list_a_b {
							font-size: 24rpx;
							color: #FF0000;
							line-height: 44rpx;
						}
					}

					.list_b {
						color: #606266;
						display: flex;
						justify-content: space-between;
						align-items: center;

						.list_b_b {
							font-size: 24rpx;
							color: #616161;
							line-height: 44rpx;
						}
					}
				}
			}
		}
	}
</style>