<template>
	<view class="installation-point-list">
		<!-- 工单信息头部 -->
		<view class="mission-header">
			<view class="mission-title">{{missionInfo.missionName}}</view>
			<view class="mission-info">
				<view class="info-row">
					<text class="info-label">安装设备类型：</text>
					<text class="info-value">{{missionInfo.productName}}</text>
				</view>
				<view class="info-row">
					<text class="info-label">安装位置：</text>
					<text class="info-value">{{missionInfo.pointTypeName}}</text>
				</view>
				<view class="info-row">
					<text class="info-label">设备总数：</text>
					<text class="info-value">{{missionInfo.deviceNum}}</text>
				</view>
				<view class="info-row">
					<text class="info-label">已安装数：</text>
					<text class="info-value">{{missionInfo.installedNum}}</text>
				</view>
				<view class="info-row">
					<text class="info-label">未安装数：</text>
					<text class="info-value">{{missionInfo.deviceNum - missionInfo.installedNum}}</text>
				</view>
				<view class="info-row">
					<text class="info-label">创建时间：</text>
					<text class="info-value">{{missionInfo.createTime ? missionInfo.createTime.substring(0, 10) : ''}}</text>
				</view>
			</view>
		</view>
		
		<!-- 设备列表 -->
		<view class="device-list">
			<view class="list-title">设备安装点列表</view>
			<view v-if="deviceList.length > 0">
				<view class="device-item" v-for="(device, idx) in deviceList" :key="idx">
					<view class="device-info">
						<view class="device-name">{{device.pointName || '未命名设备'}}</view>
						<view class="device-status" :class="device.statusDesc === '未安装' ? 'uninstalled' : 'installed'">
							{{device.statusDesc}}
						</view>
					</view>
					<view class="action-buttons">
						<!-- 设备安装按钮 -->
						<view class="install-btn" @click="goToInstallation(device)" v-if="device.statusDesc === '未安装'">
							<text class="install-text">设备安装</text>
						</view>
						<!-- 设备详情按钮 -->
						<view class="detail-btn" @click="goToDeviceDetail(device)" v-if="device.statusDesc === '已安装'">
							<text class="detail-text">设备详情</text>
						</view>
						<!-- 导航图标 -->
						<view class="nav-icon" @click="openMap(device)" v-if="device.pointLat && device.pointLon">
							<image src="@/static/image/daohang.png" class="nav-image"></image>
						</view>
					</view>
				</view>
			</view>
			<view v-else class="empty-device-list">
				<image src="@/static/image/暂无设备.png" class="empty-image"></image>
				<text class="empty-text">暂无设备数据</text>
			</view>
		</view>
		
		<!-- 地图导航确认弹窗 -->
		<view class="map-confirm-mask" v-if="showMapConfirm" @click="closeMapConfirm"></view>
		<view class="map-confirm-container" v-if="showMapConfirm">
			<view class="map-confirm-title">导航确认</view>
			<view class="map-confirm-content">
				<text>请选择导航应用</text>
				<view class="map-options">
					<view class="map-option" @click="openNavigation('baidu')">
						<text>百度地图</text>
					</view>
					<view class="map-option" @click="openNavigation('gaode')">
						<text>高德地图</text>
					</view>
				</view>
			</view>
			<view class="map-confirm-footer">
				<view class="map-cancel-btn" @click="closeMapConfirm">取消</view>
			</view>
		</view>
		
		<!-- 加载提示 -->
		<u-toast ref="uToast"></u-toast>
		<u-loading-icon :show="loading" mode="circle"></u-loading-icon>
	</view>
</template>

<script>
	import { installationTaskList } from "@/network/api.js";
	
	export default {
		data() {
			return {
				missionInfo: {}, // 工单信息
				deviceList: [], // 设备列表
				loading: false,
				customerId: "",
				missionId: "",
				showMapConfirm: false, // 控制地图导航确认弹窗显示
				currentDevice: null, // 当前选中的设备
			};
		},
		onLoad(options) {
			this.customerId = uni.getStorageSync('userId');
			this.missionId = options.missionId;
			
			// 从页面参数中获取工单信息
			this.missionInfo = {
				missionName: decodeURIComponent(options.missionName || ''),
				productName: decodeURIComponent(options.productName || ''),
				pointTypeName: decodeURIComponent(options.pointTypeName || ''),
				deviceNum: parseInt(options.deviceNum || 0),
				installedNum: parseInt(options.installedNum || 0),
				createTime: decodeURIComponent(options.createTime || '')
			};
			
			this.getDeviceList();
			
			// 监听设备安装成功事件，刷新列表
			uni.$on('refreshInstallationList', () => {
				this.getDeviceList();
			});
		},
		onShow() {
			// 页面显示时刷新列表
			this.getDeviceList();
		},
		onUnload() {
			// 页面卸载时移除事件监听
			uni.$off('refreshInstallationList');
		},
		methods: {
			// 获取设备列表
			getDeviceList() {
				this.loading = true;
				this.deviceList = [];
				
				// 获取设备详情列表
				const params = {
					customerId: this.customerId,
					missionId: this.missionId
				};
				
				installationTaskList(params)
					.then(res => {
						console.log('设备详情列表:', res);
						if (res.code === 200) {
							this.deviceList = res.data || [];
						} else {
							this.$refs.uToast.show({
								message: res.msg || '获取设备详情失败',
								position: 'bottom'
							});
						}
					})
					.catch(err => {
						console.error('获取设备详情错误:', err);
						this.$refs.uToast.show({
							message: '网络错误，请稍后重试',
							position: 'bottom'
						});
					})
					.finally(() => {
						this.loading = false;
					});
			},
			
			// 跳转到设备安装页面
			goToInstallation(device) {
				console.log('跳转到设备安装页面:', device);
				uni.navigateTo({
					url: `/pages/deviceManage/deviceInstallation?missionId=${this.missionId}&pointId=${device.pointId || device.id || ''}&pointName=${encodeURIComponent(device.pointName || '')}&taskId=${encodeURIComponent(device.taskId || '')}&pointIdentify=${encodeURIComponent(device.pointIdentify || '')}`
				});
			},
			
			// 跳转到设备详情页面
			goToDeviceDetail(device) {
				console.log('跳转到设备详情页面:', device);
				uni.navigateTo({
					url: `/pages/deviceManage/deviceInstallation?mode=detail&missionId=${this.missionId}&pointId=${device.pointId || device.id || ''}&pointName=${encodeURIComponent(device.pointName || '')}&taskId=${encodeURIComponent(device.taskId || '')}&pointIdentify=${encodeURIComponent(device.pointIdentify || '')}`
				});
			},
			
			// 打开地图选择弹窗
			openMap(device) {
				if (!device.pointLat || !device.pointLon) {
					this.$refs.uToast.show({
						message: '该设备没有位置信息，无法导航',
						position: 'bottom'
					});
					return;
				}
				
				this.currentDevice = device;
				this.showMapConfirm = true;
			},
			
			// 关闭地图导航确认弹窗
			closeMapConfirm() {
				this.showMapConfirm = false;
				this.currentDevice = null;
			},
			
			// 打开导航应用
			openNavigation(mapType) {
				if (!this.currentDevice || !this.currentDevice.pointLat || !this.currentDevice.pointLon) {
					this.$refs.uToast.show({
						message: '导航信息不完整',
						position: 'bottom'
					});
					return;
				}
				
				const latitude = this.currentDevice.pointLat;
				const longitude = this.currentDevice.pointLon;
				const name = this.currentDevice.pointName || '安装位置';
				
				// 检查当前运行环境
				// #ifdef APP-PLUS
				// App环境下检查权限
				uni.getSystemInfo({
					success: (sysInfo) => {
						if (sysInfo.platform === 'android') {
							// Android平台需要检查权限
							const permissions = ['android.permission.ACCESS_FINE_LOCATION'];
							
							plus.android.requestPermissions(
								permissions,
								(result) => {
									// 检查权限是否获取成功
									const hasPermission = result.granted && result.granted.length > 0;
									
									if (hasPermission) {
										this.navigateToMap(mapType, latitude, longitude, name);
									} else {
										this.$refs.uToast.show({
											message: '请授予位置权限以使用导航功能',
											position: 'bottom'
										});
									}
								},
								(error) => {
									console.error('权限申请失败:', error);
									this.$refs.uToast.show({
										message: '权限申请失败',
										position: 'bottom'
									});
								}
							);
						} else {
							// iOS平台直接导航
							this.navigateToMap(mapType, latitude, longitude, name);
						}
					}
				});
				// #endif
				
				// #ifdef MP-WEIXIN
				// 微信小程序环境下直接导航
				this.navigateToMap(mapType, latitude, longitude, name);
				// #endif
				
				this.closeMapConfirm();
			},
			
			// 导航到地图应用
			navigateToMap(mapType, latitude, longitude, name) {
				// #ifdef APP-PLUS
				// App环境下使用plus API
				let url = '';
				
				if (mapType === 'baidu') {
					// 百度地图URL Scheme
					url = `baidumap://map/direction?destination=${latitude},${longitude}&destinationName=${encodeURIComponent(name)}&coord_type=gcj02&mode=driving`;
				} else if (mapType === 'gaode') {
					// 高德地图URL Scheme
					url = `androidamap://navi?sourceApplication=安装工单&lat=${latitude}&lon=${longitude}&dev=0&style=2`;
				}
				
				// 尝试打开地图应用
				plus.runtime.openURL(url, (err) => {
					if (err) {
						// 打开失败，提示用户安装对应的地图应用
						let appName = mapType === 'baidu' ? '百度地图' : '高德地图';
						let appUrl = mapType === 'baidu' ? 
							'https://apps.apple.com/cn/app/id452186370' : 
							'https://apps.apple.com/cn/app/id461703208';
						
						uni.showModal({
							title: '提示',
							content: `您尚未安装${appName}，是否前往下载？`,
							success: (res) => {
								if (res.confirm) {
									plus.runtime.openURL(appUrl);
								}
							}
						});
					}
				});
				// #endif
				
				// #ifdef MP-WEIXIN
				// 微信小程序环境下使用内置地图导航
				wx.openLocation({
					latitude: parseFloat(latitude),
					longitude: parseFloat(longitude),
					name: name,
					address: name,
					scale: 18,
					success: () => {
						console.log('导航成功');
					},
					fail: (err) => {
						console.error('导航失败:', err);
						this.$refs.uToast.show({
							message: '导航失败，请稍后重试',
							position: 'bottom'
						});
					}
				});
				// #endif
			}
		}
	};
</script>

<style lang="scss" scoped>
.installation-point-list {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
	
	.mission-header {
		background-color: #fff;
		border-radius: 10rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		
		.mission-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 20rpx;
			text-align: center;
		}
		
		.mission-info {
			.info-row {
				display: flex;
				margin-bottom: 15rpx;
				
				&:last-child {
					margin-bottom: 0;
				}
				
				.info-label {
					width: 200rpx;
					font-size: 28rpx;
					color: #666;
					font-weight: bold;
				}
				
				.info-value {
					flex: 1;
					font-size: 28rpx;
					color: #333;
				}
			}
		}
	}
	
	.device-list {
		background-color: #fff;
		border-radius: 10rpx;
		padding: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		
		.list-title {
			font-size: 30rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 20rpx;
			padding-bottom: 15rpx;
			border-bottom: 1rpx solid #eee;
		}
		
		.device-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 20rpx 0;
			border-bottom: 1rpx solid #f5f5f5;
			
			&:last-child {
				border-bottom: none;
			}
			
			.device-info {
				flex: 1;
				
				.device-name {
					font-size: 28rpx;
					color: #333;
					margin-bottom: 10rpx;
				}
				
				.device-status {
					font-size: 24rpx;
					padding: 4rpx 12rpx;
					border-radius: 20rpx;
					display: inline-block;
					
					&.installed {
						background-color: #e8f7ee;
						color: #07c160;
					}
					
					&.uninstalled {
						background-color: #f7f7f7;
						color: #999;
					}
				}
			}
			
			.action-buttons {
				display: flex;
				align-items: center;
				gap: 20rpx;
			}
			
			.install-btn {
				padding: 12rpx 24rpx;
				background: linear-gradient(135deg, #007aff 0%, #5ac8fa 100%);
				border-radius: 30rpx;
				box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
				transition: all 0.3s ease;
				
				&:active {
					transform: scale(0.95);
					box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.2);
				}
				
				.install-text {
					font-size: 24rpx;
					color: #ffffff;
					font-weight: 500;
					white-space: nowrap;
				}
			}
			
			.detail-btn {
				padding: 12rpx 24rpx;
				background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
				border-radius: 30rpx;
				box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.3);
				transition: all 0.3s ease;
				
				&:active {
					transform: scale(0.95);
					box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.2);
				}
				
				.detail-text {
					font-size: 24rpx;
					color: #ffffff;
					font-weight: 500;
					white-space: nowrap;
				}
			}
			
			.nav-icon {
				width: 60rpx;
				height: 60rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				background-color: #f8f8f8;
				border-radius: 50%;
				transition: all 0.3s ease;
				
				&:active {
					transform: scale(0.95);
					background-color: #e8e8e8;
				}
				
				.nav-image {
					width: 40rpx;
					height: 40rpx;
				}
			}
		}
		
		.empty-device-list {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			padding: 100rpx 0;
			
			.empty-image {
				width: 200rpx;
				height: 200rpx;
				margin-bottom: 20rpx;
			}
			
			.empty-text {
				font-size: 28rpx;
				color: #999;
			}
		}
	}
	
	// 地图导航确认弹窗
	.map-confirm-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 1001;
	}
	
	.map-confirm-container {
		position: fixed;
		left: 50%;
		bottom: 0;
		transform: translateX(-50%);
		width: 100%;
		background-color: #fff;
		border-radius: 20rpx 20rpx 0 0;
		z-index: 1002;
		overflow: hidden;
		
		.map-confirm-title {
			padding: 30rpx;
			text-align: center;
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
			border-bottom: 1rpx solid #eee;
		}
		
		.map-confirm-content {
			padding: 30rpx;
			
			text {
				display: block;
				text-align: center;
				margin-bottom: 30rpx;
				color: #666;
				font-size: 28rpx;
			}
			
			.map-options {
				display: flex;
				justify-content: space-around;
				
				.map-option {
					padding: 20rpx 40rpx;
					background-color: #f5f5f5;
					border-radius: 10rpx;
					text-align: center;
					
					text {
						margin-bottom: 0;
						color: #333;
						font-size: 28rpx;
					}
				}
			}
		}
		
		.map-confirm-footer {
			padding: 20rpx 30rpx 50rpx;
			
			.map-cancel-btn {
				text-align: center;
				padding: 20rpx 0;
				color: #999;
				font-size: 28rpx;
			}
		}
	}
}
</style>