<template>
	<view class="bluetooth-page">
		<view class="container">
			<view class="status-card">
				<view class="status-text" :class="[bluetoothStatus.class]">{{bluetoothStatus.text}}</view>
				<view class="scan-indicator" v-if="isScanning">
					<view class="loader"></view>
					<text>正在努力扫描中...</text>
				</view>
				
				<button class="btn btn-primary" @click="startScan" v-if="!isConnected">
					{{ isScanning ? '停止扫描' : '扫描蓝牙设备' }}
				</button>
				<button class="btn btn-secondary" @click="disconnectDevice" v-if="isConnected">
					断开当前设备
				</button>
			</view>
			
			<view class="device-list" v-if="showDeviceList && deviceList.length > 0">
				<view class="card-title">可用设备</view>
				<view class="device-item" v-for="(device, index) in deviceList" :key="index" @click="connectToDevice(device)">
					<view class="device-info">
						<view class="device-name">{{device.name}}</view>
						<view class="device-mac">MAC: {{device.macAddress || device.deviceId}}</view>
						<view class="device-id">ID: {{device.deviceId}}</view>
					</view>
					<button class="btn btn-sm btn-primary connect-btn">连接</button>
				</view>
			</view>
			
			<view class="empty-devices" v-if="showDeviceList && deviceList.length === 0">
				<image src="@/static/image/暂无设备.png" class="empty-image"></image>
				<text class="empty-text">未发现可用设备，请确保设备已开启并靠近手机</text>
			</view>
			
			<button class="btn btn-success command-btn" v-if="isConnected" @click="goToCommandPage">
				指令配置
			</button>
			
			<!-- OTA升级按钮 -->
			<button class="btn btn-warning ota-btn" v-if="isConnected && !otaUpgrading" @click="startOTAUpgrade">
				OTA升级
			</button>
			
			<!-- OTA升级历史验证按钮 - 独立于升级流程 -->
			<button class="btn btn-info verify-status-btn" v-if="isConnected && hasOTAHistory && !otaUpgrading" @click="verifyDeviceUpgradeStatus">
				🔍 验证升级状态
			</button>
			
			<!-- OTA升级进度界面 -->
			<view class="ota-progress-card" v-if="otaUpgrading">
				<view class="card-title">OTA升级进行中</view>
				<view class="ota-info">
					<view class="ota-info-item">
						<text class="info-label">设备ID：</text>
						<text class="info-value">{{currentDeviceId || '获取中...'}}</text>
					</view>
					<view class="ota-info-item">
						<text class="info-label">当前版本：</text>
						<text class="info-value">{{currentVersion || '查询中...'}}</text>
					</view>
					<view class="ota-info-item">
						<text class="info-label">目标版本：</text>
						<text class="info-value">{{targetVersion || 'V2.16'}}</text>
					</view>
					<view class="ota-info-item">
						<text class="info-label">升级状态：</text>
						<text class="info-value status-text" :class="otaStatusClass">{{otaStatus}}</text>
					</view>
				</view>
				
				<view class="progress-container">
					<view class="progress-bar">
						<view class="progress-fill" :style="{width: otaProgress + '%'}"></view>
					</view>
					<text class="progress-text">{{otaProgress}}%</text>
				</view>
				
				<view class="ota-step-info">
					<text class="step-text">{{otaStepText}}</text>
				</view>
				
				<view class="ota-button-group" v-if="!otaCompleted">
					<button class="btn btn-secondary cancel-btn" @click="cancelOTAUpgrade">
						取消升级
					</button>
				</view>
				<view class="ota-button-group" v-if="otaCompleted">
					<button class="btn btn-primary complete-btn" @click="finishOTAUpgrade">
						完成
					</button>
					
					<!-- 版本验证按钮 - 仅在升级完成且已连接设备时显示 -->
					<button v-if="isConnected" @click="checkVersionAfterUpgrade" class="btn btn-secondary verify-btn">
						🔍 验证升级结果
					</button>
				</view>
			</view>
			
			<button class="btn btn-primary test-btn" v-if="isConnected" @click="testDevice">
				设备测试
			</button>
			
			<button class="btn btn-secondary record-btn" @click="goToTestRecordPage">
				查看测试记录
			</button>
			<button class="btn btn-secondary record-btn" @click="goCommandHistory">
				指令历史
			</button>
		</view>
		
		<view class="toast-container" v-if="showToast">
			<view class="toast-message" :class="toastType">{{toastMessage}}</view>
		</view>
	</view>
</template>

<script>
import { getFirmwareVersion } from '@/network/api.js'

export default {
	data() {
		return {
			isScanning: false,
			isConnected: false,
			showDeviceList: false,
			deviceList: [],
			currentDevice: null,
			bluetoothStatus: {
				text: '点击下方按钮开始扫描',
				class: ''
			},
			showToast: false,
			toastMessage: '',
			toastType: 'default',
			isBluetoothAvailable: false,
			isBluetoothInit: false,
			discoveryTimeout: null,
			services: [],
			deviceServicesMap: new Map(),
			deviceCharacteristicsMap: new Map(),
			isConnecting: false,
			isDisconnecting: false,
			// OTA升级相关状态
			otaUpgrading: false,
			otaProgress: 0,
			otaStatus: '准备中',
			otaStatusClass: '',
			otaStepText: '',
			otaCompleted: false,
			currentDeviceId: '',
			currentVersion: '',
			targetVersion: 'V2.16',
			otaTimer: null,
			firmwareUrl: '', // 固件下载地址
			serverVersionInfo: null, // 服务端版本信息
			// 分片传输状态
			currentFragment: 0,
			totalFragments: 0, // 将根据实际固件大小计算
			fragmentSize: 226, // 修改为226字节每片 最大满负荷支持226字节
			fragmentTransferActive: false,
			// 固件文件内容
			firmwareBuffer: null, // 存储固件文件的ArrayBuffer
			firmwarePath: '', // 固件文件本地路径
			firmwareSize: 0, // 固件文件实际大小
			// 设备OTA升级历史标记
			hasOTAHistory: false, // 当前连接的设备是否有OTA升级历史
			deviceOTAHistoryMap: new Map() // 设备MAC地址到OTA历史的映射
		}
	},
	onLoad() {
		// 检查蓝牙是否可用
		this.checkBluetoothAvailable();
	},
	onShow() {
		// 页面显示时检查蓝牙状态
		if (this.isBluetoothInit) {
			this.getBluetoothAdapterState();
		}
	},
	onUnload() {
		// 页面卸载时清理蓝牙资源
		this.cleanupBluetooth();
	},
	onHide() {
		// 页面隐藏时清理资源
		if (this.isScanning) {
			this.stopScan();
		}
	},
	methods: {
		showCustomToast(options) {
			this.toastMessage = options.message || '';
			this.toastType = options.type || 'default';
			this.showToast = true;
			
			// 自动关闭
			setTimeout(() => {
				this.showToast = false;
			}, 2000);
			
			// 为了兼容性，同时使用uni.showToast
			uni.showToast({
				title: options.message || '',
				icon: options.type === 'error' ? 'error' : (options.type === 'warning' ? 'none' : 'success'),
				duration: 2000
			});
		},
		checkBluetoothAvailable() {
			this.bluetoothStatus.text = '正在检查蓝牙可用性...';
			
			// #ifdef APP-PLUS
			// 检查位置权限
			console.log('检查位置服务是否开启...');
			var context = plus.android.importClass("android.content.Context");
			var locationManager = plus.android.importClass("android.location.LocationManager");
			var main = plus.android.runtimeMainActivity();
			var mainSvr = main.getSystemService(context.LOCATION_SERVICE);
			var result = mainSvr.isProviderEnabled(locationManager.GPS_PROVIDER);
			console.log("GPS状态:", result);
			
			if (!result) {
				this.showCustomToast({
					message: '请开启位置服务，蓝牙扫描需要定位权限',
					type: 'warning'
				});
			}
			// #endif
			
			uni.openBluetoothAdapter({
				success: (res) => {
					console.log('蓝牙适配器初始化成功', res);
					this.isBluetoothAvailable = true;
					this.isBluetoothInit = true;
					this.bluetoothStatus.text = '蓝牙已就绪，点击下方按钮开始扫描';
					
					// 监听蓝牙适配器状态变化
					this.onBluetoothAdapterStateChange();
					
					// 获取蓝牙适配器状态
					this.getBluetoothAdapterState();
				},
				fail: (err) => {
					console.error('蓝牙适配器初始化失败', err);
					this.isBluetoothAvailable = false;
					this.isBluetoothInit = false;
					
					if (err.errCode === 10001) {
						this.bluetoothStatus.text = '请检查设备蓝牙是否正常开启';
						this.bluetoothStatus.class = 'error';
					} else {
						this.bluetoothStatus.text = '蓝牙初始化失败: ' + err.errMsg;
						this.bluetoothStatus.class = 'error';
					}
					
					this.showCustomToast({
						message: '蓝牙不可用，请开启设备蓝牙',
						type: 'error'
					});
				}
			});
		},
		getBluetoothAdapterState() {
			uni.getBluetoothAdapterState({
				success: (res) => {
					console.log('蓝牙适配器状态:', res);
					
					if (res.available) {
						this.isBluetoothAvailable = true;
						
						if (res.discovering) {
							this.isScanning = true;
							this.bluetoothStatus.text = '正在扫描附近的蓝牙设备...';
							this.bluetoothStatus.class = 'scanning';
						} else {
							this.isScanning = false;
						}
					} else {
						this.isBluetoothAvailable = false;
						this.bluetoothStatus.text = '蓝牙不可用，请开启设备蓝牙';
						this.bluetoothStatus.class = 'error';
					}
				},
				fail: (err) => {
					console.error('获取蓝牙适配器状态失败', err);
					this.isBluetoothAvailable = false;
				}
			});
		},
		onBluetoothAdapterStateChange() {
			uni.onBluetoothAdapterStateChange((res) => {
				console.log('蓝牙适配器状态变化:', res);
				
				if (res.available) {
					this.isBluetoothAvailable = true;
					
					if (!this.isBluetoothInit) {
						this.isBluetoothInit = true;
						this.bluetoothStatus.text = '蓝牙已就绪，点击下方按钮开始扫描';
						this.bluetoothStatus.class = '';
					}
					
					if (res.discovering) {
						this.isScanning = true;
						this.bluetoothStatus.text = '正在扫描附近的蓝牙设备...';
						this.bluetoothStatus.class = 'scanning';
					} else if (this.isScanning) {
						this.isScanning = false;
						this.bluetoothStatus.text = '扫描已停止，点击按钮重新扫描';
						this.bluetoothStatus.class = '';
					}
				} else {
					this.isBluetoothAvailable = false;
					this.isBluetoothInit = false;
					this.bluetoothStatus.text = '蓝牙已关闭，请开启设备蓝牙';
					this.bluetoothStatus.class = 'error';
					
					if (this.isScanning) {
						this.isScanning = false;
						this.showDeviceList = false;
						this.deviceList = [];
					}
					
					if (this.isConnected) {
						this.isConnected = false;
						this.currentDevice = null;
						this.showCustomToast({
							message: '蓝牙已断开连接',
							type: 'error'
						});
					}
				}
			});
		},
		startScan() {
			if (!this.isBluetoothAvailable) {
				this.checkBluetoothAvailable();
				return;
			}
			
			if (this.isConnected) {
				this.showCustomToast({
					message: '已连接设备，请先断开后再扫描',
					type: 'warning'
				});
				return;
			}
			
			if (this.isScanning) {
				// 如果正在扫描，则停止扫描
				this.stopScan();
				return;
			}
			
			// 先确保之前的扫描已停止，并清理旧的监听器
			try {
				uni.offBluetoothDeviceFound();
				console.log('开始扫描前移除旧的蓝牙设备发现监听');
			} catch (error) {
				console.error('移除蓝牙设备发现监听失败:', error);
			}
			
			uni.stopBluetoothDevicesDiscovery({
				complete: () => {
					console.log('确保之前的扫描已停止');
					
					// 显示loading状态
					this.isScanning = true;
					this.showDeviceList = true;
					this.bluetoothStatus.text = '正在扫描附近的蓝牙设备...';
					this.bluetoothStatus.class = 'scanning';
					this.deviceList = []; // 清空之前的设备列表
					
					// 检查蓝牙权限
					// #ifdef APP-PLUS
					console.log('检查蓝牙权限...');
					var BTPermission = 'android.permission.BLUETOOTH';
					var BTAdminPermission = 'android.permission.BLUETOOTH_ADMIN';
					var BTConnectPermission = 'android.permission.BLUETOOTH_CONNECT';
					var BTScanPermission = 'android.permission.BLUETOOTH_SCAN';
					var locationPermission = 'android.permission.ACCESS_FINE_LOCATION';
					
					// 请求权限
					uni.getSystemInfo({
						success: (sysInfo) => {
							if (sysInfo.platform === 'android') {
								const androidVersion = parseInt(sysInfo.osVersion.split('.')[0]);
								console.log('Android版本:', androidVersion);
								
								// Android 12及以上需要BLUETOOTH_CONNECT和BLUETOOTH_SCAN权限
								if (androidVersion >= 12) {
									plus.android.requestPermissions(
										[BTConnectPermission, BTScanPermission, locationPermission],
										function(resultObj) {
											console.log('蓝牙权限请求结果:', resultObj);
										}
									);
								} else {
									// Android 12以下需要BLUETOOTH, BLUETOOTH_ADMIN和位置权限
									plus.android.requestPermissions(
										[BTPermission, BTAdminPermission, locationPermission],
										function(resultObj) {
											console.log('蓝牙权限请求结果:', resultObj);
										}
									);
								}
							}
						}
					});
					// #endif
					
					// 监听寻找到新设备的事件
					this.onBluetoothDeviceFound();
					
					// 开始搜寻附近的蓝牙设备
					uni.startBluetoothDevicesDiscovery({
						allowDuplicatesKey: true, // 允许重复上报同一设备，以防遗漏
						interval: 0, // 0 表示以最小间隔扫描
						powerLevel: 'high', // 高功率扫描
						success: (res) => {
							console.log('开始搜寻蓝牙设备:', res);
							
							// 获取在蓝牙模块生效期间所有已发现的蓝牙设备
							this.getBluetoothDevices();
							
							// 设置超时，防止一直扫描消耗电量
							this.discoveryTimeout = setTimeout(() => {
								// 如果还在扫描状态，则停止扫描
								if (this.isScanning) {
									this.stopScan();
									
									if (this.deviceList.length === 0) {
										this.showCustomToast({
											message: '未找到蓝牙设备，请确保设备已开启',
											type: 'warning'
										});
									}
								}
							}, 20000); // 延长到20秒后停止扫描
						},
						fail: (err) => {
							console.error('搜寻蓝牙设备失败:', err);
							this.isScanning = false;
							this.bluetoothStatus.text = '扫描失败: ' + err.errMsg;
							this.bluetoothStatus.class = 'error';
							
							this.showCustomToast({
								message: '扫描蓝牙设备失败，请重试',
								type: 'error'
							});
						}
					});
				}
			});
		},
		stopScan() {
			// 清除超时定时器
			if (this.discoveryTimeout) {
				clearTimeout(this.discoveryTimeout);
				this.discoveryTimeout = null;
			}
			
			// 确保移除蓝牙设备发现监听
			try {
				uni.offBluetoothDeviceFound();
				console.log('已移除蓝牙设备发现监听');
			} catch (error) {
				console.error('移除蓝牙设备发现监听失败:', error);
			}
			
			uni.stopBluetoothDevicesDiscovery({
				success: (res) => {
					console.log('停止搜寻蓝牙设备:', res);
					this.isScanning = false;
					this.bluetoothStatus.text = this.deviceList.length > 0 ? 
						'扫描完成，请选择需要连接的设备' : 
						'未发现可用设备，请确保设备已开启并在附近';
					this.bluetoothStatus.class = '';
				},
				fail: (err) => {
					console.error('停止搜寻蓝牙设备失败:', err);
				},
				complete: () => {
					this.isScanning = false;
				}
			});
		},
		getBluetoothDevices() {
			setTimeout(() => {
				uni.getBluetoothDevices({
					success: (res) => {
						console.log('获取蓝牙设备列表成功:', res);
						
						if (res.devices && res.devices.length > 0) {
							const filteredDevices = res.devices.filter(device => 
								device.name && 
								(device.name.includes('SEEFY') 
								// || 
								//  device.name.includes('报警') || 
								//  device.name.includes('K001') || 
								//  device.name.includes('alarm') || 
								//  device.name.toLowerCase().includes('gas')
								)
							);
							
							// 更新设备列表，过滤掉重复的设备
							const newList = [...this.deviceList];
							
							filteredDevices.forEach(device => {
								// 检查是否已存在于列表中
								const exists = newList.some(d => d.deviceId === device.deviceId);
								if (!exists) {
									// 获取MAC地址
									let macAddress = '';
									
									// #ifdef APP-PLUS
									try {
										// 尝试获取Android设备的MAC地址
										const deviceId = device.deviceId;
										if (plus.os.name === 'Android') {
											// Android平台上，deviceId通常就是蓝牙MAC地址
											macAddress = deviceId.replace(/:/g, ':').toUpperCase();
										} else if (plus.os.name === 'iOS') {
											// iOS下无法直接获取MAC，但可以显示UUID
											macAddress = deviceId;
										}
									} catch (e) {
										console.error('获取MAC地址失败:', e);
										macAddress = device.deviceId; // 如果无法获取MAC，则使用deviceId
									}
									// #endif
									
									// #ifndef APP-PLUS
									macAddress = device.deviceId; // 浏览器环境使用deviceId
									// #endif
									
									newList.push({
										name: device.name,
										deviceId: device.deviceId,
										RSSI: device.RSSI,
										advertisData: device.advertisData,
										macAddress: macAddress // 添加MAC地址字段
									});
								}
							});
							
							// 根据信号强度排序
							this.deviceList = newList.sort((a, b) => b.RSSI - a.RSSI);
							
							// 如果还在扫描中，继续更新设备列表
							if (this.isScanning) {
								setTimeout(() => {
									this.getBluetoothDevices();
								}, 2000);
							}
						}
					},
					fail: (err) => {
						console.error('获取蓝牙设备列表失败:', err);
					}
				});
			}, 1000); // 延迟1秒获取设备列表
		},
		onBluetoothDeviceFound() {
			// 务必先移除可能存在的旧监听器，防止重复
			try {
				uni.offBluetoothDeviceFound();
				console.log('清除现有蓝牙设备发现监听');
			} catch (error) {
				console.error('清除蓝牙设备发现监听失败:', error);
			}
			
			uni.onBluetoothDeviceFound((res) => {
				// 确保只在扫描状态下处理设备发现
				if (!this.isScanning) {
					console.log('非扫描状态，忽略设备发现');
					return;
				}
				
				// 在新设备发现时，不立即更新界面，而是收集设备
				if (res.devices && res.devices.length > 0) {
					res.devices.forEach(device => {
						// 放宽过滤条件，仅过滤掉没有名称的设备
						if (device.name && device.name.trim() !== '') {
							// 输出所有找到的设备，方便调试
							console.log(`新发现设备: ${device.name}, ID: ${device.deviceId}, RSSI: ${device.RSSI}`);
							
							// 获取MAC地址
							let macAddress = '';
							
							// #ifdef APP-PLUS
							try {
								const deviceId = device.deviceId;
								if (plus.os.name === 'Android') {
									// Android平台上，deviceId通常就是蓝牙MAC地址
									macAddress = deviceId.replace(/:/g, ':').toUpperCase();
								} else if (plus.os.name === 'iOS') {
									// iOS下无法直接获取MAC，但可以显示UUID
									macAddress = deviceId;
								}
							} catch (e) {
								console.error('获取MAC地址失败:', e);
								macAddress = device.deviceId; // 如果无法获取MAC，则使用deviceId
							}
							// #endif
							
							// #ifndef APP-PLUS
							macAddress = device.deviceId; // 浏览器环境使用deviceId
							// #endif
							
							// 检查是否已存在于列表中
							const exists = this.deviceList.some(d => d.deviceId === device.deviceId);
							if (!exists) {
								this.deviceList.push({
									name: device.name,
									deviceId: device.deviceId,
									RSSI: device.RSSI,
									advertisData: device.advertisData,
									macAddress: macAddress // 添加MAC地址字段
								});
							}
						}
					});
					
					// 根据信号强度排序
					this.deviceList.sort((a, b) => b.RSSI - a.RSSI);
				}
			});
		},
		connectToDevice(device) {
			if (this.isConnected) {
				this.showCustomToast({
					message: '已连接设备，如需连接新设备请先断开',
					type: 'warning'
				});
				return;
			}
			
			if (this.isConnecting) {
				return; // 防止重复点击
			}
			
			this.isConnecting = true;
			this.bluetoothStatus.text = `正在连接到 ${device.name}...`;
			this.bluetoothStatus.class = 'scanning';
			
			// 停止扫描前关闭发现设备监听器，确保彻底清理
			try {
				uni.offBluetoothDeviceFound();
				console.log('连接前移除蓝牙设备发现监听');
			} catch (error) {
				console.error('移除蓝牙设备发现监听失败:', error);
			}
			
			// 停止扫描
			uni.stopBluetoothDevicesDiscovery({
				success: (res) => {
					console.log('连接前停止搜寻蓝牙设备成功:', res);
					// 确保设置扫描状态为false
					this.isScanning = false;
					
					// 创建蓝牙连接
					this.createBLEConnection(device);
				},
				fail: (err) => {
					console.error('连接前停止搜寻蓝牙设备失败:', err);
					// 即使停止扫描失败，也尝试连接设备
					this.isScanning = false;
					this.createBLEConnection(device);
				}
			});
		},
		disconnectDevice() {
			if (!this.isConnected || !this.currentDevice) {
				return;
			}
			
			if (this.isDisconnecting) {
				return; // 防止重复点击
			}
			
			this.isDisconnecting = true;
			this.bluetoothStatus.text = `正在断开与 ${this.currentDevice.name} 的连接...`;
			
			uni.closeBLEConnection({
				deviceId: this.currentDevice.deviceId,
				success: (res) => {
					console.log('断开蓝牙设备成功:', res);
					
					// 清理设备信息
					this.currentDevice = null;
					this.services = [];
					this.deviceServicesMap.delete(this.currentDevice?.deviceId);
					this.deviceCharacteristicsMap.delete(this.currentDevice?.deviceId);
					
					// 更新UI状态
					this.isConnected = false;
					this.bluetoothStatus.text = '设备已断开。点击下方按钮重新扫描。';
					this.bluetoothStatus.class = '';
					this.showDeviceList = false;
					
					this.showCustomToast({
						message: '已断开蓝牙连接',
						type: 'default'
					});
				},
				fail: (err) => {
					console.error('断开蓝牙设备失败:', err);
					
					this.showCustomToast({
						message: '断开连接失败，请重试',
						type: 'error'
					});
				},
				complete: () => {
					this.isDisconnecting = false;
				}
			});
		},
		
		// OTA完成后的简单断开（不显示错误提示）
		disconnectDeviceAfterOTA() {
			if (this.currentDevice && this.isConnected) {
				console.log('🔌 OTA完成后主动断开蓝牙连接');
				
				uni.closeBLEConnection({
					deviceId: this.currentDevice.deviceId,
					success: (res) => {
						console.log('✅ 蓝牙连接已断开:', res);
						this.isConnected = false;
						this.currentDevice = null;
						this.bluetoothStatus.text = '设备已断开，可重新扫描连接';
						this.bluetoothStatus.class = '';
					},
					fail: (err) => {
						console.log('⚠️ 断开蓝牙连接失败(可能已断开):', err);
						// 即使断开失败也更新状态，因为设备可能已经重启断开了
						this.isConnected = false;
						this.currentDevice = null;
						this.bluetoothStatus.text = '设备已断开，可重新扫描连接';
						this.bluetoothStatus.class = '';
					}
				});
			}
		},
		onBLEConnectionStateChange() {
			uni.onBLEConnectionStateChange((res) => {
				console.log('蓝牙连接状态变化:', res);
				
				// 连接意外断开
				if (!res.connected && this.isConnected && this.currentDevice && res.deviceId === this.currentDevice.deviceId) {
					console.log('蓝牙连接已断开');
					
					// 清理设备信息
					this.currentDevice = null;
					this.services = [];
					this.deviceServicesMap.delete(res.deviceId);
					this.deviceCharacteristicsMap.delete(res.deviceId);
					
					// 更新UI状态
					this.isConnected = false;
					this.bluetoothStatus.text = '设备连接已断开';
					this.bluetoothStatus.class = 'error';
					this.showDeviceList = false;
					
					this.showCustomToast({
						message: '蓝牙连接已断开',
						type: 'warning'
					});
				}
			});
		},
		getBLEDeviceServices(deviceId) {
			if (!deviceId) return;
			
			// 先清除之前可能存在的特征值信息
			uni.removeStorageSync('writeServiceId');
			uni.removeStorageSync('writeCharacteristicId');
			uni.removeStorageSync('notifyServiceId');
			uni.removeStorageSync('notifyCharacteristicId');
			
			setTimeout(() => {
				uni.getBLEDeviceServices({
					deviceId: deviceId,
					success: (res) => {
						console.log('获取蓝牙设备服务成功:', res.services);
						
						if (res.services && res.services.length > 0) {
							this.services = res.services;
							this.deviceServicesMap.set(deviceId, res.services);
							
							// 遍历所有服务，以确保不会遗漏
							res.services.forEach(service => {
								this.getBLEDeviceCharacteristics(deviceId, service.uuid);
							});
						}
					},
					fail: (err) => {
						console.error('获取蓝牙设备服务失败:', err);
					}
				});
			}, 1000); // 延迟1秒执行，等待连接稳定
		},
		getBLEDeviceCharacteristics(deviceId, serviceId) {
			if (!deviceId || !serviceId) return;
			
			uni.getBLEDeviceCharacteristics({
				deviceId: deviceId,
				serviceId: serviceId,
				success: (res) => {
					console.log(`获取服务(${serviceId})的特征值成功:`, res.characteristics);
					
					if (res.characteristics && res.characteristics.length > 0) {
						// 存储特征值信息
						if (!this.deviceCharacteristicsMap.has(deviceId)) {
							this.deviceCharacteristicsMap.set(deviceId, new Map());
						}
						this.deviceCharacteristicsMap.get(deviceId).set(serviceId, res.characteristics);
						
						// 查找支持读写通知的特征值
						// 查找可写入的特征
						const writeCharacteristics = res.characteristics.filter(
							char => char.properties.write || char.properties.writeNoResponse
						);
						
						// 查找支持通知的特征
						const notifyCharacteristics = res.characteristics.filter(
							char => char.properties.notify || char.properties.indicate
						);
						
						// 如果找到可写特征，优先保存，以防止被后续服务覆盖
						if (writeCharacteristics.length > 0 && !uni.getStorageSync('writeCharacteristicId')) {
							console.log(`找到可写特征值: 服务ID=${serviceId}, 特征值ID=${writeCharacteristics[0].uuid}`);
							uni.setStorageSync('writeServiceId', serviceId);
							uni.setStorageSync('writeCharacteristicId', writeCharacteristics[0].uuid);
						}
						
						// 启用通知
						if (notifyCharacteristics.length > 0 && !uni.getStorageSync('notifyCharacteristicId')) {
							console.log(`找到通知特征值: 服务ID=${serviceId}, 特征值ID=${notifyCharacteristics[0].uuid}`);
							uni.setStorageSync('notifyServiceId', serviceId);
							uni.setStorageSync('notifyCharacteristicId', notifyCharacteristics[0].uuid);
							this.setupCharacteristicNotifications(deviceId, serviceId, notifyCharacteristics[0].uuid);
						}
					}
				},
				fail: (err) => {
					console.error(`获取服务(${serviceId})的特征值失败:`, err);
				}
			});
		},
		setupCharacteristicNotifications(deviceId, serviceId, characteristicId) {
			// 启用通知
			uni.notifyBLECharacteristicValueChange({
				deviceId: deviceId,
				serviceId: serviceId,
				characteristicId: characteristicId,
				state: true, // 启用通知
				success: (res) => {
					console.log('启用特征值通知成功:', res);
					
					// 监听特征值变化
					this.onBLECharacteristicValueChange();
					
					// 保存通知特征值的UUID
					uni.setStorageSync('notifyCharacteristicId', characteristicId);
					uni.setStorageSync('notifyServiceId', serviceId);
				},
				fail: (err) => {
					console.error('启用特征值通知失败:', err);
				}
			});
		},
		onBLECharacteristicValueChange() {
			uni.onBLECharacteristicValueChange((res) => {
				console.log('收到特征值变化:', res);
				
				// 处理接收到的数据
				if (res.value) {
					const buffer = res.value;
					
					// 首先尝试处理OTA响应
					if (this.handleOTAResponse(buffer)) {
						console.log('✅ OTA响应已处理');
						return;
					}
					
					// 如果不是OTA响应，则按原来的逻辑处理
					const dataView = new DataView(buffer);
					const uint8Array = new Uint8Array(buffer);
					
					// 打印十六进制数据
					let hexData = '';
					for (let i = 0; i < uint8Array.length; i++) {
						hexData += uint8Array[i].toString(16).padStart(2, '0') + ' ';
					}
					console.log('收到数据(HEX):', hexData);
					
					// 尝试解析数据
					try {
						// 判断第一个字节来确定命令类型
						const cmdType = dataView.getUint8(0);
						
						switch (cmdType) {
							case 0x01: // 网络参数配置响应
								if (uint8Array.length >= 2) {
									const status = dataView.getUint8(1);
									let message = '';
									
									if (status === 0x00) {
										message = '网络参数配置成功';
										this.showCustomToast({
											message: message,
											type: 'default'
										});
									} else {
										message = '网络参数配置失败，错误码: ' + status;
										this.showCustomToast({
											message: message,
											type: 'error'
										});
									}
									console.log('网络参数配置响应:', message);
								}
								break;
								
							case 0x02: // 设备状态上报
								if (uint8Array.length >= 4) {
									const deviceStatus = dataView.getUint8(1);
									const batteryLevel = dataView.getUint8(2);
									const signalStrength = dataView.getUint8(3);
									
									let statusText = '';
									switch (deviceStatus) {
										case 0x00: statusText = '正常'; break;
										case 0x01: statusText = '告警'; break;
										case 0x02: statusText = '故障'; break;
										default: statusText = '未知';
									}
									
									const message = `设备状态: ${statusText}, 电量: ${batteryLevel}%, 信号: ${signalStrength}%`;
									console.log(message);
									
									this.showCustomToast({
										message: message,
										type: deviceStatus === 0x00 ? 'default' : 'warning'
									});
								}
								break;
								
							case 0x03: // 气体浓度数据
								if (uint8Array.length >= 3) {
									const gasType = dataView.getUint8(1);
									const concentration = dataView.getUint8(2);
									
									let gasTypeName = '';
									switch (gasType) {
										case 0x01: gasTypeName = '天然气'; break;
										case 0x02: gasTypeName = '液化气'; break;
										case 0x03: gasTypeName = '一氧化碳'; break;
										default: gasTypeName = '未知气体';
									}
									
									const message = `${gasTypeName}浓度: ${concentration}%LEL`;
									console.log(message);
									
									// 浓度高于报警阈值时显示警告
									if (concentration >= 25) {
										this.showCustomToast({
											message: '警告! ' + message,
											type: 'error'
										});
									} else if (concentration >= 10) {
										this.showCustomToast({
											message: '注意! ' + message,
											type: 'warning'
										});
									}
								}
								break;
								
							default:
								// 尝试将数据解析为字符串
								try {
									// 在微信小程序中使用替代方案
									let text = '';
									if (typeof TextDecoder !== 'undefined') {
									const decoder = new TextDecoder('utf-8');
										text = decoder.decode(buffer);
									} else {
										// 小程序环境下的替代方案
										const uint8Array = new Uint8Array(buffer);
										for (let i = 0; i < uint8Array.length; i++) {
											text += String.fromCharCode(uint8Array[i]);
										}
									}
									console.log('收到数据(TEXT):', text);
									
									// 如果是JSON格式，尝试解析
									if (text.startsWith('{') && text.endsWith('}')) {
										try {
											const jsonData = JSON.parse(text);
											console.log('JSON数据:', jsonData);
										} catch (e) {
											console.error('JSON解析失败:', e);
										}
									}
								} catch (error) {
									console.error('字符串解码失败:', error);
								}
								break;
						}
					} catch (error) {
						console.error('数据解析失败:', error);
						
						// 尝试转换为字符串
						try {
							let text = '';
							if (typeof TextDecoder !== 'undefined') {
							const decoder = new TextDecoder('utf-8');
								text = decoder.decode(buffer);
							} else {
								// 小程序环境下的替代方案
								const uint8Array = new Uint8Array(buffer);
								for (let i = 0; i < uint8Array.length; i++) {
									text += String.fromCharCode(uint8Array[i]);
								}
							}
							console.log('收到数据(TEXT):', text);
						} catch (error) {
							console.error('数据解码失败:', error);
						}
					}
				}
			});
		},
		// 读取低功耗蓝牙设备的特征值
		readBLECharacteristicValue(deviceId, serviceId, characteristicId) {
			if (!deviceId || !serviceId || !characteristicId) {
				console.error('读取特征值失败：参数不完整');
				return;
			}
			
			uni.readBLECharacteristicValue({
				deviceId: deviceId,
				serviceId: serviceId,
				characteristicId: characteristicId,
				success: (res) => {
					console.log('读取特征值成功:', res);
					// 特征值数据将通过onBLECharacteristicValueChange事件返回
				},
				fail: (err) => {
					console.error('读取特征值失败:', err);
					this.showCustomToast({
						message: '读取设备数据失败',
						type: 'error'
					});
				}
			});
		},
		cleanupBluetooth() {
			// 停止扫描
			if (this.isScanning) {
				uni.stopBluetoothDevicesDiscovery({
					complete: () => {
						console.log('停止搜寻蓝牙设备');
					}
				});
			}
			
			// 关闭蓝牙模块
			if (this.isBluetoothInit) {
				uni.closeBluetoothAdapter({
					complete: () => {
						console.log('关闭蓝牙模块');
						this.isBluetoothInit = false;
						this.isBluetoothAvailable = false;
					}
				});
			}
			
			// 清除超时定时器
			if (this.discoveryTimeout) {
				clearTimeout(this.discoveryTimeout);
				this.discoveryTimeout = null;
			}
		},
		goToCommandPage() {
			// 确保当前设备信息包含MAC地址
			const deviceInfo = {
				...this.currentDevice,
				macAddress: this.currentDevice?.macAddress || this.currentDevice?.deviceId
			};
			
			// 将当前连接的设备信息传递给指令下发页面
			uni.navigateTo({
				url: './commandForm?device=' + encodeURIComponent(JSON.stringify(deviceInfo))
			});
		},
		// 将创建连接逻辑拆分到单独的方法中
		createBLEConnection(device) {
			uni.createBLEConnection({
				deviceId: device.deviceId,
				timeout: 20000, // 10秒超时
				success: (res) => {
					console.log('连接蓝牙设备成功:', res);
					
					// 保存设备信息
					this.currentDevice = {
						...device,
						// 将MAC地址存储在设备信息中
						macAddress: device.macAddress || device.deviceId
					};
					
					// 获取设备的服务
					this.getBLEDeviceServices(device.deviceId);
					
					// 监听蓝牙连接状态
					this.onBLEConnectionStateChange();
					
					// 更新UI状态
					this.isConnected = true;
					this.bluetoothStatus.text = `已连接到: ${device.name}（${device.macAddress}）`;
					this.bluetoothStatus.class = 'connected';
					this.showDeviceList = false;
					
					// 检查设备OTA升级历史
					this.checkDeviceOTAHistory();
					
					this.showCustomToast({
						message: `成功连接到 ${device.name}`,
						type: 'default'
					});
				},
				fail: (err) => {
					console.error('连接蓝牙设备失败:', err);
					
					this.isConnected = false;
					this.currentDevice = null;
					this.bluetoothStatus.text = `连接 ${device.name} 失败，请重试`;
					this.bluetoothStatus.class = 'error';
					
					// 错误信息处理
					let errMsg = '';
					switch (err.errCode) {
						case 10003:
							errMsg = '连接超时，请确保设备在附近且已开启';
							break;
						case 10012:
							errMsg = '连接已建立';
							break;
						case 10013:
							errMsg = '连接失败，设备可能已关闭或超出范围';
							break;
						default:
							errMsg = err.errMsg || '连接失败，请重试';
					}
					
					this.showCustomToast({
						message: errMsg,
						type: 'error'
					});
				},
				complete: () => {
					this.isConnecting = false;
				}
			});
		},
		testDevice() {
			// 设备监测按钮点击处理
			if (!this.isConnected || !this.currentDevice) {
				this.showCustomToast({
					message: '请先连接设备',
					type: 'warning'
				});
				return;
			}
			
			// 获取存储的蓝牙写入服务和特征值ID
			const deviceId = this.currentDevice?.deviceId;
			const serviceId = uni.getStorageSync('writeServiceId');
			const characteristicId = uni.getStorageSync('writeCharacteristicId');
			
			if (!deviceId || !serviceId || !characteristicId) {
				this.showCustomToast({
					message: '蓝牙特征值未找到，请重新连接设备',
					type: 'error'
				});
				return;
			}
			
			// 显示测试中提示
			this.showCustomToast({
				message: '正在测试设备...',
				type: 'default'
			});
			
			// 构建固定测试指令: a5 fe 1 0 0 1
			try {
				// 构建测试指令
				const buffer = new ArrayBuffer(6);
				const dataView = new DataView(buffer);
				
				// 填充命令数据
				dataView.setUint8(0, 0xA5); // 帧头1
				dataView.setUint8(1, 0xFE); // 帧头2
				dataView.setUint8(2, 0x01); // 命令
				dataView.setUint8(3, 0x00); // 数据长度高字节
				dataView.setUint8(4, 0x00); // 数据长度低字节
				dataView.setUint8(5, 0x01); // CRC校验
				
				// 记录测试指令
				const commandHex = 'A5 FE 01 00 00 01';
				console.log('发送测试指令:', commandHex);
				
				// 设置响应超时
				let responseTimeout;
				let testResult = false;
				let responseReceived = false;
				
				// 监听特征值变化，处理设备响应
				uni.onBLECharacteristicValueChange((res) => {
					if (responseReceived) return; // 已经收到响应，忽略后续数据
					
					if (res.value) {
						const buffer = res.value;
						const dataView = new DataView(buffer);
						const uint8Array = new Uint8Array(buffer);
						
						// 打印十六进制数据
						let hexData = '';
						for (let i = 0; i < uint8Array.length; i++) {
							hexData += uint8Array[i].toString(16).padStart(2, '0').toUpperCase() + ' ';
						}
						console.log('测试响应数据(HEX):', hexData);
						
						// 尝试解析为数字
						try {
							// 假设响应格式为4字节数据表示数字1234
							if (uint8Array.length >= 4) {
								// 检查是否为1234 (0x04D2)
								const responseValue = (uint8Array[0] << 24) | (uint8Array[1] << 16) | (uint8Array[2] << 8) | uint8Array[3];
								console.log('响应值:', responseValue);
								
								// 特定响应值1234表示设备正常（1234的ASCII码）825373492
								if (responseValue === 825373492) {
									testResult = true;
								}
							}
							
							responseReceived = true;
							clearTimeout(responseTimeout);
							this.processTestResult(testResult);
							
						} catch (error) {
							console.error('解析响应数据失败:', error);
						}
					}
				});
				
				// 写入测试指令到蓝牙设备
				uni.writeBLECharacteristicValue({
					deviceId: deviceId,
					serviceId: serviceId,
					characteristicId: characteristicId,
					value: buffer,
					success: (res) => {
						console.log('发送测试指令成功:', res);
						
						// 设置响应超时，5秒后如果没收到响应则判断为测试失败
						responseTimeout = setTimeout(() => {
							if (!responseReceived) {
								console.log('测试响应超时');
								this.processTestResult(false);
							}
						}, 5000);
					},
					fail: (err) => {
						console.error('发送测试指令失败:', err);
						
						this.showCustomToast({
							message: '发送测试指令失败',
							type: 'error'
						});
					}
				});
				
			} catch (error) {
				console.error('准备测试指令时出错:', error);
				
				this.showCustomToast({
					message: '测试指令准备失败',
					type: 'error'
				});
			}
		},
		
		processTestResult(isNormal) {
			// 处理测试结果，记录到本地存储
			console.log(`设备测试结果: ${isNormal ? '正常' : '异常'}`);
			
			// 显示测试结果提示
			this.showCustomToast({
				message: `设备测试${isNormal ? '正常' : '异常'}`,
				type: isNormal ? 'default' : 'error'
			});
			
			// 获取当前设备MAC地址
			const macAddress = this.currentDevice?.macAddress || this.currentDevice?.deviceId || '';
			
			// 获取MAC地址与设备编号的映射关系
			let macMapping = {};
			try {
				const storedMapping = uni.getStorageSync('deviceMacMapping');
				if (storedMapping) {
					macMapping = JSON.parse(storedMapping);
				}
			} catch (e) {
				console.error('读取MAC映射失败:', e);
			}
			
			// 检查当前MAC地址是否已有分配的编号
			let deviceNumber;
			let isExistingDevice = false;
			
			if (macMapping[macAddress]) {
				// 已存在的设备，使用之前分配的编号
				deviceNumber = macMapping[macAddress];
				isExistingDevice = true;
			} else {
				// 新设备，分配新编号
				let deviceCounter = uni.getStorageSync('deviceTestCounter') || 0;
				deviceCounter++;
				deviceNumber = deviceCounter;
				
				// 更新设备编号计数器
				uni.setStorageSync('deviceTestCounter', deviceCounter);
				
				// 记录MAC地址与编号的映射关系
				macMapping[macAddress] = deviceNumber;
				uni.setStorageSync('deviceMacMapping', JSON.stringify(macMapping));
			}
			
			// 创建测试记录
			const testRecord = {
				id: Date.now(),
				deviceNumber: deviceNumber,
				macAddress: macAddress,
				isNormal: isNormal,
				command: 'A5 FE 01 00 00 01',
				timestamp: this.formatDateTime(new Date())
			};
			
			// 获取已有的测试记录
			let testRecords = [];
			try {
				const existingRecords = uni.getStorageSync('deviceTestRecords');
				if (existingRecords) {
					testRecords = JSON.parse(existingRecords);
				}
			} catch (e) {
				console.error('读取测试记录失败:', e);
			}
			
			if (isExistingDevice) {
				// 如果是已存在的设备，查找并更新其测试记录
				const recordIndex = testRecords.findIndex(r => r.macAddress === macAddress);
				if (recordIndex !== -1) {
					// 更新记录，保留原来的deviceNumber
					testRecords[recordIndex] = {
						...testRecord,
						deviceNumber: testRecords[recordIndex].deviceNumber
					};
				} else {
					// 虽然存在MAC映射，但没找到对应记录（可能被手动删除），添加新记录
					testRecords.push(testRecord);
				}
			} else {
				// 新设备，直接添加记录
				testRecords.push(testRecord);
			}
			
			// 保存更新后的记录
			uni.setStorageSync('deviceTestRecords', JSON.stringify(testRecords));
			
			// 提示用户可以查看测试记录
			setTimeout(() => {
				uni.showModal({
					title: '测试完成',
					content: `设备测试${isNormal ? '正常' : '异常'}，是否查看测试记录？`,
					confirmText: '查看记录',
					cancelText: '继续测试',
					success: (res) => {
						if (res.confirm) {
							// 跳转到测试记录页面
							this.goToTestRecordPage();
						}
					}
				});
			}, 1000);
		},
		goToTestRecordPage() {
			// 跳转到测试记录页面
			uni.navigateTo({
				url: './deviceTest'
			});
		},
		goCommandHistory() {
				// 跳转指令下发历史页
				uni.navigateTo({
					url: '../bluetooth/commandHistory'
			})
		},
		formatDateTime(date) {
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			const seconds = String(date.getSeconds()).padStart(2, '0');
			return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
		},
		// OTA升级相关方法
		startOTAUpgrade() {
			if (!this.isConnected || !this.currentDevice) {
				this.showCustomToast({
					message: '请先连接设备',
					type: 'warning'
				});
				return;
			}
			
			// 开始升级流程
			this.otaUpgrading = true;
			this.otaProgress = 0;
			this.otaStatus = '准备中';
			this.otaStatusClass = 'scanning';
			this.otaStepText = '正在初始化升级流程...';
			this.otaCompleted = false;
			
			// 模拟升级过程
			this.simulateOTAProcess();
		},
		
		simulateOTAProcess() {
			// 开始真实的OTA升级流程，不再使用模拟
			console.log('=== 开始真实OTA升级流程 ===');
			this.getDeviceId();
		},
		
		getDeviceId() {
			this.otaStepText = '正在获取设备ID...';
			this.otaProgress = 5;
			
			console.log('=== OTA流程步骤2：获取设备ID ===');
			console.log('🔄 发送获取设备ID指令 (0x81)');
			
			// 获取蓝牙连接信息
			const deviceId = this.currentDevice?.deviceId;
			const serviceId = uni.getStorageSync('writeServiceId');
			const characteristicId = uni.getStorageSync('writeCharacteristicId');
			
			if (!deviceId || !serviceId || !characteristicId) {
				console.error('❌ 蓝牙连接信息不完整');
				this.otaStatus = '连接错误';
				this.otaStatusClass = 'error';
				this.otaStepText = '蓝牙连接信息不完整，请重新连接设备';
				this.otaCompleted = true;
				this.showCustomToast({
					message: '蓝牙连接信息不完整，请重新连接设备',
					type: 'error'
				});
				return;
			}
			
			try {
				// 构建获取设备ID指令: A5 FE 81 00 00 [CRC]
				const buffer = new ArrayBuffer(6);
				const dataView = new DataView(buffer);
				
				dataView.setUint8(0, 0xA5); // 帧头1
				dataView.setUint8(1, 0xFE); // 帧头2
				dataView.setUint8(2, 0x81); // 获取设备ID命令
				dataView.setUint8(3, 0x00); // 数据长度高字节
				dataView.setUint8(4, 0x00); // 数据长度低字节
				
				// 计算外层CRC校验：命令 + 数据长度 + 数据内容（没有数据）
				const crc = (0x81 + 0x00 + 0x00) & 0xFF;
				dataView.setUint8(5, crc);
				
				// 打印发送的指令
				let hexCmd = '';
				for (let i = 0; i < buffer.byteLength; i++) {
					hexCmd += dataView.getUint8(i).toString(16).padStart(2, '0').toUpperCase() + ' ';
				}
				console.log('📡 发送获取设备ID指令(HEX):', hexCmd);
				console.log('📋 指令解析: A5FE(帧头) 81(获取设备ID) 0000(数据长度) ' + crc.toString(16).toUpperCase() + '(CRC)');
				
				// 设置响应监听器
				this.setupOTAResponseListener((response) => {
					if (response.success) {
						console.log('=== OTA流程步骤3：设备返回设备ID ===');
						console.log('✅ 收到设备ID响应');
						
						// 解析设备ID - 对于0x81指令，响应不是OTA格式
						try {
							// 0x81指令的响应格式是：A5FE 81 [长度] [设备ID数据] [CRC]
							if (response.rawData && response.rawData.length >= 6) {
								const responseData = response.rawData;
								if (responseData[0] === 0xA5 && responseData[1] === 0xFE && responseData[2] === 0x81) {
									const dataLength = (responseData[3] << 8) | responseData[4];
									console.log('📋 设备ID数据长度:', dataLength);
									
									if (dataLength > 0 && responseData.length >= 6 + dataLength) {
										// 提取设备ID数据（跳过CRC）
										let deviceIdStr = '';
										for (let i = 5; i < 5 + dataLength - 1; i++) { // 减1是为了跳过末尾的CRC
											if (responseData[i] >= 0x30 && responseData[i] <= 0x39) { // 数字
												deviceIdStr += String.fromCharCode(responseData[i]);
											} else if (responseData[i] >= 0x41 && responseData[i] <= 0x5A) { // 大写字母
												deviceIdStr += String.fromCharCode(responseData[i]);
											} else if (responseData[i] >= 0x61 && responseData[i] <= 0x7A) { // 小写字母
												deviceIdStr += String.fromCharCode(responseData[i]);
											}
										}
										
										if (deviceIdStr.length > 0) {
											this.currentDeviceId = deviceIdStr;
											console.log(`✅ 解析设备ID: ${this.currentDeviceId}`);
											this.otaProgress = 15;
											this.otaStepText = '设备ID获取成功，正在请求服务端检查版本...';
											
											// 下一步：请求服务端检查版本
											this.checkServerVersion();
										} else {
											console.error('❌ 设备ID数据为空');
											this.handleOTAError('设备ID数据为空');
										}
									} else {
										console.error('❌ 设备ID响应数据长度不正确');
										this.handleOTAError('设备ID响应数据长度不正确');
									}
								} else {
									console.error('❌ 设备ID响应格式错误');
									this.handleOTAError('设备ID响应格式错误');
								}
							} else {
								console.error('❌ 设备ID响应数据太短');
								this.handleOTAError('设备ID响应数据太短');
							}
						} catch (error) {
							console.error('❌ 设备ID解析异常:', error);
							this.handleOTAError('设备ID解析异常: ' + error.message);
						}
					} else {
						console.error('❌ 获取设备ID失败:', response.message);
						this.handleOTAError('获取设备ID失败: ' + response.message);
					}
				}, 20000);
				
				// 发送指令
				uni.writeBLECharacteristicValue({
					deviceId: deviceId,
					serviceId: serviceId,
					characteristicId: characteristicId,
					value: buffer,
					success: (res) => {
						console.log('✅ 获取设备ID指令发送成功');
						console.log('⏳ 等待设备响应...');
					},
					fail: (err) => {
						console.error('❌ 获取设备ID指令发送失败:', err);
						this.clearOTAResponseListener();
						this.handleOTAError('指令发送失败: ' + err.errMsg);
					}
				});
				
			} catch (error) {
				console.error('❌ 构建获取设备ID指令失败:', error);
				this.handleOTAError('指令构建失败: ' + error.message);
			}
		},
		
		checkServerVersion() {
			this.otaStepText = '正在请求服务端检查固件版本...';
			this.otaProgress = 25;
			
			console.log('=== OTA流程步骤4：请求升级包信息 ===');
			console.log(`🌐 请求服务端API: GET /cst/firmware/enableVersion?deviceCode=${this.currentDeviceId}`);
			
			// 调用服务端API获取固件版本信息
			getFirmwareVersion(this.currentDeviceId).then(res => {
				console.log('=== OTA流程步骤5：服务端返回升级包信息 ===');
				console.log('📡 服务端响应:', res);
				
				if (res.code === 200 && res.data) {
					// 解析服务端返回的版本信息
					this.serverVersionInfo = res.data;
					this.targetVersion = res.data.version || 'V2.16';
					this.firmwareUrl = res.data.url;
					console.log('固件下载URL:', this.firmwareUrl);
					
					console.log(`📦 发现新版本: ${this.targetVersion}`);
					console.log(`📦 固件下载地址: ${this.firmwareUrl}`);
					
					this.otaProgress = 35;
					this.otaStepText = '服务端检查完成，发现新版本可用！';
					
					// 必须有固件下载URL才能继续
					if (this.firmwareUrl) {
						console.log('=== OTA流程步骤6：下载升级包（缓存本地） ===');
						setTimeout(() => {
							this.downloadFirmware(this.firmwareUrl);
						}, 1500);
					} else {
						// 没有下载URL，无法继续升级
						console.error('❌ 服务端未提供固件下载地址');
						this.handleOTAError('服务端未提供固件下载地址，无法继续升级');
					}
				} else {
					// 服务端没有找到新版本或出错
					console.log('❌ 服务端检查失败:', res.message || '未找到可用固件');
					this.otaProgress = 0;
					this.otaStatus = '检查失败';
					this.otaStatusClass = 'error';
					this.otaStepText = res.message || '服务端未找到可用的固件版本';
					this.otaCompleted = true;
					
					this.showCustomToast({
						message: res.message || '当前已是最新版本或服务端暂无可用固件',
						type: 'warning'
					});
				}
			}).catch(err => {
				console.error('❌ 请求服务端固件版本失败:', err);
				this.handleOTAError('无法连接服务端获取固件信息: ' + (err.message || err.errMsg || '网络错误'));
			});
		},
		
		downloadFirmware(firmwareUrl) {
			this.otaStepText = '正在下载固件包...';
			this.otaProgress = 40;
			this.otaStatus = '下载中';
			this.otaStatusClass = 'scanning';
			
			console.log('🌐 开始下载固件包:', firmwareUrl);
			
			if (!firmwareUrl) {
				console.error('❌ 固件下载地址为空');
				this.handleOTAError('固件下载地址为空');
				return;
			}
			
			// 实际下载固件文件
			const downloadTask = uni.downloadFile({
				url: firmwareUrl,
				success: (res) => {
					if (res.statusCode === 200) {
						console.log('✅ 固件包下载成功:', res.tempFilePath);
						console.log('📦 下载状态码:', res.statusCode);
						this.firmwarePath = res.tempFilePath;
						
						// 读取固件文件内容
						this.readFirmwareFile(res.tempFilePath);
					} else {
						console.error('❌ 固件包下载失败，状态码:', res.statusCode);
						this.handleOTAError('固件包下载失败，状态码: ' + res.statusCode);
					}
				},
				fail: (err) => {
					console.error('❌ 固件包下载失败:', err);
					this.handleOTAError('固件包下载失败: ' + (err.errMsg || '网络错误'));
				}
			});
			
			// 监听下载进度
			downloadTask.onProgressUpdate((res) => {
				const progress = Math.floor((res.progress / 100) * 10) + 40; // 40-50%
				this.otaProgress = Math.min(progress, 50);
				this.otaStepText = `正在下载固件包 ${res.progress}%...`;
				
				if (res.progress % 20 === 0) {
					console.log(`📊 下载进度: ${res.progress}% (${res.totalBytesWritten}/${res.totalBytesExpectedToWrite})`);
				}
			});
		},
		

		
		readFirmwareFile(filePath) {
			console.log('📖 开始读取固件文件:', filePath);
			
			// #ifdef APP-PLUS
			// APP环境下读取文件
			plus.io.resolveLocalFileSystemURL(filePath, (entry) => {
				entry.file((file) => {
					const reader = new plus.io.FileReader();
					reader.onloadend = (e) => {
						this.firmwareBuffer = e.target.result;
						this.firmwareSize = this.firmwareBuffer.byteLength;
						this.totalFragments = Math.ceil(this.firmwareSize / this.fragmentSize);
						
						console.log(`✅ 固件文件读取完成:`);
						console.log(`   文件路径: ${filePath}`);
						console.log(`   文件大小: ${this.firmwareSize} 字节 (${(this.firmwareSize/1024).toFixed(2)} KB)`);
						console.log(`   分片大小: ${this.fragmentSize} 字节`);
						console.log(`   分片总数: ${this.totalFragments} 个`);
						
						// 验证固件数据，只有验证通过才继续
						if (this.validateFirmwareData()) {
							this.otaProgress = 50;
							this.otaStepText = '固件包下载完成，正在查询设备当前版本...';
							
							// 继续下一步
							setTimeout(() => {
								this.queryVersion();
							}, 1500);
						}
						// 如果验证失败，validateFirmwareData中已经调用了handleOTAError
					};
					reader.onerror = (e) => {
						console.error('❌ 读取固件文件失败:', e);
						this.handleOTAError('读取固件文件失败');
					};
					reader.readAsArrayBuffer(file);
				}, (err) => {
					console.error('❌ 获取文件对象失败:', err);
					this.handleOTAError('获取文件对象失败');
				});
			}, (err) => {
				console.error('❌ 解析文件路径失败:', err);
				this.handleOTAError('解析文件路径失败');
			});
			// #endif
			
			// #ifdef MP-WEIXIN
			// 微信小程序环境下使用文件系统管理器
			const fs = wx.getFileSystemManager();
			try {
				fs.readFile({
					filePath: filePath,
					success: (res) => {
						this.firmwareBuffer = res.data;
						this.firmwareSize = this.firmwareBuffer.byteLength;
						this.totalFragments = Math.ceil(this.firmwareSize / this.fragmentSize);
						
						console.log(`✅ 固件文件读取完成:`);
						console.log(`   文件路径: ${filePath}`);
						console.log(`   文件大小: ${this.firmwareSize} 字节 (${(this.firmwareSize/1024).toFixed(2)} KB)`);
						console.log(`   分片大小: ${this.fragmentSize} 字节`);
						console.log(`   分片总数: ${this.totalFragments} 个`);
						
						// 验证固件数据，只有验证通过才继续
						if (this.validateFirmwareData()) {
							this.otaProgress = 50;
							this.otaStepText = '固件包下载完成，正在查询设备当前版本...';
							
							// 继续下一步
							setTimeout(() => {
								this.queryVersion();
							}, 1500);
						}
						// 如果验证失败，validateFirmwareData中已经调用了handleOTAError
					},
					fail: (err) => {
						console.error('❌ 读取固件文件失败:', err);
						this.handleOTAError('读取固件文件失败: ' + err.errMsg);
					}
				});
			} catch (err) {
				console.error('❌ 文件系统操作异常:', err);
				this.handleOTAError('文件系统操作异常: ' + err.message);
			}
			// #endif
			
			// #ifndef APP-PLUS && !MP-WEIXIN
			// 其他环境不支持文件读取
			// console.error('❌ 当前环境不支持文件读取');
			// this.handleOTAError('当前环境不支持文件读取');
			// #endif
		},
		
		// 验证固件数据
		validateFirmwareData() {
			if (!this.firmwareBuffer || this.firmwareSize === 0) {
				console.error('❌ 固件数据为空');
				this.handleOTAError('固件数据为空或未正确加载');
				return false;
			}
			
			// 检查文件大小是否合理 (至少1KB，最大10MB)
			if (this.firmwareSize < 1024) {
				console.error('❌ 固件文件过小，可能下载不完整');
				this.handleOTAError('固件文件过小，下载可能不完整');
				return false;
			}
			
			if (this.firmwareSize > 10 * 1024 * 1024) {
				console.error('❌ 固件文件过大，超出预期范围');
				this.handleOTAError('固件文件过大，超出预期范围');
				return false;
			}
			
			const firmwareView = new Uint8Array(this.firmwareBuffer);
			
			// 打印固件文件的前32字节用于验证
			const firstBytes = Array.from(firmwareView.slice(0, Math.min(32, this.firmwareSize)))
				.map(b => b.toString(16).padStart(2, '0').toUpperCase()).join(' ');
			console.log(`📦 固件文件前32字节: ${firstBytes}`);
			
			// 打印第0片数据的前16字节
			const fragment0Size = Math.min(this.fragmentSize, this.firmwareSize);
			const fragment0FirstBytes = Array.from(firmwareView.slice(0, Math.min(16, fragment0Size)))
				.map(b => b.toString(16).padStart(2, '0').toUpperCase()).join(' ');
			console.log(`📦 第0片前16字节: ${fragment0FirstBytes}`);
			
			// 检查固件数据是否为有效的二进制数据（不全为0）
			let nonZeroCount = 0;
			for (let i = 0; i < Math.min(1024, this.firmwareSize); i++) {
				if (firmwareView[i] !== 0) {
					nonZeroCount++;
				}
			}
			
			if (nonZeroCount < 100) {
				console.error('❌ 固件数据异常，大部分字节为0');
				this.handleOTAError('固件数据异常，文件可能损坏');
				return false;
			}
			
			// 打印文件信息摘要
			console.log('📋 固件文件信息摘要:');
			console.log(`   文件大小: ${this.firmwareSize} 字节 (${(this.firmwareSize/1024).toFixed(2)} KB)`);
			console.log(`   分片总数: ${this.totalFragments} 个`);
			console.log(`   最后一片大小: ${this.firmwareSize % this.fragmentSize || this.fragmentSize} 字节`);
			console.log(`   非零字节数(前1KB): ${nonZeroCount}/1024`);
			
			// 计算文件简单校验码
			let checksum = 0;
			for (let i = 0; i < Math.min(this.firmwareSize, 1024); i++) { // 只计算前1KB的校验码
				checksum += firmwareView[i];
			}
			checksum = checksum & 0xFFFF;
			console.log(`   文件校验码(前1KB): 0x${checksum.toString(16).padStart(4, '0').toUpperCase()}`);
			
			console.log('✅ 固件数据验证通过');
			return true;
		},
		
		queryVersion() {
			this.otaStepText = '正在查询设备当前版本...';
			this.otaProgress = 55;
			
			console.log('=== OTA流程步骤7：查询版本 ===');
			console.log('🔄 发送查询版本指令 (0x80+0x13)');
			
			const deviceId = this.currentDevice?.deviceId;
			const serviceId = uni.getStorageSync('writeServiceId');
			const characteristicId = uni.getStorageSync('writeCharacteristicId');
			
			if (!deviceId || !serviceId || !characteristicId) {
				console.error('❌ 蓝牙连接信息不完整');
				this.handleOTAError('蓝牙连接信息不完整');
				return;
			}
			
			try {
				// 构建OTA查询版本指令
				const otaDataContent = new Uint8Array(0); // 查询版本没有额外数据内容
				
				// 构建OTA数据区
				const otaData = new Uint8Array(8); // FFFE(2) + 版本(1) + 消息码(1) + CRC16(2) + 数据长度(2)
				
				otaData[0] = 0xFF; // OTA帧头1
				otaData[1] = 0xFE; // OTA帧头2
				otaData[2] = 0x01; // 版本号
				otaData[3] = 0x13; // 查询版本消息码
				
				// 数据区长度 (查询版本没有额外数据)
				otaData[6] = 0x00; // 数据长度高字节
				otaData[7] = 0x00; // 数据长度低字节
				
				// 计算内层CRC16：FFFE + 版本 + 消息码 + CRC16(先设为0x00 0x00) + 数据长度 + 数据内容
				// 先将CRC16位置设为0x00
				otaData[4] = 0x00; // CRC16高字节
				otaData[5] = 0x00; // CRC16低字节
				
				// 对包含CRC位置的完整内层数据计算CRC16
				const innerCRC = this.calculateCRC16(otaData);
				
						// 将计算结果填入CRC16位置
		otaData[4] = (innerCRC >> 8) & 0xFF; // CRC16高字节
		otaData[5] = innerCRC & 0xFF; // CRC16低字节
		
		console.log(`📋 内层CRC16计算数据长度: ${otaData.length} 字节`);
		
		// 构建完整指令
				const buffer = new ArrayBuffer(5 + otaData.length);
				const dataView = new DataView(buffer);
				
				// 外层指令
				dataView.setUint8(0, 0xA5); // 帧头1
				dataView.setUint8(1, 0xFE); // 帧头2
				dataView.setUint8(2, 0x80); // OTA指令
				dataView.setUint8(3, (otaData.length >> 8) & 0xFF); // 数据长度高字节
				dataView.setUint8(4, otaData.length & 0xFF); // 数据长度低字节
				
				// OTA数据区
				for (let i = 0; i < otaData.length; i++) {
					dataView.setUint8(5 + i, otaData[i]);
				}
				
				// 计算外层CRC校验：命令 + 数据长度 + OTA数据区
				let outerCRC = 0x80 + ((otaData.length >> 8) & 0xFF) + (otaData.length & 0xFF);
				for (let i = 0; i < otaData.length; i++) {
					outerCRC += otaData[i];
				}
				outerCRC = outerCRC & 0xFF;
				
				// 添加外层CRC到指令末尾
				const finalBuffer = new ArrayBuffer(buffer.byteLength + 1);
				const finalView = new DataView(finalBuffer);
				for (let i = 0; i < buffer.byteLength; i++) {
					finalView.setUint8(i, dataView.getUint8(i));
				}
				finalView.setUint8(buffer.byteLength, outerCRC);
				
				// 打印发送的指令
				let hexCmd = '';
				for (let i = 0; i < finalBuffer.byteLength; i++) {
					hexCmd += finalView.getUint8(i).toString(16).padStart(2, '0').toUpperCase() + ' ';
				}
				console.log('📡 发送查询版本指令(HEX):', hexCmd);
				console.log('📋 指令解析：A5FE(外层帧头) 80(OTA指令) ' + 
					otaData.length.toString(16).padStart(4, '0').toUpperCase() + '(数据长度) FFFE(OTA帧头) 01(版本) 13(查询版本) ' +
					innerCRC.toString(16).padStart(4, '0').toUpperCase() + '(内层CRC16) 0000(数据长度) ' + 
					outerCRC.toString(16).padStart(2, '0').toUpperCase() + '(外层CRC)');
				
				// 设置响应监听器
				this.setupOTAResponseListener((response) => {
					if (response.success) {
						console.log('=== OTA流程步骤8：设备返回当前版本号 ===');
						console.log('✅ 收到版本查询响应');
						
						try {
							if (response.messageCode === 0x13 && response.data && response.data.length > 0) {
								// 解析版本信息
								// 数据格式：CRC16(2字节) + 数据长度(2字节) + 结果码(1字节) + 版本号(16字节固定长度)
								console.log('📋 版本查询响应数据长度:', response.data.length);
								console.log('📋 版本查询响应原始数据:', Array.from(response.data).map(b => b.toString(16).padStart(2, '0').toUpperCase()).join(' '));
								
								if (response.data.length >= 5) { // 至少需要CRC16(2) + 数据长度(2) + 结果码(1)
									// 跳过CRC16(2字节) + 数据长度(2字节)，获取结果码和版本数据
									const resultCode = response.data[4]; // 第5个字节是结果码
									console.log('📋 设备响应结果码:', resultCode.toString(16).toUpperCase());
									
									if (resultCode === 0x00) { // 0x00表示成功
										// 版本号数据从第6个字节开始，固定16字节长度
										if (response.data.length >= 21) { // 4(CRC+长度) + 1(结果码) + 16(版本号) = 21
											const versionBytes = response.data.slice(5, 21); // 提取16字节版本号数据
											console.log('📋 版本号原始字节:', Array.from(versionBytes).map(b => b.toString(16).padStart(2, '0').toUpperCase()).join(' '));
											
											// 将字节转换为ASCII字符串，忽略0字节
											let versionStr = '';
											for (let i = 0; i < versionBytes.length; i++) {
												if (versionBytes[i] !== 0x00) { // 忽略补0的字节
													versionStr += String.fromCharCode(versionBytes[i]);
												}
											}
											
											console.log('📋 解析出的版本字符串:', versionStr);
											
											if (versionStr.length > 0) {
												this.currentVersion = versionStr;
												console.log(`✅ 解析设备版本: ${this.currentVersion}`);
												this.otaProgress = 65;
												this.otaStepText = '设备版本查询完成，准备通知设备新版本...';
												
												console.log(`📋 当前版本: ${this.currentVersion}`);
												console.log(`📋 目标版本: ${this.targetVersion}`);
												
												// 比较版本
												if (this.currentVersion !== this.targetVersion) {
													console.log('✅ 版本对比完成，需要升级！');
													// 下一步：新版本通知
													this.notifyNewVersion();
												} else {
													console.log('ℹ️ 设备已是最新版本');
													this.handleOTAError('设备已是最新版本，无需升级');
												}
											} else {
												console.error('❌ 版本字符串为空', versionStr);
												this.handleOTAError('版本信息解析失败：版本字符串为空');
											}
										} else {
											console.error('❌ 版本响应数据长度不足，期望至少21字节，实际:', response.data.length);
											this.handleOTAError('版本响应数据长度不足');
										}
									} else {
										console.error('❌ 设备返回版本查询失败，结果码:', resultCode.toString(16).toUpperCase());
										this.handleOTAError('设备版本查询失败，结果码: 0x' + resultCode.toString(16).toUpperCase());
									}
								} else {
									console.error('❌ 版本数据格式错误，数据长度不足:', response.data.length);
									this.handleOTAError('版本数据格式错误');
								}
							} else {
								console.error('❌ 版本查询响应格式错误');
								this.handleOTAError('版本查询响应格式错误');
							}
						} catch (error) {
							console.error('❌ 版本信息解析异常:', error);
							this.handleOTAError('版本信息解析异常: ' + error.message);
						}
					} else {
						console.error('❌ 查询版本失败:', response.message);
						this.handleOTAError('查询版本失败: ' + response.message);
					}
				}, 20000);
				
				// 发送指令
				uni.writeBLECharacteristicValue({
					deviceId: deviceId,
					serviceId: serviceId,
					characteristicId: characteristicId,
					value: finalBuffer,
					success: (res) => {
						console.log('✅ 查询版本指令发送成功');
						console.log('⏳ 等待设备响应版本信息...');
					},
					fail: (err) => {
						console.error('❌ 查询版本指令发送失败:', err);
						this.clearOTAResponseListener();
						this.handleOTAError('查询版本指令发送失败: ' + err.errMsg);
					}
				});
				
			} catch (error) {
				console.error('❌ 构建查询版本指令失败:', error);
				this.handleOTAError('构建查询版本指令失败: ' + error.message);
			}
		},
		
		// OTA错误处理
		handleOTAError(errorMessage) {
			console.error('❌ OTA升级错误:', errorMessage);
			this.otaStatus = '升级失败';
			this.otaStatusClass = 'error';
			this.otaStepText = errorMessage;
			this.otaCompleted = true;
			this.clearOTAResponseListener();
			
			// 停止分片传输
			this.fragmentTransferActive = false;
			
			// 清理固件数据
			this.firmwareBuffer = null;
			this.firmwarePath = '';
			this.firmwareSize = 0;
			console.log('🧹 升级失败，固件数据已清理');
			
			this.showCustomToast({
				message: 'OTA升级失败: ' + errorMessage,
				type: 'error'
			});
			
			// 记录失败的升级历史
			this.recordOTAHistory(false);
		},
		
		simulateVersionResponse() {
			// 此方法已废弃，不再使用模拟响应
			console.warn('⚠️ simulateVersionResponse 方法已废弃');
		},
		
		notifyNewVersion() {
			this.otaStepText = '正在通知设备新版本...';
			this.otaProgress = 70;
			this.otaStatus = '升级中';
			this.otaStatusClass = 'scanning';
			
			console.log('=== OTA流程步骤9：新版本通知 ===');
			console.log('🔄 发送新版本通知指令 (0x80+0x14)');
			
			const deviceId = this.currentDevice?.deviceId;
			const serviceId = uni.getStorageSync('writeServiceId');
			const characteristicId = uni.getStorageSync('writeCharacteristicId');
			
			if (!deviceId || !serviceId || !characteristicId) {
				console.error('❌ 蓝牙连接信息不完整');
				this.handleOTAError('蓝牙连接信息不完整');
				return;
			}
			
			try {
				// 版本号格式转换："SF02-103A SV1.0 250125" -> "SV1.0_250125"
				const originalVersion = this.targetVersion || 'V2.16';
				let convertedVersion = originalVersion;
				
				// 提取SV版本号和日期
				const svMatch = originalVersion.match(/SV(\d+\.\d+)\s+(\d{6})/);
				if (svMatch) {
					convertedVersion = `SV${svMatch[1]}_${svMatch[2]}`;
				} else {
					// 如果不匹配预期格式，尝试简化版本号
					convertedVersion = originalVersion.replace(/\s+/g, '_').substring(0, 15);
				}
				
				console.log(`📦 原始版本号: ${originalVersion}`);
				console.log(`📦 转换后版本号: ${convertedVersion}`);
				
				// 版本号转换为16字节数组，不足补0
				const versionBytes = new Uint8Array(16);
				for (let i = 0; i < Math.min(convertedVersion.length, 16); i++) {
					versionBytes[i] = convertedVersion.charCodeAt(i);
				}
				// 剩余位置自动为0
				
				// 固件信息计算
				this.fragmentSize = 226; // 修改为226字节每片
				
				// 使用实际固件大小，优先从已加载的固件获取
				let actualFirmwareSize = this.firmwareSize || 64500; // 优先使用已加载的固件大小
				if (!this.firmwareSize && this.serverVersionInfo && this.serverVersionInfo.size) {
					actualFirmwareSize = this.serverVersionInfo.size;
				} else if (!this.firmwareSize && this.serverVersionInfo && this.serverVersionInfo.fileSize) {
					actualFirmwareSize = this.serverVersionInfo.fileSize;
				}
				
				// 更新固件大小和分片总数
				this.firmwareSize = actualFirmwareSize;
				this.totalFragments = Math.ceil(actualFirmwareSize / this.fragmentSize);
				
				// 计算升级包校验码（简化为固件大小的校验）
				const firmwareChecksum = actualFirmwareSize & 0xFFFF;
				
				console.log(`📦 分片大小: ${this.fragmentSize} 字节`);
				console.log(`📦 实际固件大小: ${actualFirmwareSize} 字节`);
				console.log(`📦 计算分片总数: ${this.totalFragments} 个`);
				console.log(`📦 固件校验码: 0x${firmwareChecksum.toString(16).toUpperCase()}`);
				console.log('📦 版本号字节:', Array.from(versionBytes).map(b => b.toString(16).padStart(2, '0').toUpperCase()).join(' '));
				
				// 构建数据内容：目的版本号(16字节) + 升级包分片大小(2字节) + 升级包分片总数(2字节) + 升级包校验码(2字节)
				const dataContent = new Uint8Array(16 + 2 + 2 + 2); // 总共22字节
				let dataIndex = 0;
				
				// 目的版本号 (16字节)
				for (let i = 0; i < 16; i++) {
					dataContent[dataIndex++] = versionBytes[i];
				}
				
				// 升级包分片大小 (WORD - 2字节, 小端序)
				dataContent[dataIndex++] = this.fragmentSize & 0xFF; // 低字节
				dataContent[dataIndex++] = (this.fragmentSize >> 8) & 0xFF; // 高字节
				
				// 升级包分片总数 (WORD - 2字节, 小端序) 
				dataContent[dataIndex++] = this.totalFragments & 0xFF; // 低字节
				dataContent[dataIndex++] = (this.totalFragments >> 8) & 0xFF; // 高字节
				
				// 升级包校验码 (WORD - 2字节, 小端序)
				dataContent[dataIndex++] = firmwareChecksum & 0xFF; // 低字节
				dataContent[dataIndex++] = (firmwareChecksum >> 8) & 0xFF; // 高字节
				
				console.log('📦 数据内容字节:', Array.from(dataContent).map(b => b.toString(16).padStart(2, '0').toUpperCase()).join(' '));
				
				// 构建OTA数据区：FFFE + 版本 + 消息码 + CRC16 + 数据长度 + 数据内容
				const otaData = new Uint8Array(6 + 2 + dataContent.length);
				otaData[0] = 0xFF; // OTA帧头1
				otaData[1] = 0xFE; // OTA帧头2
				otaData[2] = 0x01; // 版本号
				otaData[3] = 0x14; // 新版本通知消息码
				
				// 数据长度 (小端序)
				otaData[6] = dataContent.length & 0xFF; // 数据长度低字节
				otaData[7] = (dataContent.length >> 8) & 0xFF; // 数据长度高字节
				
				// 数据内容
				for (let i = 0; i < dataContent.length; i++) {
					otaData[8 + i] = dataContent[i];
				}
				
				// 计算内层CRC16：FFFE + 版本 + 消息码 + CRC16(先设为0x00 0x00) + 数据长度 + 数据内容
				// 先将CRC16位置设为0x00
				otaData[4] = 0x00; // CRC16高字节
				otaData[5] = 0x00; // CRC16低字节
				
				// 对包含CRC位置的完整内层数据计算CRC16
				const innerCRC = this.calculateCRC16(otaData);
				
				// 将计算结果填入CRC16位置
				otaData[4] = (innerCRC >> 8) & 0xFF; // CRC16高字节
				otaData[5] = innerCRC & 0xFF; // CRC16低字节
				
				console.log(`📋 内层CRC16计算数据长度: ${otaData.length} 字节`);
				console.log(`📋 内层CRC16: 0x${innerCRC.toString(16).padStart(4, '0').toUpperCase()} (大端序显示: ${((innerCRC >> 8) & 0xFF).toString(16).padStart(2, '0').toUpperCase()} ${(innerCRC & 0xFF).toString(16).padStart(2, '0').toUpperCase()})`);
				
				// 构建完整指令
				const buffer = new ArrayBuffer(5 + otaData.length);
				const dataView = new DataView(buffer);
				
				// 外层指令
				dataView.setUint8(0, 0xA5); // 帧头1
				dataView.setUint8(1, 0xFE); // 帧头2
				dataView.setUint8(2, 0x80); // OTA指令
				dataView.setUint8(3, (otaData.length >> 8) & 0xFF); // 数据长度高字节
				dataView.setUint8(4, otaData.length & 0xFF); // 数据长度低字节
				
				// OTA数据区
				for (let i = 0; i < otaData.length; i++) {
					dataView.setUint8(5 + i, otaData[i]);
				}
				
				// 计算外层CRC校验：命令 + 数据长度 + OTA数据区
				let outerCRC = 0x80 + ((otaData.length >> 8) & 0xFF) + (otaData.length & 0xFF);
				for (let i = 0; i < otaData.length; i++) {
					outerCRC += otaData[i];
				}
				outerCRC = outerCRC & 0xFF;
				
				// 添加外层CRC到指令末尾
				const finalBuffer = new ArrayBuffer(buffer.byteLength + 1);
				const finalView = new DataView(finalBuffer);
				for (let i = 0; i < buffer.byteLength; i++) {
					finalView.setUint8(i, dataView.getUint8(i));
				}
				finalView.setUint8(buffer.byteLength, outerCRC);
				
				// 打印发送的指令
				let hexCmd = '';
				for (let i = 0; i < finalBuffer.byteLength; i++) {
					hexCmd += finalView.getUint8(i).toString(16).padStart(2, '0').toUpperCase() + ' ';
				}
				console.log('📡 发送新版本通知指令(HEX):', hexCmd);
				console.log('📋 指令解析: A5FE(外层帧头) 80(OTA指令) ' + 
					otaData.length.toString(16).padStart(4, '0').toUpperCase() + '(数据长度) FFFE(OTA帧头) 01(版本) 14(新版本通知) ' +
					innerCRC.toString(16).padStart(4, '0').toUpperCase() + '(内层CRC16) ' +
					dataContent.length.toString(16).padStart(4, '0').toUpperCase() + '(数据长度) [版本号+分片信息] ' +
					outerCRC.toString(16).padStart(2, '0').toUpperCase() + '(外层CRC)');
				
				// 验证与案例的对比
				console.log('📋 协议案例验证:');
				console.log('   目标版本号:', convertedVersion, '(16字节)');
				console.log('   分片大小:', this.fragmentSize, '字节 (0x' + this.fragmentSize.toString(16).toUpperCase() + ')');
				console.log('   分片总数:', this.totalFragments, '个 (0x' + this.totalFragments.toString(16).toUpperCase() + ')');
				console.log('   校验码: 0x' + firmwareChecksum.toString(16).toUpperCase());
				
				// 设置响应监听器
				this.setupOTAResponseListener((response) => {
					if (response.success) {
						console.log('=== OTA流程步骤10：设备返回结果码 ===');
						console.log('✅ 收到新版本通知响应');
						
						try {
							if (response.messageCode === 0x14 && response.data && response.data.length > 2) {
								// 跳过CRC16，获取结果码
								const resultData = response.data.slice(2); // 跳过CRC16
								if (resultData.length > 0) {
									const resultCode = resultData[0]; // 第一个字节是结果码
									
									console.log('📋 设备响应结果码:', resultCode.toString(16).toUpperCase());
									
									if (resultCode === 0x00) {
										console.log('✅ 设备确认：允许执行OTA升级');
										this.otaStepText = '设备已确认升级，开始分片传输...';
										this.otaProgress = 75;
										
										// 开始分片传输
										this.startFragmentTransfer();
									} else {
										console.error('❌ 设备拒绝升级，结果码:', resultCode);
										this.handleOTAError('设备拒绝升级，错误码: 0x' + resultCode.toString(16).toUpperCase());
									}
								} else {
									console.error('❌ 新版本通知响应数据为空');
									this.handleOTAError('新版本通知响应数据为空');
								}
							} else {
								console.error('❌ 新版本通知响应格式错误');
								this.handleOTAError('新版本通知响应格式错误');
							}
						} catch (error) {
							console.error('❌ 新版本通知响应解析异常:', error);
							this.handleOTAError('新版本通知响应解析异常: ' + error.message);
						}
					} else {
						console.error('❌ 新版本通知失败:', response.message);
						this.handleOTAError('新版本通知失败: ' + response.message);
					}
				}, 15000);
				
				// 发送指令
				uni.writeBLECharacteristicValue({
					deviceId: deviceId,
					serviceId: serviceId,
					characteristicId: characteristicId,
					value: finalBuffer,
					
					success: (res) => {
						console.log('✅ 新版本通知指令发送成功');
						console.log('⏳ 等待设备确认是否允许升级...');
					},
					fail: (err) => {
						console.error('❌ 新版本通知指令发送失败:', err);
						this.clearOTAResponseListener();
						this.handleOTAError('新版本通知指令发送失败: ' + err.errMsg);
					}
				});
				
			} catch (error) {
				console.error('❌ 构建新版本通知指令失败:', error);
				this.handleOTAError('构建新版本通知指令失败: ' + error.message);
			}
		},
		simulateUpgradeConfirmation() {
			// 此方法已废弃，不再使用模拟
			console.warn('⚠️ simulateUpgradeConfirmation 方法已废弃');
		},
		
		startFragmentTransfer() {
			console.log('=== OTA流程步骤11-12：分片传输循环（断点续传） ===');
			
			// 初始化分片传输状态
			this.currentFragment = 0;
			// this.totalFragments 已在notifyNewVersion中根据实际固件大小计算
			// this.fragmentSize 已在notifyNewVersion中设置为200字节
			this.fragmentTransferActive = true;
			
			console.log(`🚀 开始分片传输，总计 ${this.totalFragments} 个分片`);
			console.log(`📦 每个分片大小: ${this.fragmentSize} 字节`);
			console.log('📡 传输协议: 设备请求分片 -> 小程序返回分片数据');
			
			// 设置持续的响应监听器，用于处理设备的分片请求
			this.setupFragmentResponseListener();
			
			this.otaStepText = `等待设备请求分片 1/${this.totalFragments}...`;
			this.otaProgress = 75;
		},
		
		setupFragmentResponseListener() {
			console.log('🔍 设置分片请求监听器');
			
			// 设置一个长时间的监听器用于分片传输
			this.setupOTAResponseListener((response) => {
				if (response.success && this.fragmentTransferActive) {
					try {
						if (response.messageCode === 0x15) {
							// 这是设备请求分片的指令
							console.log('📥 收到设备分片请求');
							
							// 解析请求的分片序号
							if (response.data && response.data.length > 4) {
								// response.data 包含：CRC16(2字节) + 数据长度(2字节) + 数据内容
								const crc16 = response.data.slice(0, 2);
								const dataLengthBytes = response.data.slice(2, 4);
								const dataLength = (dataLengthBytes[0] << 8) | dataLengthBytes[1]; // 大端序解析
								
								console.log('📋 内层CRC16:', Array.from(crc16).map(b => b.toString(16).padStart(2, '0').toUpperCase()).join(' '));
								console.log('📋 内层数据长度字节:', Array.from(dataLengthBytes).map(b => b.toString(16).padStart(2, '0').toUpperCase()).join(' '));
								console.log('📋 内层数据长度:', dataLength, `(0x${dataLength.toString(16).padStart(4, '0').toUpperCase()})`);
								
								if (response.data.length >= 4 + dataLength) {
									// 提取数据内容
									const dataContent = response.data.slice(4, 4 + dataLength);
									console.log('📋 数据内容:', Array.from(dataContent).map(b => b.toString(16).padStart(2, '0').toUpperCase()).join(' '));
									console.log('📋 数据内容长度:', dataContent.length, '字节');
									
									// 数据内容结构：前16字节设备ID + 后2字节分片序号
									if (dataContent.length >= 18) {
										// 提取设备ID（前16字节）
										const deviceIdBytes = dataContent.slice(0, 16);
										let deviceId = '';
										for (let i = 0; i < deviceIdBytes.length; i++) {
											if (deviceIdBytes[i] >= 0x20 && deviceIdBytes[i] <= 0x7E) {
												deviceId += String.fromCharCode(deviceIdBytes[i]);
											} else if (deviceIdBytes[i] === 0x00) {
												break; // 遇到结束符停止
											}
										}
										console.log('📋 设备ID字节:', Array.from(deviceIdBytes).map(b => b.toString(16).padStart(2, '0').toUpperCase()).join(' '));
										console.log('📋 设备ID(ASCII):', deviceId);
										
										// 提取分片序号（后2字节，大端序）
										const fragmentBytes = dataContent.slice(16, 18);
										const requestedFragment = (fragmentBytes[0] << 8) | fragmentBytes[1]; // 大端序解析
										console.log('📋 分片序号字节:', Array.from(fragmentBytes).map(b => b.toString(16).padStart(2, '0').toUpperCase()).join(' '));
										console.log(`📋 设备请求分片: ${requestedFragment} (大端序: ${fragmentBytes[0].toString(16).padStart(2, '0').toUpperCase()} ${fragmentBytes[1].toString(16).padStart(2, '0').toUpperCase()})`);
										
										if (requestedFragment >= 0 && requestedFragment < this.totalFragments) {
											// 发送请求的分片数据（分片序号从0开始，与设备端保持一致）
											console.log(`📤 准备发送分片序号: ${requestedFragment} (第${requestedFragment}片数据，从0开始)`);
											this.sendFragmentData(requestedFragment);
										} else {
											console.error('❌ 请求的分片序号超出范围:', requestedFragment, '总分片数:', this.totalFragments);
										}
									} else {
										console.error('❌ 数据内容长度不足18字节，无法解析设备ID和分片序号，实际长度:', dataContent.length);
									}
								} else {
									console.error('❌ 请求数据长度不足，无法获取完整数据内容');
								}
							} else {
								console.error('❌ 分片请求数据格式错误');
							}
						} else if (response.messageCode === 0x84) {
							// 设备上报下载完成状态
							console.log('=== OTA流程步骤13：设备上报下载成功 ===');
							console.log('✅ 设备上报：下载完成');
							
							this.fragmentTransferActive = false;
							this.clearOTAResponseListener();
							
							this.otaStepText = '分片传输完成，正在执行升级...';
							this.otaProgress = 85;
							
							// 开始执行升级
							this.executeUpgrade();
						}
					} catch (error) {
						console.error('❌ 分片响应处理异常:', error);
					}
				} else if (!response.success && this.fragmentTransferActive) {
					console.error('❌ 分片传输过程中出错:', response.message);
					this.fragmentTransferActive = false;
					this.handleOTAError('分片传输失败: ' + response.message);
				}
			}, 300000); // 5分钟超时，分片传输可能需要较长时间
		},
		
		sendFragmentData(fragmentIndex) {
			console.log(`📤 发送分片 ${fragmentIndex}/${this.totalFragments}`);
			
			const deviceId = this.currentDevice?.deviceId;
			const serviceId = uni.getStorageSync('writeServiceId');
			const characteristicId = uni.getStorageSync('writeCharacteristicId');
			
			if (!deviceId || !serviceId || !characteristicId) {
				console.error('❌ 蓝牙连接信息不完整');
				this.handleOTAError('蓝牙连接信息不完整');
				return;
			}
			
			try {
				// 检查固件数据是否已正确加载
				if (!this.firmwareBuffer) {
					console.error('❌ 固件数据未加载 - firmwareBuffer为空');
					this.handleOTAError('固件数据未加载，请确保固件下载完成');
					return;
				}
				
				if (!this.firmwareSize || this.firmwareSize <= 0) {
					console.error('❌ 固件数据未加载 - firmwareSize无效:', this.firmwareSize);
					this.handleOTAError('固件大小无效，请重新下载固件');
					return;
				}
				
				if (!this.totalFragments || this.totalFragments <= 0) {
					console.error('❌ 分片计算错误 - totalFragments无效:', this.totalFragments);
					this.handleOTAError('分片计算错误，请重新下载固件');
					return;
				}
				
				const startOffset = fragmentIndex * this.fragmentSize;
				const endOffset = Math.min(startOffset + this.fragmentSize, this.firmwareSize);
				const actualFragmentSize = endOffset - startOffset;
				
				// 从固件缓存中提取对应分片的数据
				const firmwareView = new Uint8Array(this.firmwareBuffer);
				const fragmentData = new Uint8Array(actualFragmentSize);
				
				for (let i = 0; i < actualFragmentSize; i++) {
					fragmentData[i] = firmwareView[startOffset + i];
				}
				
				console.log(`📤 准备发送分片 ${fragmentIndex}/${this.totalFragments}`);
				console.log(`📦 分片数据大小: ${fragmentData.length} 字节`);
				console.log(`📦 分片起始偏移: ${startOffset} (0x${startOffset.toString(16).padStart(8, '0').toUpperCase()})`);
				console.log(`📦 实际分片大小: ${actualFragmentSize} 字节 (${actualFragmentSize === this.fragmentSize ? '完整分片' : '最后分片'})`);
				
				// 打印分片数据的前16字节和后16字节用于验证
				if (fragmentData.length >= 16) {
					const firstBytes = Array.from(fragmentData.slice(0, 16)).map(b => b.toString(16).padStart(2, '0').toUpperCase()).join(' ');
					console.log(`📦 分片数据前16字节: ${firstBytes}`);
					
					if (fragmentData.length >= 32) {
						const lastBytes = Array.from(fragmentData.slice(-16)).map(b => b.toString(16).padStart(2, '0').toUpperCase()).join(' ');
						console.log(`📦 分片数据后16字节: ${lastBytes}`);
					}
				}
				
				// 如果是第0片，验证数据是否正确解析
				if (fragmentIndex === 0) {
					const expectedFirstBytes = 'D8 97 00 20 E1 61 00 08 85 A2 00 08 01 98 00 08';
					const actualFirstBytes = Array.from(fragmentData.slice(0, 16)).map(b => b.toString(16).padStart(2, '0').toUpperCase()).join(' ');
					
					console.log('🔍 第0片数据发送验证:');
					console.log(`   真实固件期望: ${expectedFirstBytes}`);
					console.log(`   当前分片数据: ${actualFirstBytes}`);
					console.log(`   验证结果: ${expectedFirstBytes === actualFirstBytes ? '✅ 完全一致！' : '❌ 不一致'}`);
					
					if (expectedFirstBytes !== actualFirstBytes) {
						console.warn('⚠️ 第0片数据与期望值不匹配，可能是不同版本的固件文件');
					}
				}
				
				// 构建数据内容：结果码(1字节) + 分片序号(2字节) + 分片数据(200字节)
				const dataContent = new Uint8Array(1 + 2 + fragmentData.length);
				let dataIndex = 0;
				
				// 结果码 (0x00表示处理成功)
				dataContent[dataIndex++] = 0x00;
				
				// 分片序号 (WORD - 2字节, 大端序，与设备请求保持一致)
				dataContent[dataIndex++] = (fragmentIndex >> 8) & 0xFF; // 高字节
				dataContent[dataIndex++] = fragmentIndex & 0xFF; // 低字节
				
				// 分片数据
				for (let i = 0; i < fragmentData.length; i++) {
					dataContent[dataIndex++] = fragmentData[i];
				}
				
				console.log(`📋 数据内容构成: 结果码(1字节) + 分片序号(2字节) + 分片数据(${fragmentData.length}字节) = ${dataContent.length}字节`);
				console.log(`📋 分片序号: ${fragmentIndex} (0x${fragmentIndex.toString(16).padStart(4, '0').toUpperCase()}, 大端序存储: ${((fragmentIndex >> 8) & 0xFF).toString(16).padStart(2, '0').toUpperCase()} ${(fragmentIndex & 0xFF).toString(16).padStart(2, '0').toUpperCase()})`);
		
		// 构建OTA数据区：FFFE + 版本 + 消息码 + CRC16 + 数据长度 + 数据内容
		const otaData = new Uint8Array(6 + 2 + dataContent.length);
		
		otaData[0] = 0xFF; // OTA帧头1
		otaData[1] = 0xFE; // OTA帧头2
		otaData[2] = 0x01; // 版本号
		otaData[3] = 0x15; // 分片数据响应消息码
		
		// 数据长度 (小端序) - 修正字节序
		otaData[6] = dataContent.length & 0xFF; // 数据长度低字节
		otaData[7] = (dataContent.length >> 8) & 0xFF; // 数据长度高字节
		
		console.log(`📋 内层数据长度: ${dataContent.length} (0x${dataContent.length.toString(16).padStart(4, '0').toUpperCase()}, 小端序存储: ${(dataContent.length & 0xFF).toString(16).padStart(2, '0').toUpperCase()} ${((dataContent.length >> 8) & 0xFF).toString(16).padStart(2, '0').toUpperCase()}, 大端序显示: ${((dataContent.length >> 8) & 0xFF).toString(16).padStart(2, '0').toUpperCase()} ${(dataContent.length & 0xFF).toString(16).padStart(2, '0').toUpperCase()})`);
		
		// 数据内容
		for (let i = 0; i < dataContent.length; i++) {
			otaData[8 + i] = dataContent[i];
		}
		
		// 计算内层CRC16：FFFE + 版本 + 消息码 + CRC16(先设为0x00 0x00) + 数据长度 + 数据内容
		// 先将CRC16位置设为0x00
		otaData[4] = 0x00; // CRC16高字节
		otaData[5] = 0x00; // CRC16低字节
		
		// 对包含CRC位置的完整内层数据计算CRC16
		const innerCRC = this.calculateCRC16(otaData);
		
		// 将计算结果填入CRC16位置
		otaData[4] = (innerCRC >> 8) & 0xFF; // CRC16高字节
		otaData[5] = innerCRC & 0xFF; // CRC16低字节
				
				console.log(`📋 内层CRC16计算数据长度: ${otaData.length} 字节`);
				console.log(`📋 内层CRC16: 0x${innerCRC.toString(16).padStart(4, '0').toUpperCase()} (大端序显示: ${((innerCRC >> 8) & 0xFF).toString(16).padStart(2, '0').toUpperCase()} ${(innerCRC & 0xFF).toString(16).padStart(2, '0').toUpperCase()})`);
				
				// 构建完整指令
				const buffer = new ArrayBuffer(5 + otaData.length);
				const dataView = new DataView(buffer);
				
				// 外层指令
				dataView.setUint8(0, 0xA5); // 帧头1
				dataView.setUint8(1, 0xFE); // 帧头2
				dataView.setUint8(2, 0x80); // OTA指令
				dataView.setUint8(3, (otaData.length >> 8) & 0xFF); // 数据长度高字节
				dataView.setUint8(4, otaData.length & 0xFF); // 数据长度低字节
				
				// OTA数据区
				for (let i = 0; i < otaData.length; i++) {
					dataView.setUint8(5 + i, otaData[i]);
				}
				
				// 计算外层CRC校验：命令 + 数据长度 + OTA数据区
				let outerCRC = 0x80 + ((otaData.length >> 8) & 0xFF) + (otaData.length & 0xFF);
				for (let i = 0; i < otaData.length; i++) {
					outerCRC += otaData[i];
				}
				outerCRC = outerCRC & 0xFF;
				
				// 添加外层CRC到指令末尾
				const finalBuffer = new ArrayBuffer(buffer.byteLength + 1);
				const finalView = new DataView(finalBuffer);
				for (let i = 0; i < buffer.byteLength; i++) {
					finalView.setUint8(i, dataView.getUint8(i));
				}
				finalView.setUint8(buffer.byteLength, outerCRC);
				
				console.log(`📋 外层数据长度: ${otaData.length} (0x${otaData.length.toString(16).padStart(4, '0').toUpperCase()}, 大端序显示: ${((otaData.length >> 8) & 0xFF).toString(16).padStart(2, '0').toUpperCase()} ${(otaData.length & 0xFF).toString(16).padStart(2, '0').toUpperCase()})`);
				console.log(`📋 外层CRC: 0x${outerCRC.toString(16).padStart(2, '0').toUpperCase()}`);
				console.log(`📋 最终指令总长度: ${finalBuffer.byteLength} 字节`);
				
				// 打印完整指令用于调试
				let hexCmd = '';
				for (let i = 0; i < finalBuffer.byteLength; i++) {
					hexCmd += finalView.getUint8(i).toString(16).padStart(2, '0').toUpperCase() + ' ';
				}
				console.log('📡 发送分片数据指令(HEX):', hexCmd);
				console.log('📋 指令结构分析:');
				console.log('   A5FE: 外层帧头');
				console.log('   80: OTA指令');
				console.log(`   ${((otaData.length >> 8) & 0xFF).toString(16).padStart(2, '0').toUpperCase()}${(otaData.length & 0xFF).toString(16).padStart(2, '0').toUpperCase()}: 外层数据长度(大端序显示)`);
				console.log('   FFFE: OTA帧头');
				console.log('   01: 版本号');
				console.log('   15: 分片响应消息码');
				console.log(`   ${((innerCRC >> 8) & 0xFF).toString(16).padStart(2, '0').toUpperCase()}${(innerCRC & 0xFF).toString(16).padStart(2, '0').toUpperCase()}: 内层CRC16(大端序显示)`);
				console.log(`   ${((dataContent.length >> 8) & 0xFF).toString(16).padStart(2, '0').toUpperCase()}${(dataContent.length & 0xFF).toString(16).padStart(2, '0').toUpperCase()}: 内层数据长度(大端序显示，实际小端序存储)`);
				console.log('   00: 结果码');
				console.log(`   ${((fragmentIndex >> 8) & 0xFF).toString(16).padStart(2, '0').toUpperCase()}${(fragmentIndex & 0xFF).toString(16).padStart(2, '0').toUpperCase()}: 分片序号(大端序存储)`);
				console.log(`   [${fragmentData.length}字节分片数据]`);
				console.log(`   ${outerCRC.toString(16).padStart(2, '0').toUpperCase()}: 外层CRC`);
				
				// 验证与期望结果的对比
				console.log('📋 期望结果验证:');
				console.log(`   数据长度应为: 203 (0x00CB), 小端序存储: CB 00, 大端序显示: 00 CB`);
				console.log(`   实际设置: ${dataContent.length} (0x${dataContent.length.toString(16).padStart(4, '0').toUpperCase()}), 小端序存储: ${(dataContent.length & 0xFF).toString(16).padStart(2, '0').toUpperCase()} ${((dataContent.length >> 8) & 0xFF).toString(16).padStart(2, '0').toUpperCase()}, 大端序显示: ${((dataContent.length >> 8) & 0xFF).toString(16).padStart(2, '0').toUpperCase()} ${(dataContent.length & 0xFF).toString(16).padStart(2, '0').toUpperCase()}`);
				console.log(`   CRC16期望: F921(大端序显示), 实际计算: ${((innerCRC >> 8) & 0xFF).toString(16).padStart(2, '0').toUpperCase()}${(innerCRC & 0xFF).toString(16).padStart(2, '0').toUpperCase()}`);
				
				// 发送分片数据
				uni.writeBLECharacteristicValue({
					deviceId: deviceId,
					serviceId: serviceId,
					characteristicId: characteristicId,
					value: finalBuffer,
					success: (res) => {
						console.log(`✅ 分片 ${fragmentIndex} 发送成功 (${this.fragmentSize}字节)`);
						
						// 更新进度
						this.currentFragment = fragmentIndex;
						
						// 检查是否为最后一片 (分片索引从0开始，所以最后一片是totalFragments-1)
						const isLastFragment = (fragmentIndex === this.totalFragments - 1);
						
						if (isLastFragment) {
							// 最后一片发送成功，认为升级完成
							console.log('🎉 最后一片发送成功！');
							console.log(`📊 所有分片发送完成: ${fragmentIndex + 1}/${this.totalFragments} (100%)`);
							
							this.otaProgress = 100;
							this.otaStatus = '升级完成';
							this.otaStatusClass = 'connected';
							this.otaStepText = '固件包发送完成，设备正在安装固件...';
							this.otaCompleted = true;
							
							// 停止分片传输
							this.fragmentTransferActive = false;
							this.clearOTAResponseListener();
							
							// 显示升级完成提示
							this.showUpgradeCompleteDialog();
						} else {
							// 不是最后一片，继续正常流程
							const progress = 75 + Math.floor((fragmentIndex / this.totalFragments) * 10); // 75%-85%
							this.otaProgress = Math.min(progress, 85);
							this.otaStepText = `分片传输中 ${fragmentIndex + 1}/${this.totalFragments}...`;
							
							// 每20个分片显示一次详细进度
							if (fragmentIndex % 20 === 0 || fragmentIndex <= 3 || fragmentIndex >= this.totalFragments - 3) {
								console.log(`📊 传输进度: ${Math.floor(((fragmentIndex + 1)/this.totalFragments)*100)}% (${fragmentIndex + 1}/${this.totalFragments})`);
								console.log(`📦 当前分片: ${fragmentIndex}, 分片大小: ${this.fragmentSize}字节`);
							}
							
							// 重新设置监听器等待下一个分片请求
							if (this.fragmentTransferActive) {
								this.setupFragmentResponseListener();
							}
						}
					},
					fail: (err) => {
						console.error(`❌ 分片 ${fragmentIndex} 发送失败:`, err);
						this.fragmentTransferActive = false;
						this.handleOTAError(`分片 ${fragmentIndex} 发送失败: ` + err.errMsg);
					}
				});
				
			} catch (error) {
				console.error('❌ 构建分片数据失败:', error);
				this.fragmentTransferActive = false;
				this.handleOTAError('构建分片数据失败: ' + error.message);
			}
		},
		
		simulateFragmentTransfer() {
			// 此方法已废弃，使用 startFragmentTransfer
			console.warn('⚠️ simulateFragmentTransfer 方法已废弃');
		},
		
		simulateDownloadComplete() {
			// 此方法已废弃，由设备主动上报
			console.warn('⚠️ simulateDownloadComplete 方法已废弃');
		},
		cancelOTAUpgrade() {
			if (this.otaCompleted) {
				this.finishOTAUpgrade();
				return;
			}
			
			uni.showModal({
				title: '确认取消',
				content: '确定要取消OTA升级吗？升级过程将被中断。',
				confirmText: '确定取消',
				cancelText: '继续升级',
				success: (res) => {
					if (res.confirm) {
						console.log('❌ 用户取消OTA升级');
						
						this.otaUpgrading = false;
						this.otaProgress = 0;
						this.otaStatus = '已取消';
						this.otaStatusClass = 'error';
						this.otaStepText = '升级已被用户取消';
						this.otaCompleted = true;
						
						// 清理所有状态和监听器
						this.clearOTAResponseListener();
						this.fragmentTransferActive = false;
						this.currentFragment = 0;
						
						// 清理定时器
						if (this.otaTimer) {
							clearTimeout(this.otaTimer);
							this.otaTimer = null;
						}
						
						this.showCustomToast({
							message: 'OTA升级已取消',
							type: 'warning'
						});
						
						// 记录升级历史
						this.recordOTAHistory(false);
					}
				}
			});
		},
		
		finishOTAUpgrade() {
			this.otaUpgrading = false;
			this.otaProgress = 0;
			this.otaStatus = '准备中';
			this.otaStatusClass = '';
			this.otaStepText = '';
			this.otaCompleted = false;
			this.currentDeviceId = '';
			this.currentVersion = '';
			this.firmwareUrl = '';
			this.serverVersionInfo = null;
			
			// 清理分片传输状态
			this.fragmentTransferActive = false;
			this.currentFragment = 0;
			
			// 清理监听器和定时器
			this.clearOTAResponseListener();
			if (this.otaTimer) {
				clearTimeout(this.otaTimer);
				this.otaTimer = null;
			}
		},
		
		recordOTAHistory(success) {
			// 记录OTA升级历史
			const otaRecord = {
				id: Date.now(),
				deviceId: this.currentDeviceId,
				deviceName: this.currentDevice?.name || '未知设备',
				macAddress: this.currentDevice?.macAddress || this.currentDevice?.deviceId,
				fromVersion: this.currentVersion,
				toVersion: this.targetVersion,
				firmwareUrl: this.firmwareUrl,
				serverVersion: this.serverVersionInfo,
				success: success,
				timestamp: this.formatDateTime(new Date()),
				duration: success ? '约3分钟' : '未完成'
			};
			
			// 获取已有的OTA历史记录
			let otaHistory = [];
			try {
				const existingHistory = uni.getStorageSync('otaUpgradeHistory');
				if (existingHistory) {
					otaHistory = JSON.parse(existingHistory);
				}
			} catch (e) {
				console.error('读取OTA历史记录失败:', e);
			}
			
			// 添加新记录
			otaHistory.unshift(otaRecord); // 添加到开头，最新的在前面
			
			// 只保留最近50条记录
			if (otaHistory.length > 50) {
				otaHistory = otaHistory.slice(0, 50);
			}
			
			// 保存更新后的记录
			uni.setStorageSync('otaUpgradeHistory', JSON.stringify(otaHistory));
		},
		// CRC16计算函数 - 修正为与Java版本一致的CRC16-CCITT算法
		calculateCRC16(data) {
			// CRC16-CCITT查找表，与Java版本完全一致
			const crc16_ccitt_table = [
				0x0000, 0x1021, 0x2042, 0x3063, 0x4084, 0x50a5, 0x60c6, 0x70e7,
				0x8108, 0x9129, 0xa14a, 0xb16b, 0xc18c, 0xd1ad, 0xe1ce, 0xf1ef,
				0x1231, 0x0210, 0x3273, 0x2252, 0x52b5, 0x4294, 0x72f7, 0x62d6,
				0x9339, 0x8318, 0xb37b, 0xa35a, 0xd3bd, 0xc39c, 0xf3ff, 0xe3de,
				0x2462, 0x3443, 0x0420, 0x1401, 0x64e6, 0x74c7, 0x44a4, 0x5485,
				0xa56a, 0xb54b, 0x8528, 0x9509, 0xe5ee, 0xf5cf, 0xc5ac, 0xd58d,
				0x3653, 0x2672, 0x1611, 0x0630, 0x76d7, 0x66f6, 0x5695, 0x46b4,
				0xb75b, 0xa77a, 0x9719, 0x8738, 0xf7df, 0xe7fe, 0xd79d, 0xc7bc,
				0x48c4, 0x58e5, 0x6886, 0x78a7, 0x0840, 0x1861, 0x2802, 0x3823,
				0xc9cc, 0xd9ed, 0xe98e, 0xf9af, 0x8948, 0x9969, 0xa90a, 0xb92b,
				0x5af5, 0x4ad4, 0x7ab7, 0x6a96, 0x1a71, 0x0a50, 0x3a33, 0x2a12,
				0xdbfd, 0xcbdc, 0xfbbf, 0xeb9e, 0x9b79, 0x8b58, 0xbb3b, 0xab1a,
				0x6ca6, 0x7c87, 0x4ce4, 0x5cc5, 0x2c22, 0x3c03, 0x0c60, 0x1c41,
				0xedae, 0xfd8f, 0xcdec, 0xddcd, 0xad2a, 0xbd0b, 0x8d68, 0x9d49,
				0x7e97, 0x6eb6, 0x5ed5, 0x4ef4, 0x3e13, 0x2e32, 0x1e51, 0x0e70,
				0xff9f, 0xefbe, 0xdfdd, 0xcffc, 0xbf1b, 0xaf3a, 0x9f59, 0x8f78,
				0x9188, 0x81a9, 0xb1ca, 0xa1eb, 0xd10c, 0xc12d, 0xf14e, 0xe16f,
				0x1080, 0x00a1, 0x30c2, 0x20e3, 0x5004, 0x4025, 0x7046, 0x6067,
				0x83b9, 0x9398, 0xa3fb, 0xb3da, 0xc33d, 0xd31c, 0xe37f, 0xf35e,
				0x02b1, 0x1290, 0x22f3, 0x32d2, 0x4235, 0x5214, 0x6277, 0x7256,
				0xb5ea, 0xa5cb, 0x95a8, 0x8589, 0xf56e, 0xe54f, 0xd52c, 0xc50d,
				0x34e2, 0x24c3, 0x14a0, 0x0481, 0x7466, 0x6447, 0x5424, 0x4405,
				0xa7db, 0xb7fa, 0x8799, 0x97b8, 0xe75f, 0xf77e, 0xc71d, 0xd73c,
				0x26d3, 0x36f2, 0x0691, 0x16b0, 0x6657, 0x7676, 0x4615, 0x5634,
				0xd94c, 0xc96d, 0xf90e, 0xe92f, 0x99c8, 0x89e9, 0xb98a, 0xa9ab,
				0x5844, 0x4865, 0x7806, 0x6827, 0x18c0, 0x08e1, 0x3882, 0x28a3,
				0xcb7d, 0xdb5c, 0xeb3f, 0xfb1e, 0x8bf9, 0x9bd8, 0xabbb, 0xbb9a,
				0x4a75, 0x5a54, 0x6a37, 0x7a16, 0x0af1, 0x1ad0, 0x2ab3, 0x3a92,
				0xfd2e, 0xed0f, 0xdd6c, 0xcd4d, 0xbdaa, 0xad8b, 0x9de8, 0x8dc9,
				0x7c26, 0x6c07, 0x5c64, 0x4c45, 0x3ca2, 0x2c83, 0x1ce0, 0x0cc1,
				0xef1f, 0xff3e, 0xcf5d, 0xdf7c, 0xaf9b, 0xbfba, 0x8fd9, 0x9ff8,
				0x6e17, 0x7e36, 0x4e55, 0x5e74, 0x2e93, 0x3eb2, 0x0ed1, 0x1ef0
			];
			
			// CRC初值为0x0000（与Java版本一致）
			let crc_reg = 0x0000;
			
			// 使用与Java版本完全相同的算法
			for (let i = 0; i < data.length; i++) {
				crc_reg = ((crc_reg >> 8) & 0xFF) ^ crc16_ccitt_table[(crc_reg ^ data[i]) & 0xFF];
			}
			
			return crc_reg & 0xFFFF;
		},
		
		// OTA响应监听器状态
		otaResponseWaiting: false,
		otaResponseCallback: null,
		otaResponseTimeout: null,
		
		// 设置OTA响应监听
		setupOTAResponseListener(callback, timeout = 20000) {
			console.log('🔍 设置OTA响应监听器，超时时间:', timeout + 'ms');
			
			// 清除之前的监听器
			this.clearOTAResponseListener();
			
			this.otaResponseWaiting = true;
			this.otaResponseCallback = callback;
			
			// 设置超时
			this.otaResponseTimeout = setTimeout(() => {
				console.log('❌ OTA响应超时');
				const timeoutCallback = this.otaResponseCallback;
				this.clearOTAResponseListener();
				this.otaResponseCallback = null;
				if (timeoutCallback && typeof timeoutCallback === 'function') {
					timeoutCallback({ success: false, error: 'response_timeout', message: '设备响应超时' });
				}
			}, timeout);
		},
		
		// 清除OTA响应监听
		clearOTAResponseListener() {
			console.log('🧹 清除OTA响应监听器');
			this.otaResponseWaiting = false;
			if (this.otaResponseTimeout) {
				clearTimeout(this.otaResponseTimeout);
				this.otaResponseTimeout = null;
			}
			// 不要立即清除callback，而是在调用完成后再清除
		},
		
		// 处理OTA响应数据
		handleOTAResponse(buffer) {
			if (!this.otaResponseWaiting || !this.otaResponseCallback || typeof this.otaResponseCallback !== 'function') {
				return false;
			}
			
			console.log('📥 收到OTA响应数据');
			const dataView = new DataView(buffer);
			console.log('📥 响应数据(dataView):', dataView);
			const uint8Array = new Uint8Array(buffer);
			
			// 打印十六进制数据
			let hexData = '';
			for (let i = 0; i < uint8Array.length; i++) {
				hexData += uint8Array[i].toString(16).padStart(2, '0').toUpperCase() + ' ';
			}
			console.log('📥 响应数据(HEX):', hexData);
			console.log('📥 响应数据(uint8Array):', uint8Array);
			
			// 检查是否是外层指令响应 (A5FE [命令] ...)
			if (uint8Array.length >= 5 && 
				uint8Array[0] === 0xA5 && 
				uint8Array[1] === 0xFE) {
				
				const commandCode = uint8Array[2];
				console.log('📋 响应命令码:', commandCode.toString(16).toUpperCase());
				
				if (commandCode === 0x81) {
					// 0x81 获取设备ID响应，不是OTA格式
					const callback = this.otaResponseCallback;
					this.clearOTAResponseListener();
					this.otaResponseCallback = null;
					callback({
						success: true,
						commandCode: commandCode,
						rawData: uint8Array
					});
					return true;
				} else if (commandCode === 0x80) {
					// 0x80 OTA指令响应，检查内层OTA格式
					const dataLength = (uint8Array[3] << 8) | uint8Array[4];
					console.log('📋 OTA响应数据长度:', dataLength);
					
					// 解析OTA数据区
					if (uint8Array.length >= 7 && 
						uint8Array[5] === 0xFF && 
						uint8Array[6] === 0xFE) {
						
						const version = uint8Array[7];
						const messageCode = uint8Array[8];
						
						console.log('📋 OTA版本:', version);
						console.log('📋 OTA消息码:', messageCode.toString(16).toUpperCase());
						
						// 调用回调函数
						const callback = this.otaResponseCallback;
						this.clearOTAResponseListener();
						this.otaResponseCallback = null;
						callback({
							success: true,
							version: version,
							messageCode: messageCode,
							data: uint8Array.slice(9),
							rawData: uint8Array
						});
						
						return true;
					} else {
						console.error('❌ OTA数据区格式错误');
						const callback = this.otaResponseCallback;
						this.clearOTAResponseListener();
						this.otaResponseCallback = null;
						callback({
							success: false,
							error: 'invalid_ota_format',
							message: 'OTA数据区格式错误'
						});
						return true;
					}
				}
			}
			
			return false;
		},
		
		executeUpgrade() {
			this.otaStepText = '正在执行固件升级...';
			this.otaProgress = 90;
			this.otaStatus = '升级中';
			this.otaStatusClass = 'scanning';
			
			console.log('=== OTA流程步骤14：执行升级 ===');
			console.log('🔄 发送执行升级指令 (0x80+0x85)');
			console.log('📋 指令内容：通知设备开始固件升级');
			
			const deviceId = this.currentDevice?.deviceId;
			const serviceId = uni.getStorageSync('writeServiceId');
			const characteristicId = uni.getStorageSync('writeCharacteristicId');
			
			if (!deviceId || !serviceId || !characteristicId) {
				console.error('❌ 蓝牙连接信息不完整');
				this.handleOTAError('蓝牙连接信息不完整');
				return;
			}
			
			try {
				// 构建OTA执行升级指令
				const otaData = new Uint8Array(8); // FFFE(2) + 版本(1) + 消息码(1) + CRC16(2) + 数据长度(2)
				
				otaData[0] = 0xFF; // OTA帧头1
				otaData[1] = 0xFE; // OTA帧头2
				otaData[2] = 0x01; // 版本号
				otaData[3] = 0x85; // 执行升级消息码
				
				// 数据长度 (执行升级没有额外数据)
				otaData[6] = 0x00; // 数据长度高字节
				otaData[7] = 0x00; // 数据长度低字节
				
				// 计算内层CRC16 - 修正逻辑：先将CRC16字段置0，计算整个内层数据，再回填
				// 1. 先将CRC16字段置0
				otaData[4] = 0x00; // CRC16高字节置0
				otaData[5] = 0x00; // CRC16低字节置0
				
				// 2. 计算整个内层数据的CRC16（包含置0的CRC16字段）
				const innerCRC = this.calculateCRC16(otaData);
				
				// 3. 将计算结果回填到CRC16字段
				otaData[4] = (innerCRC >> 8) & 0xFF;  // CRC16高字节
				otaData[5] = innerCRC & 0xFF; // CRC16低字节
				
				// 构建完整指令
				const buffer = new ArrayBuffer(5 + otaData.length);
				const dataView = new DataView(buffer);
				
				// 外层指令
				dataView.setUint8(0, 0xA5); // 帧头1
				dataView.setUint8(1, 0xFE); // 帧头2
				dataView.setUint8(2, 0x80); // OTA指令
				dataView.setUint8(3, (otaData.length >> 8) & 0xFF); // 数据长度高字节
				dataView.setUint8(4, otaData.length & 0xFF); // 数据长度低字节
				
				// OTA数据区
				for (let i = 0; i < otaData.length; i++) {
					dataView.setUint8(5 + i, otaData[i]);
				}
				
				// 计算外层CRC校验：命令 + 数据长度 + OTA数据区
				let outerCRC = 0x80 + ((otaData.length >> 8) & 0xFF) + (otaData.length & 0xFF);
				for (let i = 0; i < otaData.length; i++) {
					outerCRC += otaData[i];
				}
				outerCRC = outerCRC & 0xFF;
				
				// 添加外层CRC到指令末尾
				const finalBuffer = new ArrayBuffer(buffer.byteLength + 1);
				const finalView = new DataView(finalBuffer);
				for (let i = 0; i < buffer.byteLength; i++) {
					finalView.setUint8(i, dataView.getUint8(i));
				}
				finalView.setUint8(buffer.byteLength, outerCRC);
				
				// 打印发送的指令
				let hexCmd = '';
				for (let i = 0; i < finalBuffer.byteLength; i++) {
					hexCmd += finalView.getUint8(i).toString(16).padStart(2, '0').toUpperCase() + ' ';
				}
				console.log('📡 发送执行升级指令(HEX):', hexCmd);
				console.log('📋 指令解析：A5FE(外层帧头) 80(OTA指令) ' + 
					otaData.length.toString(16).padStart(4, '0').toUpperCase() + '(数据长度) FFFE(OTA帧头) 01(版本) 85(执行升级) ' + 
					innerCRC.toString(16).padStart(4, '0').toUpperCase() + '(内层CRC16) 0000(数据长度) ' +
					outerCRC.toString(16).padStart(2, '0').toUpperCase() + '(外层CRC)');
				
				// 设置响应监听器，监听升级过程中的各种状态
				this.setupUpgradeResponseListener();
				
				// 发送指令
				uni.writeBLECharacteristicValue({
					deviceId: deviceId,
					serviceId: serviceId,
					characteristicId: characteristicId,
					value: finalBuffer,
					success: (res) => {
						console.log('✅ 执行升级指令发送成功');
						console.log('⏳ 等待设备开始升级过程...');
					},
					fail: (err) => {
						console.error('❌ 执行升级指令发送失败:', err);
						this.clearOTAResponseListener();
						this.handleOTAError('执行升级指令发送失败: ' + err.errMsg);
					}
				});
				
			} catch (error) {
				console.error('❌ 构建执行升级指令失败:', error);
				this.handleOTAError('构建执行升级指令失败: ' + error.message);
			}
		},
		
		setupUpgradeResponseListener() {
			console.log('🔍 设置升级过程监听器');
			
			// 设置监听器监听升级过程的各种状态上报
			this.setupOTAResponseListener((response) => {
				if (response.success) {
					try {
						if (response.messageCode === 0x85) {
							// 设备确认开始升级
							console.log('=== OTA流程步骤15：设备返回执行结果 ===');
							console.log('✅ 设备确认：开始执行升级');
							console.log('⚙️ 设备开始内部升级流程...');
							
							this.otaProgress = 92;
							this.otaStepText = '设备正在执行内部升级...';
							
							// 继续监听升级进度状态
							this.setupUpgradeResponseListener();
							
						} else if (response.messageCode === 0x86) {
							// 设备上报升级结果
							console.log('=== OTA流程步骤16：设备上报升级结果 ===');
							console.log('✅ 收到升级结果');
							
							if (response.data && response.data.length > 2) {
								const resultData = response.data.slice(2); // 跳过CRC16
								if (resultData.length > 0) {
									const resultCode = resultData[0]; // 第一个字节是结果码
									
									console.log('📋 升级结果码:', resultCode.toString(16).toUpperCase());
									
									if (resultCode === 0x00) {
										console.log('✅ 固件升级成功完成！');
										console.log('🔄 设备准备重启以应用新固件...');
										
										this.otaProgress = 98;
										this.otaStepText = '升级成功，设备准备重启...';
										
										// 发送确认结果
										this.sendUpgradeConfirmation();
									} else {
										console.error('❌ 固件升级失败，错误码:', resultCode);
										this.handleOTAError('固件升级失败，错误码: 0x' + resultCode.toString(16).toUpperCase());
									}
								} else {
									console.error('❌ 升级结果数据为空');
									this.handleOTAError('升级结果数据为空');
								}
							} else {
								console.error('❌ 升级结果响应格式错误');
								this.handleOTAError('升级结果响应格式错误');
							}
						} else {
							// 其他状态上报 (可能是升级进度)
							console.log('📋 收到升级状态上报，消息码:', response.messageCode.toString(16).toUpperCase());
							// 继续监听
							this.setupUpgradeResponseListener();
						}
					} catch (error) {
						console.error('❌ 升级响应处理异常:', error);
						this.handleOTAError('升级响应处理异常: ' + error.message);
					}
				} else {
					console.error('❌ 升级过程失败:', response.message);
					this.handleOTAError('升级过程失败: ' + response.message);
				}
			}, 120000); // 2分钟超时，升级过程可能需要较长时间
		},
		
		sendUpgradeConfirmation() {
			console.log('=== OTA流程步骤17：小程序确认结果 ===');
			console.log('🔄 发送升级结果确认指令 (0x87)');
			console.log('📋 确认内容：收到升级成功通知');
			
			const deviceId = this.currentDevice?.deviceId;
			const serviceId = uni.getStorageSync('writeServiceId');
			const characteristicId = uni.getStorageSync('writeCharacteristicId');
			
			if (!deviceId || !serviceId || !characteristicId) {
				console.error('❌ 蓝牙连接信息不完整');
				// 即使发送确认失败，也认为升级成功
				this.completeUpgrade();
				return;
			}
			
			try {
				// 构建确认指令
				const confirmData = new Uint8Array([0x00]); // 确认成功
				
				// 构建OTA数据区：FFFE + 版本 + 消息码 + CRC16 + 数据长度 + 数据内容
				const otaData = new Uint8Array(6 + 2 + confirmData.length);
				
				otaData[0] = 0xFF; // OTA帧头1
				otaData[1] = 0xFE; // OTA帧头2
				otaData[2] = 0x01; // 版本号
				otaData[3] = 0x87; // 确认升级结果消息码
				
				// 数据长度
				otaData[6] = (confirmData.length >> 8) & 0xFF; // 数据长度高字节
				otaData[7] = confirmData.length & 0xFF; // 数据长度低字节
				
				// 数据内容
				for (let i = 0; i < confirmData.length; i++) {
					otaData[8 + i] = confirmData[i];
				}
				
				// 计算内层CRC16 - 修正逻辑：先将CRC16字段置0，计算整个内层数据，再回填
				// 1. 先将CRC16字段置0
				otaData[4] = 0x00; // CRC16高字节置0
				otaData[5] = 0x00; // CRC16低字节置0
				
				// 2. 计算整个内层数据的CRC16（包含置0的CRC16字段）
				const innerCRC = this.calculateCRC16(otaData);
				
				// 3. 将计算结果回填到CRC16字段
				otaData[4] = (innerCRC >> 8) & 0xFF; // CRC16高字节
				otaData[5] = innerCRC & 0xFF; // CRC16低字节
				
				// 构建完整指令
				const buffer = new ArrayBuffer(5 + otaData.length);
				const dataView = new DataView(buffer);
				
				// 外层指令
				dataView.setUint8(0, 0xA5); // 帧头1
				dataView.setUint8(1, 0xFE); // 帧头2
				dataView.setUint8(2, 0x80); // OTA指令
				dataView.setUint8(3, (otaData.length >> 8) & 0xFF); // 数据长度高字节
				dataView.setUint8(4, otaData.length & 0xFF); // 数据长度低字节
				
				// OTA数据区
				for (let i = 0; i < otaData.length; i++) {
					dataView.setUint8(5 + i, otaData[i]);
				}
				
				// 计算外层CRC校验：命令 + 数据长度 + OTA数据区
				let outerCRC = 0x80 + ((otaData.length >> 8) & 0xFF) + (otaData.length & 0xFF);
				for (let i = 0; i < otaData.length; i++) {
					outerCRC += otaData[i];
				}
				outerCRC = outerCRC & 0xFF;
				
				// 添加外层CRC到指令末尾
				const finalBuffer = new ArrayBuffer(buffer.byteLength + 1);
				const finalView = new DataView(finalBuffer);
				for (let i = 0; i < buffer.byteLength; i++) {
					finalView.setUint8(i, dataView.getUint8(i));
				}
				finalView.setUint8(buffer.byteLength, outerCRC);
				
				// 打印发送的指令
				let hexCmd = '';
				for (let i = 0; i < finalBuffer.byteLength; i++) {
					hexCmd += finalView.getUint8(i).toString(16).padStart(2, '0').toUpperCase() + ' ';
				}
				console.log('📡 发送确认指令(HEX):', hexCmd);
				console.log('📋 指令解析：A5FE(外层帧头) 80(OTA指令) ' + 
					otaData.length.toString(16).padStart(4, '0').toUpperCase() + '(数据长度) FFFE(OTA帧头) 01(版本) 87(确认升级结果) ' +
					innerCRC.toString(16).padStart(4, '0').toUpperCase() + '(内层CRC16) ' +
					confirmData.length.toString(16).padStart(4, '0').toUpperCase() + '(数据长度) 00(确认成功) ' +
					outerCRC.toString(16).padStart(2, '0').toUpperCase() + '(外层CRC)');
				
				// 发送确认指令
				uni.writeBLECharacteristicValue({
					deviceId: deviceId,
					serviceId: serviceId,
					characteristicId: characteristicId,
					value: finalBuffer,
					success: (res) => {
						console.log('✅ 确认指令发送成功');
						console.log('🔄 设备开始重启...');
						
						// 升级完成
						this.completeUpgrade();
					},
					fail: (err) => {
						console.error('❌ 确认指令发送失败:', err);
						// 即使发送确认失败，也认为升级成功
						this.completeUpgrade();
					}
				});
				
			} catch (error) {
				console.error('❌ 构建确认指令失败:', error);
				// 即使构建确认失败，也认为升级成功
				this.completeUpgrade();
			}
		},
		
		completeUpgrade() {
			console.log('=== OTA升级流程完成 ===');
			console.log('🎉 设备重启完成！');
			console.log('🔄 设备重新连接中...');
			console.log(`📋 升级前版本: ${this.currentVersion}`);
			console.log(`📋 升级后版本: ${this.targetVersion}`);
			console.log('✅ OTA升级全流程成功完成！');
			console.log('==========================================');
			
			this.otaProgress = 100;
			this.otaStatus = '升级成功';
			this.otaStatusClass = 'connected';
			this.otaStepText = 'OTA升级已成功完成！';
			this.otaCompleted = true;
			
			// 清理响应监听器
			this.clearOTAResponseListener();
			
			// 停止分片传输
			this.fragmentTransferActive = false;
			
			// 清理固件数据
			this.firmwareBuffer = null;
			this.firmwarePath = '';
			this.firmwareSize = 0;
			console.log('🧹 固件数据已清理');
			
			// 更新当前版本为目标版本
			this.currentVersion = this.targetVersion;
			
			this.showCustomToast({
				message: 'OTA升级成功完成！',
				type: 'default'
			});
			
			// 记录升级历史
			this.recordOTAHistory(true);
			
			// 升级完成后的日志总结
			setTimeout(() => {
				console.log('📊 OTA升级统计:');
				console.log('   • 设备ID:', this.currentDeviceId);
				console.log('   • 升级版本:', this.targetVersion);
				console.log('   • 分片总数: 129个');
				console.log('   • 升级耗时: 根据实际情况');
				console.log('   • 升级结果: 成功');
				console.log('✅ 升级历史已保存到本地存储');
			}, 1000);
		},
		// 显示升级完成对话框
		showUpgradeCompleteDialog() {
			// 记录升级历史
			this.recordOTAHistory(true);
			
			// 清理固件数据
			this.firmwareBuffer = null;
			this.firmwarePath = '';
			this.firmwareSize = 0;
			console.log('🧹 固件数据已清理');
			
			// 延迟1秒显示对话框，让用户看到100%完成状态
			setTimeout(() => {
				uni.showModal({
					title: '🎉 固件升级完成',
					content: `恭喜！固件包已成功发送至设备\n\n📱 接下来设备会：\n• 自动重启\n• 安装新固件\n• 断开蓝牙连接\n\n⏱️ 请稍等约30秒后：\n• 点击蓝牙扫描重新连接设备\n• 使用下方"验证升级结果"按钮检查\n• 确认版本号是否已更新为 ${this.targetVersion}\n\n✅ 版本号匹配即表示升级成功！`,
					confirmText: '我知道了',
					showCancel: false,
					success: (res) => {
						if (res.confirm) {
							console.log('✅ 用户已确认升级完成提示');
							
							// 显示成功提示
							this.showCustomToast({
								message: '固件升级已完成，请重新连接设备验证',
								type: 'success'
							});
							
							// 自动断开蓝牙连接（如果还连接着）
							this.disconnectDeviceAfterOTA();
						}
					}
				});
			}, 1000);
		},
		
		// 升级完成后检查设备版本
		checkVersionAfterUpgrade() {
			if (!this.isConnected || !this.currentDevice) {
				this.showCustomToast({
					message: '请先连接蓝牙设备',
					type: 'warning'
				});
				return;
			}
			
			// 显示检查状态
			this.showCustomToast({
				message: '正在检查设备版本...',
				type: 'default'
			});
			
			// 获取设备ID
			this.sendGetDeviceIdCommand()
				.then(deviceId => {
					if (deviceId) {
						this.currentDeviceId = deviceId;
						// 查询设备版本
						return this.sendGetVersionCommand();
					} else {
						throw new Error('获取设备ID失败');
					}
				})
				.then(version => {
					if (version) {
						console.log(`📋 当前设备版本: ${version}`);
						console.log(`📋 目标版本: ${this.targetVersion}`);
						
						if (version === this.targetVersion) {
							// 版本匹配，升级成功
							uni.showModal({
								title: '🎉 升级验证成功',
								content: `恭喜！设备版本已成功更新\n\n当前版本：${version}\n目标版本：${this.targetVersion}\n\n✅ 固件升级已完成！设备正常运行中。\n\n您可以开始使用设备的新功能了。`,
								confirmText: '太好了',
								showCancel: false,
								success: () => {
									this.showCustomToast({
										message: '🎉 固件升级验证成功！',
										type: 'success'
									});
									
									// 清理OTA状态，让用户可以正常使用设备
									this.finishOTAUpgrade();
								}
							});
						} else {
							// 版本不匹配，升级可能失败或需要重试
							uni.showModal({
								title: '⏱️ 版本验证结果',
								content: `当前设备版本：${version}\n目标版本：${this.targetVersion}\n\n❗ 版本号尚未更新，可能原因：\n\n⏳ 正常情况：\n• 设备仍在安装固件中\n• 需要等待更长时间\n\n❌ 异常情况：\n• 固件安装失败\n• 固件文件损坏\n\n💡 建议操作：\n稍等30秒后重新检查，或重新发起升级`,
								confirmText: '30秒后重新检查',
								cancelText: '重新升级',
								success: (res) => {
									if (res.confirm) {
										// 延迟30秒后重新检查
										this.showCustomToast({
											message: '30秒后自动检查版本...',
											type: 'default'
										});
										setTimeout(() => {
											this.checkVersionAfterUpgrade();
										}, 30000);
									} else {
										// 用户选择重新升级
										this.finishOTAUpgrade();
										this.showCustomToast({
											message: '请重新点击OTA升级按钮',
											type: 'warning'
										});
									}
								}
							});
						}
					} else {
						throw new Error('获取设备版本失败');
					}
				})
				.catch(error => {
					console.error('❌ 版本检查失败:', error);
					this.showCustomToast({
						message: '版本检查失败: ' + error.message,
						type: 'error'
					});
				});
		},
		
		// 发送获取设备ID命令（返回Promise）
		sendGetDeviceIdCommand() {
			return new Promise((resolve, reject) => {
				console.log('📤 发送获取设备ID命令: 0x81');
				
				const command = new Uint8Array([0x81]);
				
				this.setupOTAResponseListener((response) => {
					try {
						if (response && response.length >= 17) {
							// 设备ID在响应的第2到第17字节（16字节）
							const deviceIdBytes = response.slice(1, 17);
							const deviceId = Array.from(deviceIdBytes)
								.map(b => b.toString(16).padStart(2, '0').toUpperCase())
								.join('');
							
							console.log('✅ 收到设备ID响应:', deviceId);
							resolve(deviceId);
						} else {
							console.error('❌ 设备ID响应格式错误:', response);
							reject(new Error('设备ID响应格式错误'));
						}
					} catch (error) {
						console.error('❌ 解析设备ID失败:', error);
						reject(error);
					}
				}, 10000);
				
				// 获取蓝牙连接信息
				const deviceId = this.currentDevice?.deviceId;
				const serviceId = uni.getStorageSync('writeServiceId');
				const characteristicId = uni.getStorageSync('writeCharacteristicId');
				
				if (!deviceId || !serviceId || !characteristicId) {
					console.error('❌ 蓝牙连接信息不完整');
					reject(new Error('蓝牙连接信息不完整，请重新连接设备'));
					return;
				}
				
				// 发送命令
				uni.writeBLECharacteristicValue({
					deviceId: deviceId,
					serviceId: serviceId,
					characteristicId: characteristicId,
					value: command.buffer,
					success: (res) => {
						console.log('✅ 获取设备ID命令发送成功');
						console.log('⏳ 等待设备响应...');
					},
					fail: (err) => {
						console.error('❌ 获取设备ID命令发送失败:', err);
						this.clearOTAResponseListener();
						reject(new Error('指令发送失败: ' + err.errMsg));
					}
				});
			});
		},
		
		// 发送获取版本命令（返回Promise）
		sendGetVersionCommand() {
			return new Promise((resolve, reject) => {
				console.log('📤 发送获取版本命令: 0x80 0x13');
				
				const command = new Uint8Array([0x80, 0x13]);
				
				this.setupOTAResponseListener((response) => {
					try {
						if (response && response.length >= 3 && response[0] === 0x80 && response[1] === 0x13) {
							// 版本号在第3字节
							const version = response[2];
							const versionStr = `V${version}`;
							
							console.log('✅ 收到版本响应:', versionStr);
							resolve(versionStr);
						} else {
							console.error('❌ 版本响应格式错误:', response);
							reject(new Error('版本响应格式错误'));
						}
					} catch (error) {
						console.error('❌ 解析版本失败:', error);
						reject(error);
					}
				}, 10000);
				
				// 获取蓝牙连接信息
				const deviceId = this.currentDevice?.deviceId;
				const serviceId = uni.getStorageSync('writeServiceId');
				const characteristicId = uni.getStorageSync('writeCharacteristicId');
				
				if (!deviceId || !serviceId || !characteristicId) {
					console.error('❌ 蓝牙连接信息不完整');
					reject(new Error('蓝牙连接信息不完整，请重新连接设备'));
					return;
				}
				
				// 发送命令
				uni.writeBLECharacteristicValue({
					deviceId: deviceId,
					serviceId: serviceId,
					characteristicId: characteristicId,
					value: command.buffer,
					success: (res) => {
						console.log('✅ 获取版本命令发送成功');
						console.log('⏳ 等待设备响应...');
					},
					fail: (err) => {
						console.error('❌ 发送获取版本命令失败:', err);
						this.clearOTAResponseListener();
						reject(new Error('指令发送失败: ' + err.errMsg));
					}
				});
			});
		},
		
		// 检查设备OTA升级历史
		checkDeviceOTAHistory() {
			if (!this.currentDevice) {
				this.hasOTAHistory = false;
				return;
			}
			
			const deviceKey = this.currentDevice.macAddress || this.currentDevice.deviceId;
			console.log('🔍 检查设备OTA历史:', deviceKey);
			
			try {
				// 从本地存储读取OTA升级历史
				const otaHistoryString = uni.getStorageSync('otaUpgradeHistory');
				if (otaHistoryString) {
					const otaHistory = JSON.parse(otaHistoryString);
					
					// 检查是否有该设备的升级记录
					const deviceHistory = otaHistory.find(record => 
						record.macAddress === deviceKey || 
						record.deviceId === deviceKey ||
						record.deviceName === this.currentDevice.name
					);
					
					if (deviceHistory) {
						console.log('✅ 发现设备OTA历史记录:', deviceHistory);
						this.hasOTAHistory = true;
						
						// 缓存到Map中
						this.deviceOTAHistoryMap.set(deviceKey, deviceHistory);
					} else {
						console.log('📋 设备无OTA历史记录');
						this.hasOTAHistory = false;
					}
				} else {
					console.log('📋 无任何OTA历史记录');
					this.hasOTAHistory = false;
				}
			} catch (error) {
				console.error('❌ 检查OTA历史失败:', error);
				this.hasOTAHistory = false;
			}
		},
		
		// 验证设备升级状态（独立于OTA流程）
		verifyDeviceUpgradeStatus() {
			if (!this.isConnected || !this.currentDevice) {
				this.showCustomToast({
					message: '请先连接蓝牙设备',
					type: 'warning'
				});
				return;
			}
			
			this.showCustomToast({
				message: '正在验证设备升级状态...',
				type: 'default'
			});
			
			console.log('🔍 开始验证设备升级状态');
			
			// 获取设备当前版本
			this.getDeviceCurrentVersion()
				.then(currentVersion => {
					console.log('📋 设备当前版本:', currentVersion);
					
					// 获取设备应有的最新版本（从后端API）
					const deviceId = this.getDeviceIdFromHistory();
					if (deviceId) {
						return this.getLatestVersionFromServer(deviceId, currentVersion);
					} else {
						// 如果没有设备ID历史，先获取设备ID
						return this.sendGetDeviceIdCommand()
							.then(deviceId => {
								return this.getLatestVersionFromServer(deviceId, currentVersion);
							});
					}
				})
				.then(result => {
					this.showVersionComparisonResult(result);
				})
				.catch(error => {
					console.error('❌ 验证升级状态失败:', error);
					this.showCustomToast({
						message: '验证失败: ' + error.message,
						type: 'error'
					});
				});
		},
		
		// 获取设备当前版本
		getDeviceCurrentVersion() {
			return new Promise((resolve, reject) => {
				console.log('📤 发送获取版本命令: 0x80 0x13');
				
				const command = new Uint8Array([0x80, 0x13]);
				
				this.setupOTAResponseListener((response) => {
					try {
						if (response && response.length >= 3 && response[0] === 0x80 && response[1] === 0x13) {
							// 版本号在第3字节
							const version = response[2];
							const versionStr = `V${version}`;
							
							console.log('✅ 收到版本响应:', versionStr);
							resolve(versionStr);
						} else {
							console.error('❌ 版本响应格式错误:', response);
							reject(new Error('版本响应格式错误'));
						}
					} catch (error) {
						console.error('❌ 解析版本失败:', error);
						reject(error);
					}
				}, 10000);
				
				// 获取蓝牙连接信息
				const deviceId = this.currentDevice?.deviceId;
				const serviceId = uni.getStorageSync('writeServiceId');
				const characteristicId = uni.getStorageSync('writeCharacteristicId');
				
				if (!deviceId || !serviceId || !characteristicId) {
					reject(new Error('蓝牙连接信息不完整，请重新连接设备'));
					return;
				}
				
				// 发送命令
				uni.writeBLECharacteristicValue({
					deviceId: deviceId,
					serviceId: serviceId,
					characteristicId: characteristicId,
					value: command.buffer,
					success: (res) => {
						console.log('✅ 获取版本命令发送成功');
					},
					fail: (err) => {
						console.error('❌ 获取版本命令发送失败:', err);
						this.clearOTAResponseListener();
						reject(new Error('指令发送失败: ' + err.errMsg));
					}
				});
			});
		},
		
		// 从历史记录获取设备ID
		getDeviceIdFromHistory() {
			const deviceKey = this.currentDevice?.macAddress || this.currentDevice?.deviceId;
			const history = this.deviceOTAHistoryMap.get(deviceKey);
			return history?.deviceId || null;
		},
		
		// 从服务端获取最新版本信息
		getLatestVersionFromServer(deviceId, currentVersion) {
			return new Promise((resolve, reject) => {
				console.log(`🌐 请求服务端最新版本: deviceId=${deviceId}`);
				
				getFirmwareVersion(deviceId).then(res => {
					console.log('📡 服务端版本响应:', res);
					
					if (res.code === 200 && res.data) {
						const latestVersion = res.data.version;
						console.log(`📋 服务端最新版本: ${latestVersion}`);
						
						resolve({
							currentVersion: currentVersion,
							latestVersion: latestVersion,
							isUpToDate: currentVersion === latestVersion,
							deviceId: deviceId
						});
					} else {
						reject(new Error('获取服务端版本信息失败'));
					}
				}).catch(error => {
					console.error('❌ 请求服务端版本失败:', error);
					reject(new Error('网络请求失败: ' + error.message));
				});
			});
		},
		
		// 显示版本比对结果
		showVersionComparisonResult(result) {
			const { currentVersion, latestVersion, isUpToDate, deviceId } = result;
			
			if (isUpToDate) {
				// 版本一致，升级成功
				uni.showModal({
					title: '✅ 设备版本验证',
					content: `设备版本验证完成！\n\n当前版本：${currentVersion}\n最新版本：${latestVersion}\n\n🎉 设备已是最新版本，上次升级成功！\n\n设备运行正常，可以正常使用。`,
					confirmText: '太好了',
					showCancel: false,
					success: () => {
						this.showCustomToast({
							message: '✅ 设备版本已是最新！',
							type: 'success'
						});
					}
				});
			} else {
				// 版本不一致，需要升级
				uni.showModal({
					title: '⚠️ 设备版本验证',
					content: `设备版本验证完成！\n\n当前版本：${currentVersion}\n最新版本：${latestVersion}\n\n❗ 设备版本落后，建议进行升级\n\n可能原因：\n• 上次升级未完成\n• 固件安装失败\n• 有新版本发布`,
					confirmText: '立即升级',
					cancelText: '稍后升级',
					success: (res) => {
						if (res.confirm) {
							// 用户选择立即升级
							this.showCustomToast({
								message: '请点击下方OTA升级按钮开始升级',
								type: 'warning'
							});
						} else {
							this.showCustomToast({
								message: '建议及时升级以获得最佳体验',
								type: 'default'
							});
						}
					}
				});
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.bluetooth-page {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.container {
	padding: 30rpx;
}

.status-card {
	background-color: #ffffff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.status-text {
	text-align: center;
	color: #333333;
	margin-bottom: 30rpx;
	padding: 20rpx;
	border-radius: 10rpx;
	
	&.scanning {
		background-color: #e6f2ff;
		color: #47afff;
	}
	
	&.connected {
		background-color: #e6f9eb;
		color: #4cd964;
	}
	
	&.error {
		background-color: #ffebeb;
		color: #ff3b30;
	}
}

.scan-indicator {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 30rpx;
}

.loader {
	border: 4rpx solid #f3f3f3;
	border-top: 4rpx solid #47afff;
	border-radius: 50%;
	width: 40rpx;
	height: 40rpx;
	animation: spin 1s linear infinite;
	margin-right: 20rpx;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.btn {
	display: block;
	width: 100%;
	padding: 24rpx 0;
	font-size: 32rpx;
	font-weight: 500;
	border-radius: 16rpx;
	border: none;
	text-align: center;
	margin-bottom: 20rpx;
}

.btn-primary {
	background-color: #47afff;
	color: white;
}

.btn-secondary {
	background-color: #f5f5f5;
	color: #47afff;
	border: 2rpx solid #47afff;
}

.btn-success {
	background-color: #4cd964;
	color: white;
}

.btn-warning {
	background-color: #ff9500;
	color: white;
}

.btn-sm {
	padding: 16rpx 24rpx;
	font-size: 28rpx;
	width: auto;
}

.device-list {
	background-color: #ffffff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.card-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 30rpx;
}

.empty-devices {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 60rpx 0;
	background-color: #ffffff;
	border-radius: 20rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.empty-image {
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 20rpx;
}

.empty-text {
	color: #999999;
	text-align: center;
	font-size: 28rpx;
}

.device-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx 0;
	border-bottom: 2rpx solid #f5f5f5;
	
	&:last-child {
		border-bottom: none;
	}
}

.device-info {
	flex: 1;
}

.device-name {
	font-size: 32rpx;
	font-weight: 500;
	color: #333333;
	margin-bottom: 10rpx;
}

.device-mac {
	font-size: 24rpx;
	color: #999999;
	margin-bottom: 10rpx;
}

.device-id {
	font-size: 24rpx;
	color: #999999;
}

.connect-btn {
	margin-bottom: 0;
}

.command-btn {
	margin-top: 30rpx;
}

.test-btn {
	margin-top: 30rpx;
}

.record-btn {
	margin-top: 30rpx;
}

.ota-btn {
	margin-top: 30rpx;
}

.verify-status-btn {
	background: linear-gradient(135deg, #17a2b8, #138496);
	color: white;
	font-weight: bold;
	margin-top: 20rpx;
}

.verify-status-btn:active {
	background: linear-gradient(135deg, #138496, #10707f);
}

.toast-container {
	position: fixed;
	bottom: 100rpx;
	left: 50%;
	transform: translateX(-50%);
	z-index: 9999;
}

.toast-message {
	padding: 20rpx 40rpx;
	background-color: rgba(0, 0, 0, 0.7);
	color: #ffffff;
	border-radius: 10rpx;
	font-size: 28rpx;
	
	&.error {
		background-color: rgba(255, 59, 48, 0.9);
	}
	
	&.warning {
		background-color: rgba(255, 204, 0, 0.9);
	}
}

.ota-progress-card {
	background-color: #ffffff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.ota-info {
	margin-bottom: 30rpx;
}

.ota-info-item {
	margin-bottom: 10rpx;
}

.info-label {
	font-size: 28rpx;
	font-weight: 500;
	color: #333333;
	margin-right: 10rpx;
}

.info-value {
	font-size: 28rpx;
	color: #999999;
	
	&.status-text {
		font-weight: 500;
		
		&.scanning {
			color: #47afff;
		}
		
		&.connected {
			color: #4cd964;
		}
		
		&.error {
			color: #ff3b30;
		}
	}
}

.progress-container {
	margin-bottom: 30rpx;
}

.progress-bar {
	background-color: #f5f5f5;
	border-radius: 10rpx;
	height: 20rpx;
	margin-bottom: 10rpx;
	overflow: hidden;
}

.progress-fill {
	background: linear-gradient(45deg, #47afff, #5bc0de);
	border-radius: 10rpx;
	height: 100%;
	transition: width 0.3s ease-in-out;
	position: relative;
	
	&::after {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(45deg, 
			transparent 25%, 
			rgba(255,255,255,0.3) 25%, 
			rgba(255,255,255,0.3) 50%, 
			transparent 50%, 
			transparent 75%, 
			rgba(255,255,255,0.3) 75%);
		background-size: 30rpx 30rpx;
		animation: progressMove 2s linear infinite;
		border-radius: 10rpx;
	}
}

@keyframes progressMove {
	0% {
		background-position: 0 0;
	}
	100% {
		background-position: 30rpx 0;
	}
}

.progress-text {
	font-size: 28rpx;
	font-weight: 500;
	color: #333333;
	text-align: right;
}

.ota-step-info {
	margin-bottom: 30rpx;
}

.step-text {
	font-size: 28rpx;
	color: #999999;
}

.cancel-btn {
	margin-top: 0;
	margin-right: 0;
	width: 100%;
	display: block;
}

.complete-btn {
	margin-top: 0;
	margin-left: 0;
	width: 100%;
	display: block;
}

.verify-btn {
	background: linear-gradient(135deg, #28a745, #20c997);
	color: white;
	font-weight: bold;
	font-size: 28rpx;
	margin-top: 20rpx;
	width: 100%;
	display: block;
}

.verify-btn:active {
	background: linear-gradient(135deg, #1e7e34, #17a2b8);
}

.ota-button-group {
	display: flex;
	flex-direction: column;
	margin-top: 30rpx;
}
</style> 