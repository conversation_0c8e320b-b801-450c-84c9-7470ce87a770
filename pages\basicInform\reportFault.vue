<template>
	<!-- 上报故障 -->
	<view class="patientInform">
		<view class="form_view rightForm">
			<u--form labelPosition="left" :model="modelPatient" :rules="patientRules" ref="patientForm" labelWidth="120" :labelStyle="labelStyle">
				<u-form-item label="选择设备" prop="userInfo.equipDev" borderBottom @click="showDevPicker = true;"
					ref="item1" :required="true">
					<u--input v-model="modelPatient.userInfo.equipDev" disabled disabledColor="#ffffff"
						placeholder="请选择设备" border="none" inputAlign="right"></u--input>
					<u-icon slot="right" name="arrow-right"></u-icon>
				</u-form-item>
				<u-form-item style="background-color: #ffffff;" label="设备类型" prop="userInfo.type" borderBottom :required="true">
					<input style="background-color: #ffffff;text-align: right;" placeholder-class="placeholderEE"  border="none" :disabled="true" readonly   v-model="modelPatient.userInfo.type" maxlength="15" inputAlign="right" 
						></input>
						
					<!-- <textarea disabled  placeholder="请输入设备类型" placeholder-class="placeholderEE" inputAlign="right"  style="width: 100%;" class="textarea1"  v-model="modelPatient.userInfo.type"  auto-height ></textarea> -->
				</u-form-item>
				<u-form-item label="设备编号" prop="userInfo.code" borderBottom :required="true">
					<!-- <u--input v-model="modelPatient.userInfo.code" type="number" maxlength="15" inputAlign="right"
						border="none" placeholder="请输入设备编号"></u--input> -->
						
						<input style="background-color: #ffffff;text-align: right;" placeholder-class="placeholderEE"  border="none" :disabled="true" readonly 
						  v-model="modelPatient.userInfo.code" type="number" maxlength="15" inputAlign="right"
							></input>
				</u-form-item>
				<u-form-item label="地址" prop="userInfo.address" borderBottom>
					<u--input v-model="modelPatient.userInfo.address" maxlength="20" inputAlign="right" border="none"
						placeholder="请输入地址"></u--input>
					<u-icon slot="right" name="map" color="#2979ff" size="25" @click="scanClock()"></u-icon>
				</u-form-item>
				<u-form-item label="故障描述" prop="userInfo.description" borderBottom>
					<u--input v-model="modelPatient.userInfo.description" maxlength="50" inputAlign="right"
						border="none" placeholder="请输入故障描述"></u--input>
				</u-form-item>
				<u-form-item label="故障照片" prop="userInfo.picUrl" borderBottom>
					<u-upload :fileList="fileList1" @afterRead="afterRead" @delete="deletePic" name="1" multiple
						:maxCount="3" :previewFullImage="true"></u-upload>
				</u-form-item>
			</u--form>
			<u-picker itemHeight="60" :show="showDevPicker" keyName="name" :columns="devColumns" @confirm="devConfirm"
				@cancel="devCancel"></u-picker>
			<!-- <u-datetime-picker itemHeight="60" :show="showPicker" v-model="nowDate" mode="date" closeOnClickOverlay
				@confirm="confirm" @cancel="cancel" @close="close"></u-datetime-picker> -->
				<view class="viewOne_c_a" @click="goHistoryList">
					<image class="backImg" src="@/static/homePage/listIcon.png" mode=""></image>
					<text>历史故障列表</text>
				</view>
				
			<view class="btnXFView">
				<u-button type="primary" text="提交" :disabled="disabled" :customStyle="primaryBtnCss"
					@click="submit"></u-button>
			</view>
			<u-modal title="提示" :content="content" :show="showModal" showCancelButton closeOnClickOverlay
				@confirm="modalConfirm" @cancel="modalCancel" @close="modalClose"></u-modal>
		</view>
	</view>
</template>

<script>
	import {
		devAllList,
		cstFaultAdd
	} from "@/network/api.js"
	import {
		baseURL,
		baseURL_a
	} from '@/network/base.js';
	export default {
		data() {
			return {
				content: '',
				showModal: false,
				primaryBtnCss: {
					width: '574rpx',
					height: '88rpx',
					background: '#0165FC',
					boxShadow: '0rpx 4rpx 24rpx 0rpx rgba(54,150,255,0.4)',
					borderRadius: '200rpx',
					fontSize: '28rpx',
					fontFamily: 'Microsoft YaHei UI-Regular, Microsoft YaHei UI',
					color: '#FFFFFF',
					lineHeight: '28rpx'
				},
				disabled: false,
				labelStyle: {
					// fontSize: '28rpx',
					// fontFamily: 'Microsoft YaHei UI-Regular, Microsoft YaHei UI',
					// lineHeight: '28rpx'
				},
				showDevPicker: false,
				showPicker: false,
				nowDate: Number(new Date()),
				modelPatient: {
					userInfo: {
						equipDev: '',
						equipType: '',
						code: '',
						// chroma: '',
						address: '',
						// operationTime: '',
						description: '',
						picUrl: "",
						type: ''
					},
				},
				fileList1: [],
				devColumns: [],
				patientRules: {
					'userInfo.equipDev': {
						type: 'string',
						required: true,
						message: '请选择',
						trigger: ['blur', 'change']
					},
					'userInfo.equipType': {
						type: 'string',
						required: true,
						message: '请输入',
						trigger: ['blur', 'change']
					},
					'userInfo.code': {
						type: 'string',
						required: true,
						message: '请输入',
						trigger: ['blur', 'change']
					},
					'userInfo.chroma': [{
						type: 'number',
						required: true,
						message: '请输入',
						trigger: ['blur', 'change']
					}],
					// 'userInfo.operationTime': {
					// 	type: 'string',
					// 	required: true,
					// 	message: '请选择日期',
					// 	trigger: ['blur', 'change']
					// },
					'userInfo.address': {
						type: 'string',
						required: false,
						message: '请输入',
						trigger: ['blur', 'change']
					},
					'userInfo.description': {
						type: 'string',
						required: false,
						message: '请输入',
						trigger: ['blur', 'change']
					},
					'userInfo.picUrl': {
						type: 'string',
						required: false,
						message: '请输入',
						trigger: ['blur', 'change']
					}
				}
			};
		},
		onLoad() {
			this.getDevList();
		},
		methods: {
			devConfirm(e) {
				console.log("1212", e)
				this.modelPatient.userInfo.equipDev = e.value[0].name
				this.modelPatient.userInfo.equipmentId = e.value[0].id
				this.modelPatient.userInfo = Object.assign({}, this.modelPatient.userInfo, e.value[0]);
				if(this.modelPatient.userInfo.equipType == '001')
				{
					this.modelPatient.userInfo.type = '家用型可燃气体探测器';
				}
				else if(this.modelPatient.userInfo.equipType == '002')
				{
					this.modelPatient.userInfo.type = '地下空间燃气泄漏监测仪';
				}
				else if(this.modelPatient.userInfo.equipType == '003')
				{
					this.modelPatient.userInfo.type = '工商业可燃气体探测器';
				}
				else if(this.modelPatient.userInfo.equipType == '004')
				{
					this.modelPatient.userInfo.type = '地埋式燃气泄漏监测仪';
				}
				this.$refs.patientForm.validateField('userInfo.equipDev')
				this.showDevPicker = false;
			},
			devCancel() {
				this.showDevPicker = false;
			},
			// 删除图片
			deletePic(event) {
				this[`fileList${event.name}`].splice(event.index, 1)
			},
			getDevList() {
				let query = {
					customerId: uni.getStorageSync('userId')
				}
				devAllList(query).then(res => {
						this.devColumns = [];
						this.devColumns.push(res.data);
					})
					.catch((err) => {})
			},
			goHistoryList() {
				uni.navigateTo({
					url: '../basicInform/historicalFaultList'
				})
			},
			// 新增图片
			async afterRead(event) {
				// 当设置 mutiple 为 true 时, file 为数组格式，否则为对象格式
				console.log("event", event)
				console.log("event22", this[`fileList${event.name}`])
				let lists = [].concat(event.file)
				let fileListLen = this[`fileList${event.name}`].length
				lists.map((item) => {-
					this[`fileList${event.name}`].push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				for (let i = 0; i < lists.length; i++) {
					const result = await this.uploadFilePromise(lists[i].url)
					
					let optData = JSON.parse(result);
					console.log("result", optData.data)
					let item = this[`fileList${event.name}`][fileListLen]
					this[`fileList${event.name}`].splice(fileListLen, 1, Object.assign(item, {
						status: 'success',
						message: '',
						url: optData.data
					}))
					fileListLen++
				}
			},
			uploadFilePromise(url) {
				return new Promise((resolve, reject) => {
					let a = uni.uploadFile({
						url: baseURL_a + '/file/picUpload', // 仅为示例，非真实的接口地址
						filePath: url,
						name: 'file',
						// formData: {
						// 	user: 'test'
						// },
						success: (res) => {
							console.log("iiii", res, typeof(res.data))
							setTimeout(() => {
								resolve(res.data)
							}, 1000)
						}
					});
				})
			},
			submit() {
				this.$refs.patientForm.validate().then(res => {
					// this.showModal = true;
					uni.showLoading({
						title: '请求中..'
					})
					let fileList = [];
					console.log("this.fileList1", this.fileList1)
					for (let i = 0; i < this.fileList1.length; i++) {
						let listUrl = this.fileList1[i]
						
						fileList.push(listUrl.url)
					}
					console.log('fileList', fileList)
					let form = this.modelPatient.userInfo;
					form.userId = uni.getStorageSync('userId');
					form.picUrl = fileList.join(',')
					delete form.id;
					cstFaultAdd(form).then(res => {
							uni.navigateBack({
								delta: 1 // 默认值是1，表示返回的页面层数
							});
							uni.hideLoading();
						})
						.catch((err) => {
							console.log("1212", err)
							uni.hideLoading();
						})
				}).catch(errors => {
					// uni.$u.toast('校验失败')
				})
			},
			scanClock(){
				let that = this;
				uni.showLoading({
					title: '定位中..'
				})
				uni.getLocation({
					type: 'gcj02',
					success: function (res) {
						let URL = 'https://apis.map.qq.com/ws/geocoder/v1/?location=';
						let key = 'XMCBZ-RU5CU-3SXV7-GAPV5-BKZXZ-6WBM5';
						let getAddressUrl = URL + res.latitude + ',' + res.longitude + `&key=${key}`;
						uni.request({
							url: getAddressUrl,
							success: result => {
								let Res_Data = result.data.result;
								console.log("🚀 ~ Res_Data:", Res_Data.address)
								that.modelPatient.userInfo.address = Res_Data.address;
								uni.hideLoading();
							}
						});
					}
				});
			},
			close() {
				this.showPicker = false
			},
			cancel() {
				this.showPicker = false
			},
			confirm(e) {
				this.showPicker = false
				const timeFormat = uni.$u.timeFormat;
				this.modelPatient.userInfo.operationTime = timeFormat(e.value, 'yyyy-mm-dd')
			},

			modalConfirm() {
				this.showModal = false
				console.log('confirm');
			},
			modalCancel() {
				this.showModal = false
				console.log('cancel');
			},
			modalClose() {
				this.showModal = false
				console.log('close');
			}
		},
		onReady() {
			//如果需要兼容微信小程序，并且校验规则中含有方法等，只能通过setRules方法设置规则。
			this.$refs.patientForm.setRules(this.patientRules)
		},
	};
</script>

<style lang="scss" scoped>
	.patientInform {
		padding: 0 0 100rpx;
	}
	.placeholderEE {
		
	}
	.viewOne_c_a {
		margin: 0 0 20rpx;
		display: flex;
		align-items: center;
		justify-content: flex-end;
	
		.backImg {
			width: 40rpx;
			height: 40rpx;
			margin-right: 10rpx;
		}
	
		text {
			color: #39A1FF;
			font-size: 24rpx;
		}
	}
</style>