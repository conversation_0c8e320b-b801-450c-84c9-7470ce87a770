<template>
	<!-- 报警详情 -->
	<view class="alarmDetail">
		<view class="listView">
			<view class="listModule">
				<view class="titleHead">{{dataList.name}}</view>
				<view>设备位置：{{dataList.address}}</view>
				<view>当前浓度：{{dataList.chroma}}</view>
				<view>当前温度：{{dataList.temp}}℃</view>
				<view>设备编号：{{dataList.equipCode}}</view>
				<view>上报时间：{{dataList.alarmTime}}</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		cstAlarmDetail
	} from "@/network/api.js"
	export default {
		data() {
			return {
				dataList: '', //数据
				total: null, //总条数
				pageNum: 1, //第几页
				pageSize: 15 //显示多少条
			};
		},
		onLoad(opt) { // 初始化
			this.init(opt)
		},
		methods: {
			init(opt) { // 初始化数据
				this.getList(opt.equipmentId);
			},
			getList(id) { //获取数据
				let params = {
					alarmId: id
				}
				cstAlarmDetail(params).then(res => {
				
					this.dataList = res.data;
					console.log(this.dataList)
					
					this.total = res.total
				})
			},
			goFaultDetail() {
				// uni.navigateTo({
				// 	url: './faultDetails'
				// })
			}
		},
		onReachBottom() { //触底事件
			if (this.pageNum * this.pageSize >= this.total) {
				uni.showToast({
					title: '没有更多数据了',
					icon: 'none',
					duration: 1000
				});
			} else {
				if (this.pageNum <= this.pageNum - 1) {
					setTimeout(() => {
						uni.hideLoading()
					}, 500)
				} else {
					uni.showLoading({
						title: '加载中'
					});
					this.pageNum++
					this.getList()
				}
				setTimeout(() => {
					uni.hideLoading()
				}, 500)
			}
		},
		onReady() {},
	};
</script>
<style>
	page {
		background-color: #F6F6F6;
	}
</style>
<style lang="scss" scoped>
	.alarmDetail {
		.listView {
			margin-top: 10rpx;

			.listModule {
				background-color: #ffffff;
				margin-bottom: 12rpx;
				padding: 26rpx 60rpx;
				font-size: 24rpx;
				color: #3D3D3D;
				line-height: 48rpx;

				.titleHead {
					font-size: 34rpx;
					color: #FF0000;
					line-height: 56rpx;
					margin-bottom: 6rpx;
				}
			}
		}
	}
</style>