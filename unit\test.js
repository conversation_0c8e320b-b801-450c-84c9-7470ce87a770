/**
 * 验证电子邮箱格式
 */
function email(value) {
	return /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/.test(value);
}

/**
 * 验证手机格式
 */
function mobile(value) {
	return /^1[3-9]\d{9}$/.test(value)
}

/**
 * 验证URL格式
 */
function url(value) {
	return /http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w-.\/?%&=]*)?/.test(value)
}

/**
 * 验证日期格式
 */
function date(value) {
	return !/Invalid|NaN/.test(new Date(value).toString())
}

/**
 * 验证ISO类型的日期格式
 */
function dateISO(value) {
	return /^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])$/.test(value)
}

/**
 * 验证十进制数字
 */
function number(value) {
	return /^(?:-?\d+|-?\d{1,3}(?:,\d{3})+)?(?:\.\d+)?$/.test(value)
}

/**
 * 验证整数
 */
function digits(value) {
	return /^\d+$/.test(value)
}

/**
 * 验证身份证号码
 */
function idCard(value) {
	return /^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/.test(
		value)
}

/**
 * 是否车牌号
 */
function carNo(value) {
	// 新能源车牌
	const xreg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF]$)|([DF][A-HJ-NP-Z0-9][0-9]{4}$))/;
	// 旧车牌
	const creg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/;
	if (value.length === 7) {
		return creg.test(value);
	} else if (value.length === 8) {
		return xreg.test(value);
	} else {
		return false;
	}
}

/**
 * 金额,只允许2位小数
 */
function amount(value) {
	//金额，只允许保留两位小数
	return /^[1-9]\d*(,\d{3})*(\.\d{1,2})?$|^0\.\d{1,2}$/.test(value);
}

/**
 * 中文
 */
function chinese(value) {
	let reg = /^[\u4e00-\u9fa5]+$/gi;
	return reg.test(value);
}

/**
 * 只能输入字母
 */
function letter(value) {
	return /^[a-zA-Z]*$/.test(value);
}

/**
 * 只能是字母或者数字
 */
function enOrNum(value) {
	//英文或者数字
	let reg = /^[0-9a-zA-Z]*$/g;
	return reg.test(value);
}

/**
 * 验证是否包含某个值
 */
function contains(value, param) {
	return value.indexOf(param) >= 0
}

/**
 * 验证一个值范围[min, max]
 */
function range(value, param) {
	return value >= param[0] && value <= param[1]
}

/**
 * 验证一个长度范围[min, max]
 */
function rangeLength(value, param) {
	return value.length >= param[0] && value.length <= param[1]
}

/**
 * 是否固定电话
 */
function landline(value) {
	let reg = /^\d{3,4}-\d{7,8}(-\d{3,4})?$/;
	return reg.test(value);
}

/**
 * 判断是否为空
 */
function empty(value) {
	switch (typeof value) {
		case 'undefined':
			return true;
		case 'string':
			if (value.replace(/(^[ \t\n\r]*)|([ \t\n\r]*$)/g, '').length == 0) return true;
			break;
		case 'boolean':
			if (!value) return true;
			break;
		case 'number':
			if (0 === value || isNaN(value)) return true;
			break;
		case 'object':
			if (null === value || value.length === 0) return true;
			for (var i in value) {
				return false;
			}
			return true;
	}
	return false;
}

/**
 * 是否json字符串
 */
function jsonString(value) {
	if (typeof value == 'string') {
		try {
			var obj = JSON.parse(value);
			if (typeof obj == 'object' && obj) {
				return true;
			} else {
				return false;
			}
		} catch (e) {
			return false;
		}
	}
	return false;
}


/**
 * 是否数组
 */
function array(value) {
	if (typeof Array.isArray === "function") {
		return Array.isArray(value);
	} else {
		return Object.prototype.toString.call(value) === "[object Array]";
	}
}

/**
 * 是否对象
 */
function object(value) {
	return Object.prototype.toString.call(value) === '[object Object]';
}

/**
 * 是否短信验证码
 */
function code(value, len = 6) {
	return new RegExp(`^\\d{${len}}$`).test(value);
}

/***
 * 去掉字符串中的特殊字符（校验特殊字符）
 */
function excludeSpecial(value) {
	// 去掉转义字符
	value = value.replace(/[\'\"\\\/\b\f\n\r\t]/g, '');
	// 去掉特殊字符
	value = value.replace(/[\@\#\￥\$\%\^\&\*\(\)\{\}\:\"\<\>\?\[\]\~\`\,\:\、\|\=\+\-\.]/g, '');
	// 去掉中文特殊字符
	value = value.replace(/[\…\：\“\”\《\》\？\。\，\/\‘\；\】\【]/g, '');
	// 去掉空格
	value = value.replace(/[ ]/g, '');
	return value;
}

function appNoneTop(){ 
	const ua = window.navigator.userAgent.toLowerCase();
	if (ua.match(/MicroMessenger/i) == 'micromessenger') {
		let appPage = document.querySelector('uni-page-head');
		appPage.style.display = "none"
	}
}
function cityjson(){ 
	  let city =  [
						{
							"label": "北京",
							"value": "京"
						}, {
							"label": "天津",
							"value": "津"
						}, {
							"label": "上海",
							"value": "沪"
						}, {
							"children": [{
								"label": "石家庄",
								"value": "冀A"
							}, {
								"label": "唐山",
								"value": "冀B"
							}, {
								"label": "秦皇岛",
								"value": "冀C"
							}, {
								"label": "邯郸",
								"value": "冀D"
							}, {
								"label": "邢台",
								"value": "冀E"
							}, {
								"label": "保定",
								"value": "冀F"
							}, {
								"label": "张家口",
								"value": "冀G"
							}, {
								"label": "承德",
								"value": "冀H"
							}, {
								"label": "沧州",
								"value": "冀J"
							}, {
								"label": "廊坊",
								"value": "冀R"
							}, {
								"label": "沧州",
								"value": "冀S"
							}, {
								"label": "衡水",
								"value": "冀T"
							}],
							"label": "河北",
							"value": "冀"
						}, {
							"children": [{
								"label": "济南",
								"value": "鲁A "
							}, {
								"label": "青岛",
								"value": "鲁B"
							}, {
								"label": "淄博",
								"value": "鲁C"
							}, {
								"label": "枣庄",
								"value": "鲁D"
							}, {
								"label": "东营",
								"value": "鲁E"
							}, {
								"label": "烟台",
								"value": "鲁F"
							}, {
								"label": "潍坊",
								"value": "鲁G"
							}, {
								"label": "济宁",
								"value": "鲁H"
							}, {
								"label": "泰安",
								"value": "鲁J"
							}, {
								"label": "威海",
								"value": "鲁K"
							}, {
								"label": "日照",
								"value": "鲁L"
							}, {
								"label": "滨州",
								"value": "鲁M"
							}, {
								"label": "德州",
								"value": "鲁N"
							}, {
								"label": "聊城",
								"value": "鲁P"
							}, {
								"label": "临沂",
								"value": "鲁Q"
							}, {
								"label": "菏泽",
								"value": "鲁R"
							}, {
								"label": "莱芜",
								"value": "鲁S"
							}, {
								"label": "青岛增补",
								"value": "鲁U"
							}, {
								"label": "潍坊增补",
								"value": "鲁V"
							}, {
								"label": "烟台",
								"value": "鲁Y"
							}],
							"label": "山东",
							"value": "鲁"
						}, {
							"children": [{
								"label": "郑州",
								"value": "豫A"
							}, {
								"label": "开封",
								"value": "豫B"
							}, {
								"label": "洛阳",
								"value": "豫C"
							}, {
								"label": "平顶山",
								"value": "豫D"
							}, {
								"label": "安阳",
								"value": "豫E"
							}, {
								"label": "鹤壁",
								"value": "豫F"
							}, {
								"label": "新乡",
								"value": "豫G"
							}, {
								"label": "焦作",
								"value": "豫H"
							}, {
								"label": "濮阳",
								"value": "豫J"
							}, {
								"label": "许昌",
								"value": "豫K"
							}, {
								"label": "漯河",
								"value": "豫L"
							}, {
								"label": "三门峡",
								"value": "豫M"
							}, {
								"label": "商丘",
								"value": "豫N"
							}, {
								"label": "周口",
								"value": "豫P"
							}, {
								"label": "驻马店",
								"value": "豫Q"
							}, {
								"label": "南阳",
								"value": "豫R"
							}, {
								"label": "信阳",
								"value": "豫S"
							}, {
								"label": "济源",
								"value": "豫U"
							}],
							"label": "河南",
							"value": "豫"
						}, {
							"children": [{
								"label": "福州",
								"value": "闽A"
							}, {
								"label": "莆田",
								"value": "闽B"
							}, {
								"label": "泉州",
								"value": "闽C"
							}, {
								"label": "厦门",
								"value": "闽D"
							}, {
								"label": "漳州",
								"value": "闽E"
							}, {
								"label": "龙岩",
								"value": "闽F"
							}, {
								"label": "三明",
								"value": "闽G"
							}, {
								"label": "南平",
								"value": "闽H"
							}, {
								"label": "宁德",
								"value": "闽J"
							}, {
								"label": "直系统",
								"value": "闽K"
							}],
							"label": "福建",
							"value": "闽"
						}, {
							"children": [{
								"label": "重庆区（江南）",
								"value": "渝A"
							}, {
								"label": "重庆区（江北）",
								"value": "渝B"
							}, {
								"label": "永川区",
								"value": "渝C"
							}, {
								"label": "万州区",
								"value": "渝F"
							}, {
								"label": "涪陵区",
								"value": "渝G"
							}, {
								"label": "黔江区",
								"value": "渝H"
							}],
							"label": "重庆",
							"value": "渝"
						}, {
							"children": [{
								"label": "武汉",
								"value": "鄂A"
							}, {
								"label": "黄石",
								"value": "鄂B"
							}, {
								"label": "十堰",
								"value": "鄂C"
							}, {
								"label": "荆州",
								"value": "鄂D"
							}, {
								"label": "宜昌",
								"value": "鄂E"
							}, {
								"label": "襄樊",
								"value": "鄂F"
							}, {
								"label": "鄂州",
								"value": "鄂G"
							}, {
								"label": "荆门 ",
								"value": "鄂H"
							}, {
								"label": "黄冈",
								"value": "鄂J"
							}, {
								"label": "孝感",
								"value": "鄂K"
							}, {
								"label": "咸宁",
								"value": "鄂L"
							}, {
								"label": "仙桃",
								"value": "鄂M"
							}, {
								"label": "潜江",
								"value": "鄂N"
							}, {
								"label": "神农架林区",
								"value": "鄂P"
							}, {
								"label": "恩施土家族苗族自治州",
								"value": "鄂Q"
							}, {
								"label": "天门",
								"value": "鄂R"
							}, {
								"label": "随州",
								"value": "鄂S"
							}],
							"label": "湖北",
							"value": "鄂"
						}, {
							"children": [{
								"label": "长沙",
								"value": "湘A "
							}, {
								"label": "株洲",
								"value": "湘B"
							}, {
								"label": "湘潭",
								"value": "湘C"
							}, {
								"label": "衡阳",
								"value": "湘D"
							}, {
								"label": "邵阳",
								"value": "湘E"
							}, {
								"label": "岳阳",
								"value": "湘F"
							}, {
								"label": "张家界",
								"value": "湘G"
							}, {
								"label": "益阳",
								"value": "湘H"
							}, {
								"label": "常德",
								"value": "湘J"
							}, {
								"label": "娄底",
								"value": "湘K"
							}, {
								"label": "郴州",
								"value": "湘L"
							}, {
								"label": "永州",
								"value": "湘M"
							}, {
								"label": "怀化",
								"value": "湘N"
							}, {
								"label": "湘西土家族苗族自治州",
								"value": "湘U"
							}],
							"label": "湖南",
							"value": "湘"
						}, {
							"children": [{
								"label": "南昌",
								"value": "赣A"
							}, {
								"label": "赣州",
								"value": "赣B"
							}, {
								"label": "宜春",
								"value": "赣C"
							}, {
								"label": "吉安",
								"value": "赣D"
							}, {
								"label": "上饶",
								"value": "赣E"
							}, {
								"label": "抚州",
								"value": "赣F"
							}, {
								"label": "九江",
								"value": "赣G"
							}, {
								"label": "景德镇",
								"value": "赣H"
							}, {
								"label": "萍乡",
								"value": "赣J"
							}, {
								"label": "新余",
								"value": "赣K"
							}, {
								"label": "鹰潭",
								"value": "赣L"
							}, {
								"label": "南昌,直系统",
								"value": "赣M"
							}],
							"label": "江西",
							"value": "赣"
						}, {
							"children": [{
								"label": "海口",
								"value": "琼A"
							}, {
								"label": "三亚",
								"value": "琼B"
							}, {
								"label": "琼海",
								"value": "琼C"
							}, {
								"label": "五指山",
								"value": "琼D"
							}, {
								"label": "洋浦开发区",
								"value": "琼E"
							}],
							"label": "海南",
							"value": "琼"
						}, {
							"children": [{
								"label": "哈尔滨",
								"value": "黑A"
							}, {
								"label": "齐齐哈尔",
								"value": "黑B"
							}, {
								"label": "牡丹江",
								"value": "黑C"
							}, {
								"label": "佳木斯",
								"value": "黑D"
							}, {
								"label": "大庆",
								"value": "黑E"
							}, {
								"label": "伊春",
								"value": "黑F"
							}, {
								"label": "鸡西",
								"value": "黑G"
							}, {
								"label": "鹤岗",
								"value": "黑H"
							}, {
								"label": "双鸭山",
								"value": "黑J"
							}, {
								"label": "七台河",
								"value": "黑K"
							}, {
								"label": "松花江地区",
								"value": "黑L"
							}, {
								"label": "绥化",
								"value": "黑M"
							}, {
								"label": "黑河",
								"value": "黑N"
							}, {
								"label": "大兴安岭地区",
								"value": "黑P"
							}, {
								"label": "农垦系统",
								"value": "黑R"
							}],
							"label": "黑龙江",
							"value": "黑"
						}, {
							"children": [{
								"label": "西安",
								"value": "陕A"
							}, {
								"label": "铜川",
								"value": "陕B"
							}, {
								"label": "宝鸡",
								"value": "陕C"
							}, {
								"label": "咸阳",
								"value": "陕D"
							}, {
								"label": "渭南",
								"value": "陕E"
							}, {
								"label": "汉中",
								"value": "陕F"
							}, {
								"label": "安康",
								"value": "陕G"
							}, {
								"label": "商洛",
								"value": "陕H"
							}, {
								"label": "延安",
								"value": "陕J"
							}, {
								"label": "榆林",
								"value": "陕K"
							}, {
								"label": "杨凌高新农业示范区",
								"value": "陕V"
							}],
							"label": "陕西",
							"value": "陕"
						}, {
							"children": [{
								"label": "贵阳",
								"value": "贵A"
							}, {
								"label": "六盘水",
								"value": "贵B"
							}, {
								"label": "遵义",
								"value": "贵C"
							}, {
								"label": "铜仁",
								"value": "贵D"
							}, {
								"label": "黔西南布依族苗族自治州",
								"value": "贵E"
							}, {
								"label": "毕节",
								"value": "贵F"
							}, {
								"label": "安顺",
								"value": "贵G"
							}, {
								"label": "黔东南苗族侗族自治州",
								"value": "贵H"
							}, {
								"label": "黔南布依族苗族自治州",
								"value": "贵J"
							}],
							"label": "贵州",
							"value": "贵"
						}, {
							"children": [{
								"label": "乌鲁木齐",
								"value": "新A"
							}, {
								"label": "昌吉回族自治州",
								"value": "新B"
							}, {
								"label": "石河子",
								"value": "新C"
							}, {
								"label": "奎屯",
								"value": "新D"
							}, {
								"label": "博尔塔拉蒙古自治州",
								"value": "新E"
							}, {
								"label": "伊犁哈萨克自治州直辖县",
								"value": "新F"
							}, {
								"label": "塔城",
								"value": "新G"
							}, {
								"label": "阿勒泰",
								"value": "新H"
							}, {
								"label": "克拉玛依",
								"value": "新J"
							}, {
								"label": "吐鲁番",
								"value": "新K"
							}, {
								"label": "哈密",
								"value": "新L"
							}, {
								"label": "巴音郭愣蒙古自治州",
								"value": "新M"
							}, {
								"label": "阿克苏",
								"value": "新N"
							}, {
								"label": "克孜勒苏柯尔克孜自治州",
								"value": "新P"
							}, {
								"label": "喀什",
								"value": "新Q"
							}, {
								"label": "和田",
								"value": "新R"
							}],
							"label": "新疆",
							"value": "新"
						}, {
							"children": [{
								"label": "南京",
								"value": "苏A"
							}, {
								"label": "无锡",
								"value": "苏B"
							}, {
								"label": "徐州",
								"value": "苏C"
							}, {
								"label": "常州",
								"value": "苏D"
							}, {
								"label": "苏州",
								"value": "苏E"
							}, {
								"label": "南通",
								"value": "苏F"
							}, {
								"label": "连云港",
								"value": "苏G"
							}, {
								"label": "淮安",
								"value": "苏H"
							}, {
								"label": "盐城",
								"value": "苏J"
							}, {
								"label": "扬州",
								"value": "苏K"
							}, {
								"label": "镇江",
								"value": "苏L"
							}, {
								"label": "泰州",
								"value": "苏M"
							}, {
								"label": "宿迁",
								"value": "苏N"
							}],
							"label": "江苏",
							"value": "苏"
						}, {
							"children": [{
								"label": "合肥",
								"value": "皖A"
							}, {
								"label": "芜湖",
								"value": "皖B"
							}, {
								"label": "蚌埠",
								"value": "皖C"
							}, {
								"label": "淮南",
								"value": "皖D"
							}, {
								"label": "马鞍山",
								"value": "皖E"
							}, {
								"label": "淮北",
								"value": "皖F"
							}, {
								"label": "铜陵",
								"value": "皖G"
							}, {
								"label": "安庆",
								"value": "皖H"
							}, {
								"label": "黄山",
								"value": "皖J"
							}, {
								"label": "阜阳",
								"value": "皖K"
							}, {
								"label": "宿州",
								"value": "皖L"
							}, {
								"label": "滁州",
								"value": "皖M"
							}, {
								"label": "六安",
								"value": "皖N"
							}, {
								"label": "宣城",
								"value": "皖P"
							}, {
								"label": "巢湖",
								"value": "皖Q"
							}, {
								"label": "池州",
								"value": "皖R"
							}, {
								"label": "亳州",
								"value": "皖S"
							}],
							"label": "安徽",
							"value": "皖"
						}, {
							"children": [{
								"label": "拉萨",
								"value": "藏A"
							}, {
								"label": "昌都地区",
								"value": "藏B"
							}, {
								"label": "山南地区",
								"value": "藏C"
							}, {
								"label": "日喀则地区",
								"value": "藏D"
							}, {
								"label": "那曲地区",
								"value": "藏E"
							}, {
								"label": "阿里地区",
								"value": "藏F"
							}, {
								"label": "林芝地区",
								"value": "藏G"
							}, {
								"label": "天全县车辆管理所",
								"value": "藏H"
							}, {
								"label": "格尔木车辆管理所",
								"value": "藏J"
							}],
							"label": "西藏",
							"value": "藏"
						}, {
							"children": [{
								"label": "长春",
								"value": "吉A"
							}, {
								"label": "吉林",
								"value": "吉B"
							}, {
								"label": "四平",
								"value": "吉C"
							}, {
								"label": "辽源",
								"value": "吉D"
							}, {
								"label": "通化",
								"value": "吉E"
							}, {
								"label": "白山",
								"value": "吉F"
							}, {
								"label": "白城",
								"value": "吉G"
							}, {
								"label": "延边朝鲜族自治州",
								"value": "吉H"
							}, {
								"label": "松原",
								"value": "吉J"
							}, {
								"label": "长白山",
								"value": "吉K"
							}],
							"label": "吉林",
							"value": "吉"
						}, {
							"children": [{
								"label": "太原",
								"value": "晋A"
							}, {
								"label": "大同",
								"value": "晋B"
							}, {
								"label": "阳泉",
								"value": "晋C"
							}, {
								"label": "长治",
								"value": "晋D"
							}, {
								"label": "晋城",
								"value": "晋E"
							}, {
								"label": "朔州",
								"value": "晋F"
							}, {
								"label": "忻州",
								"value": "晋H"
							}, {
								"label": "吕梁地区",
								"value": "晋J"
							}, {
								"label": "晋中",
								"value": "晋K"
							}, {
								"label": "临汾",
								"value": "晋L"
							}, {
								"label": "运城",
								"value": "晋M"
							}],
							"label": "山西",
							"value": "晋"
						}, {
							"children": [{
								"label": "银川",
								"value": "宁A"
							}, {
								"label": "石嘴山",
								"value": "宁B"
							}, {
								"label": "银南",
								"value": "宁C"
							}, {
								"label": "固原",
								"value": "宁D"
							}, {
								"label": "中卫",
								"value": "宁E"
							}],
							"label": "宁夏",
							"value": "宁"
						}, {
							"children": [{
								"label": "兰州",
								"value": "甘A"
							}, {
								"label": "嘉峪关",
								"value": "甘B"
							}, {
								"label": "金昌",
								"value": "甘C"
							}, {
								"label": "白银",
								"value": "甘D"
							}, {
								"label": "天水",
								"value": "甘E"
							}, {
								"label": "酒泉",
								"value": "甘F"
							}, {
								"label": "张掖",
								"value": "甘G"
							}, {
								"label": "武威",
								"value": "甘H"
							}, {
								"label": "定西",
								"value": "甘J"
							}, {
								"label": "陇南",
								"value": "甘K"
							}, {
								"label": "平凉",
								"value": "甘L"
							}, {
								"label": "庆阳",
								"value": "甘M"
							}, {
								"label": "临夏回族自治州",
								"value": "甘N"
							}, {
								"label": "甘南藏族自治州",
								"value": "甘P"
							}],
							"label": "甘肃",
							"value": "甘"
						}, {
							"children": [{
								"label": "成都",
								"value": "川A"
							}, {
								"label": "绵阳",
								"value": "川B"
							}, {
								"label": "自贡",
								"value": "川C"
							}, {
								"label": "攀枝花",
								"value": "川D"
							}, {
								"label": "泸州",
								"value": "川E"
							}, {
								"label": "德阳",
								"value": "川F"
							}, {
								"label": "广元",
								"value": "川H"
							}, {
								"label": "遂宁",
								"value": "川J"
							}, {
								"label": "内江",
								"value": "川K"
							}, {
								"label": "乐山",
								"value": "川L"
							}, {
								"label": "资阳",
								"value": "川M"
							}, {
								"label": "宜宾",
								"value": "川Q"
							}, {
								"label": "南充",
								"value": "川R"
							}, {
								"label": "达州",
								"value": "川S"
							}, {
								"label": "雅安",
								"value": "川T"
							}, {
								"label": "阿坝藏族羌族自治州",
								"value": "川U"
							}, {
								"label": "甘孜藏族自治州",
								"value": "川V"
							}, {
								"label": "凉山彝族自治州",
								"value": "川W"
							}, {
								"label": "广安",
								"value": "川X"
							}, {
								"label": "巴中",
								"value": "川Y"
							}, {
								"label": "眉山",
								"value": "川Z"
							}],
							"label": "四川",
							"value": "川"
						}, {
							"children": [{
								"label": "杭州",
								"value": "浙A"
							}, {
								"label": "宁波",
								"value": "浙B"
							}, {
								"label": "温州",
								"value": "浙C"
							}, {
								"label": "绍兴",
								"value": "浙D"
							}, {
								"label": "湖州",
								"value": "浙E"
							}, {
								"label": "嘉兴",
								"value": "浙F"
							}, {
								"label": "金华",
								"value": "浙G"
							}, {
								"label": "衢州",
								"value": "浙H"
							}, {
								"label": "台州",
								"value": "浙J"
							}, {
								"label": "丽水",
								"value": "浙K"
							}, {
								"label": "舟山",
								"value": "浙L"
							}],
							"label": "浙江",
							"value": "浙"
						}, {
							"children": [{
								"label": "南宁",
								"value": "桂A"
							}, {
								"label": "柳州",
								"value": "桂B"
							}, {
								"label": "桂林",
								"value": "桂C"
							}, {
								"label": "梧州",
								"value": "桂D"
							}, {
								"label": "北海",
								"value": "桂E"
							}, {
								"label": "崇左",
								"value": "桂F"
							}, {
								"label": "来宾",
								"value": "桂G"
							}, {
								"label": "桂林地区",
								"value": "桂H"
							}, {
								"label": "贺州",
								"value": "桂J"
							}, {
								"label": "玉林",
								"value": "桂K"
							}, {
								"label": "百色",
								"value": "桂L"
							}, {
								"label": "河池",
								"value": "桂M"
							}, {
								"label": "钦州",
								"value": "桂N"
							}, {
								"label": "防城港",
								"value": "桂P"
							}, {
								"label": "贵港",
								"value": "桂R"
							}],
							"label": "广西",
							"value": "桂"
						}, {
							"children": [{
								"label": "昆明",
								"value": "云A"
							}, {
								"label": "东川",
								"value": "云A-V"
							}, {
								"label": "昭通",
								"value": "云C"
							}, {
								"label": "曲靖",
								"value": "云D"
							}, {
								"label": "楚雄彝族自治州",
								"value": "云E"
							}, {
								"label": "玉溪",
								"value": "云F"
							}, {
								"label": "红河哈尼族彝族自治州",
								"value": "云G"
							}, {
								"label": "文山壮族苗族自治州",
								"value": "云H"
							}, {
								"label": "思茅",
								"value": "云J"
							}, {
								"label": "西双版纳傣族自治州",
								"value": "云K"
							}, {
								"label": "大理白族自治州",
								"value": "云L"
							}, {
								"label": "保山",
								"value": "云M"
							}, {
								"label": "德宏傣族景颇族自治州",
								"value": "云N"
							}, {
								"label": "丽江",
								"value": "云P"
							}, {
								"label": "怒江傈僳族自治州",
								"value": "云Q"
							}, {
								"label": "迪庆藏族自治州",
								"value": "云R"
							}, {
								"label": "临沧地区",
								"value": "云S"
							}],
							"label": "云南",
							"value": "云"
						}, {
							"children": [{
								"label": "呼和浩特",
								"value": "蒙A"
							}, {
								"label": "包头",
								"value": "蒙B"
							}, {
								"label": "乌海",
								"value": "蒙C"
							}, {
								"label": "赤峰",
								"value": "蒙D"
							}, {
								"label": "呼伦贝尔 ",
								"value": "蒙E"
							}, {
								"label": "兴安盟",
								"value": "蒙F"
							}, {
								"label": "通辽",
								"value": "蒙G"
							}, {
								"label": "锡林郭勒盟",
								"value": "蒙H"
							}, {
								"label": "乌兰察布盟",
								"value": "蒙J"
							}, {
								"label": "鄂尔多斯 ",
								"value": "蒙K"
							}, {
								"label": "巴彦淖尔盟",
								"value": "蒙L"
							}, {
								"label": "　阿拉善盟",
								"value": "蒙M　"
							}],
							"label": "内蒙古",
							"value": "蒙"
						}, {
							"children": [{
								"label": "沈阳",
								"value": "辽A"
							}, {
								"label": "大连",
								"value": "辽B"
							}, {
								"label": "鞍山",
								"value": "辽C"
							}, {
								"label": "抚顺",
								"value": "辽D"
							}, {
								"label": "本溪",
								"value": "辽E"
							}, {
								"label": "丹东",
								"value": "辽F"
							}, {
								"label": "锦州",
								"value": "辽G"
							}, {
								"label": "营口",
								"value": "辽H"
							}, {
								"label": "阜新",
								"value": "辽J"
							}, {
								"label": "辽阳",
								"value": "辽K"
							}, {
								"label": "盘锦",
								"value": "辽L"
							}, {
								"label": "铁岭",
								"value": "辽M"
							}, {
								"label": "朝阳",
								"value": "辽N"
							}, {
								"label": "葫芦岛",
								"value": "辽P"
							}],
							"label": "辽宁",
							"value": "辽"
						}, {
							"children": [{
								"label": "广州",
								"value": "粤A"
							}, {
								"label": "深圳",
								"value": "粤B"
							}, {
								"label": "珠海",
								"value": "粤C"
							}, {
								"label": "汕头",
								"value": "粤D"
							}, {
								"label": "佛山",
								"value": "粤E"
							}, {
								"label": "韶关",
								"value": "粤F"
							}, {
								"label": "湛江",
								"value": "粤G"
							}, {
								"label": "肇庆",
								"value": "粤H"
							}, {
								"label": "江门",
								"value": "粤J"
							}, {
								"label": "茂名",
								"value": "粤K"
							}, {
								"label": "惠州",
								"value": "粤L"
							}, {
								"label": "梅州",
								"value": "粤M"
							}, {
								"label": "汕尾",
								"value": "粤N"
							}, {
								"label": "河源",
								"value": "粤P"
							}, {
								"label": "阳江",
								"value": "粤Q"
							}, {
								"label": "清远",
								"value": "粤R"
							}, {
								"label": "东莞",
								"value": "粤S"
							}, {
								"label": "中山",
								"value": "粤T"
							}, {
								"label": "潮州",
								"value": "粤U"
							}, {
								"label": "揭阳",
								"value": "粤V"
							}, {
								"label": "云浮",
								"value": "粤W"
							}, {
								"label": "顺德区",
								"value": "粤X"
							}, {
								"label": "南海区",
								"value": "粤Y"
							}, {
								"label": "港澳进入内地车辆",
								"value": "粤Z"
							}],
							"label": "广东",
							"value": "粤"
						}, {
							"children": [{
								"label": "西宁",
								"value": "青A"
							}, {
								"label": "海东",
								"value": "青B"
							}, {
								"label": "海北藏族自治州",
								"value": "青C"
							}, {
								"label": "黄南藏族自治州",
								"value": "青D"
							}, {
								"label": "藏族自治州",
								"value": "青E"
							}, {
								"label": "果洛藏族自治州",
								"value": "青F"
							}, {
								"label": "玉树藏族自治州",
								"value": "青G"
							}, {
								"label": "海西蒙古族藏族自治州",
								"value": "青H"
							}],
							"label": "青海",
							"value": "青"
						}
		 ]
		 return city
}
export default {
	email,
	mobile,
	url,
	date,
	dateISO,
	number,
	digits,
	idCard,
	carNo,
	amount,
	chinese,
	letter,
	enOrNum,
	contains,
	range,
	rangeLength,
	empty,
	isEmpty: empty,
	jsonString,
	landline,
	object,
	array,
	code,
	excludeSpecial,
	appNoneTop,
	cityjson
}
