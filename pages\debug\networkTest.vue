<template>
  <view class="network-test">
    <view class="header">
      <text class="title">网络连接测试</text>
    </view>
    
    <view class="test-section">
      <view class="test-item">
        <text class="test-label">网络状态:</text>
        <text class="test-value" :class="networkStatus.class">{{ networkStatus.text }}</text>
      </view>
      
      <view class="test-item">
        <text class="test-label">API服务器:</text>
        <text class="test-value" :class="apiStatus.class">{{ apiStatus.text }}</text>
      </view>
      
      <view class="test-item">
        <text class="test-label">当前环境:</text>
        <text class="test-value">{{ currentEnv }}</text>
      </view>
    </view>
    
    <view class="button-section">
      <button class="test-btn" @click="runTests" :disabled="testing">
        {{ testing ? '测试中...' : '开始测试' }}
      </button>
      
      <button class="switch-btn" @click="switchEnvironment">
        切换到{{ nextEnv }}环境
      </button>
    </view>
    
    <view class="log-section">
      <text class="log-title">测试日志:</text>
      <scroll-view class="log-content" scroll-y>
        <view v-for="(log, index) in logs" :key="index" class="log-item">
          <text class="log-time">{{ log.time }}</text>
          <text class="log-text" :class="log.type">{{ log.message }}</text>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script>
import { checkNetworkStatus, testAPI } from '@/utils/debug.js';
import { baseURL_a } from '@/network/base.js';

export default {
  data() {
    return {
      testing: false,
      networkStatus: { text: '未知', class: 'unknown' },
      apiStatus: { text: '未测试', class: 'unknown' },
      currentEnv: '正式环境',
      nextEnv: '测试环境',
      logs: []
    }
  },
  
  onLoad() {
    this.addLog('页面加载完成', 'info');
    this.checkCurrentEnv();
  },
  
  methods: {
    addLog(message, type = 'info') {
      const now = new Date();
      const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
      this.logs.unshift({ time, message, type });
      if (this.logs.length > 50) {
        this.logs.pop();
      }
    },
    
    checkCurrentEnv() {
      if (baseURL_a.includes('seefyiot.com')) {
        this.currentEnv = '正式环境';
        this.nextEnv = '测试环境';
      } else if (baseURL_a.includes('ahhsiot.com')) {
        this.currentEnv = '测试环境';
        this.nextEnv = '正式环境';
      } else {
        this.currentEnv = '开发环境';
        this.nextEnv = '正式环境';
      }
    },
    
    async runTests() {
      this.testing = true;
      this.addLog('开始网络测试...', 'info');
      
      // 测试网络状态
      try {
        const hasNetwork = await checkNetworkStatus();
        if (hasNetwork) {
          this.networkStatus = { text: '正常', class: 'success' };
          this.addLog('网络连接正常', 'success');
        } else {
          this.networkStatus = { text: '无网络', class: 'error' };
          this.addLog('网络连接异常', 'error');
        }
      } catch (error) {
        this.networkStatus = { text: '检测失败', class: 'error' };
        this.addLog('网络状态检测失败', 'error');
      }
      
      // 测试API服务器
      try {
        this.addLog(`测试API服务器: ${baseURL_a}`, 'info');
        const apiOk = await testAPI(baseURL_a + '/health');
        if (apiOk) {
          this.apiStatus = { text: '正常', class: 'success' };
          this.addLog('API服务器连接正常', 'success');
        } else {
          this.apiStatus = { text: '连接失败', class: 'error' };
          this.addLog('API服务器连接失败', 'error');
        }
      } catch (error) {
        this.apiStatus = { text: '测试失败', class: 'error' };
        this.addLog(`API测试失败: ${error.message}`, 'error');
      }
      
      this.testing = false;
      this.addLog('网络测试完成', 'info');
    },
    
    switchEnvironment() {
      uni.showModal({
        title: '切换环境',
        content: `确定要切换到${this.nextEnv}吗？`,
        success: (res) => {
          if (res.confirm) {
            this.addLog(`切换到${this.nextEnv}`, 'info');
            // 这里需要修改base.js文件中的配置
            uni.showToast({
              title: '请手动修改base.js配置',
              icon: 'none'
            });
          }
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.network-test {
  padding: 20rpx;
  
  .header {
    text-align: center;
    margin-bottom: 40rpx;
    
    .title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
    }
  }
  
  .test-section {
    background: #fff;
    border-radius: 10rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;
    
    .test-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20rpx;
      
      .test-label {
        font-size: 28rpx;
        color: #666;
      }
      
      .test-value {
        font-size: 28rpx;
        font-weight: bold;
        
        &.success { color: #52c41a; }
        &.error { color: #ff4d4f; }
        &.unknown { color: #999; }
      }
    }
  }
  
  .button-section {
    display: flex;
    gap: 20rpx;
    margin-bottom: 30rpx;
    
    .test-btn, .switch-btn {
      flex: 1;
      height: 80rpx;
      border-radius: 10rpx;
      font-size: 28rpx;
    }
    
    .test-btn {
      background: #1890ff;
      color: white;
    }
    
    .switch-btn {
      background: #52c41a;
      color: white;
    }
  }
  
  .log-section {
    background: #fff;
    border-radius: 10rpx;
    padding: 30rpx;
    
    .log-title {
      font-size: 28rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 20rpx;
    }
    
    .log-content {
      height: 400rpx;
      
      .log-item {
        display: flex;
        margin-bottom: 10rpx;
        
        .log-time {
          font-size: 24rpx;
          color: #999;
          margin-right: 20rpx;
          min-width: 120rpx;
        }
        
        .log-text {
          font-size: 24rpx;
          flex: 1;
          
          &.success { color: #52c41a; }
          &.error { color: #ff4d4f; }
          &.info { color: #333; }
        }
      }
    }
  }
}
</style>
