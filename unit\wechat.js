/*
    微信(公众号)支付方法
*/
const wx = require('jweixin-module');
 
// 微信支付
const wexinPay = (data, callback, errorCallback) => {
	let [appId, timestamp, nonceStr, signature, packages, sign] = [data.appid, data.timeStamp, data.nonceStr, data.app_sign,data.package, data.app_sign
	];
	
	wx.config({
		debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
		appId, // 必填，公众号的唯一标识
		timestamp, // 必填，生成签名的时间戳
		nonceStr, // 必填，生成签名的随机串
		signature, // 必填，签名，见附录1
		jsApiList: ['chooseWXPay'] // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
	});
	WeixinJSBridge.invoke(
		'getBrandWCPayRequest', {
			appId: appId, //公众号名称，由商户传入
			timeStamp: timestamp, //时间戳，自1970年以来的秒数
			nonceStr: nonceStr, //随机串
			package: packages,
			signType: 'MD5', //微信签名方式：
			paySign: sign //微信签名
		},
		function(res) {
			if (res.err_msg == 'get_brand_wcpay_request:ok') {
				// 使用以上方式判断前端返回,微信团队郑重提示：
				//res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。
				//支付成功回调
				callback(res)
			} else if (res.err_msg == 'get_brand_wcpay_request:cancel') {
				//支付失败回调
				errorCallback(res)
			}
		}
	)
}
 
// 微信分享
const wexinShare = (data, callback, errorCallback) => {
	let [appId, timestamp, nonceStr, signature, packages, sign,name,link,url] = [data.appId, data.timestamp, data.nonceStr, data.signature,data.package, data.app_sign,data.name,data.link,data.url	];
	wx.config({
		debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
		appId, // 必填，公众号的唯一标识
		timestamp, // 必填，生成签名的时间戳
		nonceStr, // 必填，生成签名的随机串
		signature, // 必填，签名，见附录1
		jsApiList: ["onMenuShareAppMessage"] // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
	});
	
	
	wx.ready(function(){
	  // config信息验证后会执行ready方法，所有接口调用都必须在config接口获得结果之后，config是一个客户端的异步操作，所以如果需要在页面加载时就调用相关接口，则须把相关接口放在ready函数中调用来确保正确执行。对于用户触发时才调用的接口，则可以直接调用，不需要放在ready函数中。
	    
        // 设置分享内容
		wx.onMenuShareAppMessage({
		  title: '', // 分享标题
		  desc: name, // 分享描述
		  link: link, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
			imgUrl:url,
		  type: '', // 分享类型,music、video或link，不填默认为link
		  dataUrl: '', // 如果type是music或video，则要提供数据链接，默认为空
		  success: function (res) {
		  	console.log("分享成功",name,link,url);
		  	// 设置成功
		  	callback(res)
		  },
		  fail:function(err){
		  	errorCallback(err)
		  }
		});
	});
}
 
 
// 隐藏右上角胶囊
const wexinHide = (data, callback, errorCallback) => {
	let [appId, timestamp, nonceStr, signature] = [data.appId, data.timestamp, data.nonceStr, data.signature];
	wx.config({
		debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
		appId, // 必填，公众号的唯一标识
		timestamp, // 必填，生成签名的时间戳
		nonceStr, // 必填，生成签名的随机串
		signature, // 必填，签名，见附录1
		jsApiList: ["hideOptionMenu"] // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
	});
	
	wx.ready(function(){
	  // config信息验证后会执行ready方法，所有接口调用都必须在config接口获得结果之后，config是一个客户端的异步操作，所以如果需要在页面加载时就调用相关接口，则须把相关接口放在ready函数中调用来确保正确执行。对于用户触发时才调用的接口，则可以直接调用，不需要放在ready函数中。
		wx.hideOptionMenu();
 
	});
	wx.error(function(res){
		console.log("信息验证失败",res);
	  // config信息验证失败会执行error函数，如签名过期导致验证失败，具体错误信息可以打开config的debug模式查看，也可以在返回的res参数中查看，对于SPA可以在这里更新签名。
	});
 
}
 
// 导出这三个方法
export default {
	wexinPay,
	wexinShare,
	wexinHide,
}