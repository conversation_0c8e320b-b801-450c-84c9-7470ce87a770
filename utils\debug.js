// 调试工具
export const debugLog = {
  // 网络请求调试
  logRequest: (url, method, data, headers) => {
    console.group('🌐 网络请求')
    console.log('URL:', url)
    console.log('Method:', method)
    console.log('Data:', data)
    console.log('Headers:', headers)
    console.groupEnd()
  },
  
  // 网络响应调试
  logResponse: (url, response) => {
    console.group('📥 网络响应')
    console.log('URL:', url)
    console.log('Status Code:', response.statusCode)
    console.log('Response Data:', response.data)
    console.groupEnd()
  },
  
  // 错误调试
  logError: (url, error) => {
    console.group('❌ 网络错误')
    console.log('URL:', url)
    console.error('Error:', error)
    console.groupEnd()
  },
  
  // 登录流程调试
  logLogin: (step, data) => {
    console.group('🔐 登录流程')
    console.log('步骤:', step)
    console.log('数据:', data)
    console.groupEnd()
  }
}

// 网络状态检查
export const checkNetworkStatus = () => {
  return new Promise((resolve) => {
    uni.getNetworkType({
      success: (res) => {
        console.log('网络类型:', res.networkType)
        resolve(res.networkType !== 'none')
      },
      fail: () => {
        resolve(false)
      }
    })
  })
}

// API测试工具
export const testAPI = async (url) => {
  try {
    const response = await uni.request({
      url: url,
      method: 'GET',
      timeout: 10000
    })
    console.log('API测试结果:', response)
    return response.statusCode === 200
  } catch (error) {
    console.error('API测试失败:', error)
    return false
  }
}
