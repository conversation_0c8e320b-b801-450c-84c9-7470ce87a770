### 更新版蓝牙指令格式总结（整合OTA升级）

#### 完整指令结构（嵌套格式）
```plaintext
┌─────────────┬─────────────┬───────────────┬──────────────────────────────────────┬──────────┐
│  帧头       │  命令       │  数据长度     │                数据区                │ CRC校验  │
│ (2 Byte)    │ (1 Byte)    │ (2 Byte)      │              (变长)                  │ (1 Byte) │
├─────────────┼─────────────┼───────────────┼──────────────────────────────────────┼──────────┤
│  0xA5FE     │  0x80       │  N+7          │  OTA升级指令（完整结构见下方）        │ 动态计算 │
│ (固定)      │ (OTA指令)   │ (OTA总长度+7) │                                      │ (低8位)  │
└─────────────┴─────────────┴───────────────┴──────────────────────────────────────┴──────────┘
```

#### 数据区中的OTA升级指令结构
```plaintext
┌─────────────┬─────────────┬─────────────┬─────────────┬───────────────┬──────────┐
│  OTA帧头    │  版本号     │  消息码     │  CRC16      │  数据区长度   │  数据内容 │
│ (2 Byte)    │ (1 Byte)    │ (1 Byte)    │ (2 Byte)    │ (2 Byte)      │ (变长)   │
├─────────────┼─────────────┼─────────────┼─────────────┼───────────────┼──────────┤
│  0xFFFE     │  0x01       │  消息类型    │ 动态计算    │  M           │ 实际数据  │
│ (固定)      │ (协议版本1) │ (19-24)     │ (CRC16校验) │ (0x0000-0x0100)│ (0-256B) │
└─────────────┴─────────────┴─────────────┴─────────────┴───────────────┴──────────┘
```

### 关键计算规则


0. **JS版CRC16校验算法**：
```javascript
const crc16Table = [
  0x0000, 0x1021, 0x2042, 0x3063, 0x4084, 0x50a5, 0x60c6, 0x70e7,
  0x8108, 0x9129, 0xa14a, 0xb16b, 0xc18c, 0xd1ad, 0xe1ce, 0xf1ef,
  0x1231, 0x0210, 0x3273, 0x2252, 0x52b5, 0x4294, 0x72f7, 0x62d6,
  0x9339, 0x8318, 0xb37b, 0xa35a, 0xd3bd, 0xc39c, 0xf3ff, 0xe3de,
  0x2462, 0x3443, 0x0420, 0x1401, 0x64e6, 0x74c7, 0x44a4, 0x5485,
  0xa56a, 0xb54b, 0x8528, 0x9509, 0xe5ee, 0xf5cf, 0xc5ac, 0xd58d,
  0x3653, 0x2672, 0x1611, 0x0630, 0x76d7, 0x66f6, 0x5695, 0x46b4,
  0xb75b, 0xa77a, 0x9719, 0x8738, 0xf7df, 0xe7fe, 0xd79d, 0xc7bc,
  0x48c4, 0x58e5, 0x6886, 0x78a7, 0x0840, 0x1861, 0x2802, 0x3823,
  0xc9cc, 0xd9ed, 0xe98e, 0xf9af, 0x8948, 0x9969, 0xa90a, 0xb92b,
  0x5af5, 0x4ad4, 0x7ab7, 0x6a96, 0x1a71, 0x0a50, 0x3a33, 0x2a12,
  0xdbfd, 0xcbdc, 0xfbbf, 0xeb9e, 0x9b79, 0x8b58, 0xbb3b, 0xab1a,
  0x6ca6, 0x7c87, 0x4ce4, 0x5cc5, 0x2c22, 0x3c03, 0x0c60, 0x1c41,
  0xedae, 0xfd8f, 0xcdec, 0xddcd, 0xad2a, 0xbd0b, 0x8d68, 0x9d49,
  0x7e97, 0x6eb6, 0x5ed5, 0x4ef4, 0x3e13, 0x2e32, 0x1e51, 0x0e70,
  0xff9f, 0xefbe, 0xdfdd, 0xcffc, 0xbf1b, 0xaf3a, 0x9f59, 0x8f78,
  0x9188, 0x81a9, 0xb1ca, 0xa1eb, 0xd10c, 0xc12d, 0xf14e, 0xe16f,
  0x1080, 0x00a1, 0x30c2, 0x20e3, 0x5004, 0x4025, 0x7046, 0x6067,
  0x83b9, 0x9398, 0xa3fb, 0xb3da, 0xc33d, 0xd31c, 0xe37f, 0xf35e,
  0x02b1, 0x1290, 0x22f3, 0x32d2, 0x4235, 0x5214, 0x6277, 0x7256,
  0xb5ea, 0xa5cb, 0x95a8, 0x8589, 0xf56e, 0xe54f, 0xd52c, 0xc50d,
  0x34e2, 0x24c3, 0x14a0, 0x0481, 0x7466, 0x6447, 0x5424, 0x4405,
  0xa7db, 0xb7fa, 0x8799, 0x97b8, 0xe75f, 0xf77e, 0xc71d, 0xd73c,
  0x26d3, 0x36f2, 0x0691, 0x16b0, 0x6657, 0x7676, 0x4615, 0x5634,
  0xd94c, 0xc96d, 0xf90e, 0xe92f, 0x99c8, 0x89e9, 0xb98a, 0xa9ab,
  0x5844, 0x4865, 0x7806, 0x6827, 0x18c0, 0x08e1, 0x3882, 0x28a3,
  0xcb7d, 0xdb5c, 0xeb3f, 0xfb1e, 0x8bf9, 0x9bd8, 0xabbb, 0xbb9a,
  0x4a75, 0x5a54, 0x6a37, 0x7a16, 0x0af1, 0x1ad0, 0x2ab3, 0x3a92,
  0xfd2e, 0xed0f, 0xdd6c, 0xcd4d, 0xbdaa, 0xad8b, 0x9de8, 0x8dc9,
  0x7c26, 0x6c07, 0x5c64, 0x4c45, 0x3ca2, 0x2c83, 0x1ce0, 0x0cc1,
  0xef1f, 0xff3e, 0xcf5d, 0xdf7c, 0xaf9b, 0xbfba, 0x8fd9, 0x9ff8,
  0x6e17, 0x7e36, 0x4e55, 0x5e74, 0x2e93, 0x3eb2, 0x0ed1, 0x1ef0
];

/**
 * CRC16校验计算
 * @param {Uint8Array} data - 输入字节数组
 * @returns {number} 16位校验值
 */
function calculateCRC16(data) {
  let crc = 0x0000;
  for (let i = 0; i < data.length; i++) {
    const index = (crc >> 8) ^ data[i];
    crc = ((crc << 8) & 0xFFFF) ^ crc16Table[index & 0xFF];
  }
  return crc;
}

// 测试用例 (FF FE 01 13 00 00 00 00)
const testData = new Uint8Array([0xFF, 0xFE, 0x01, 0x13, 0x00, 0x00, 0x00, 0x00]);
const crcValue = calculateCRC16(testData);
console.log(crcValue.toString(16).toUpperCase()); // 输出: "1647"
```
1. **外层数据长度计算**：
   ```javascript
   // OTA数据区总长度 = 2(帧头) + 1(版本) + 1(消息码) + 2(CRC16) + 2(数据区长度) + M(数据内容)
   const otaTotalLength = 8 + dataContentLength;  // M = 数据内容长度
   
   // 外层指令的数据长度字段值 (2字节大端序)
   const outerDataLength = otaTotalLength;
   ```

2. **内层CRC16计算**：
   ```javascript
   // 计算范围：FF FE + 版本 + 消息码 + 数据区长度 + 数据内容
   const crcData = new Uint8Array([
     0xFF, 0xFE, 
     version, 
     messageCode,
     ...dataLengthBytes,  // 2字节数据区长度
     ...dataContent      // 实际数据
   ]);
   const crcValue = calculateCRC16(crcData);  // 使用之前提供的JS CRC16算法
   ```

3. **外层CRC8计算**：
   ```javascript
   function calcOuterCRC(cmd, dataLen, otaData) {
     let sum = cmd;
     sum += (dataLen >> 8) & 0xFF;  // 长度高位
     sum += dataLen & 0xFF;         // 长度低位
     for (let byte of otaData) sum += byte;
     return sum & 0xFF;  // 取低8位
   }
   ```

### 完整指令示例
**新版本通知指令**：
```
外层指令头: A5 FE 80 00 1C
OTA数据区: FF FE 01 14 16 47 00 12 56 32 2E 31 36... 01 F4 00 81 38 36
外层CRC: XX
```
其中：
- `00 1C` = 28字节（2+1+1+2+2+18）
- `16 47` = CRC16校验值
- `00 12` = 18字节数据内容
- `56 32 2E 31 36` = "V2.16"的ASCII
- `01 F4` = 分片大小500
- `00 81` = 分片总数129
- `38 36` = 校验码3836

### 更新后的蓝牙指令格式总结（含设备ID获取）

#### 完整指令结构（嵌套格式）
```plaintext
┌─────────────┬─────────────┬───────────────┬──────────────────────┬──────────┐
│  帧头       │  命令       │  数据长度     │        数据区        │ CRC校验  │
│ (2 Byte)    │ (1 Byte)    │ (2 Byte)      │ (变长)               │ (1 Byte) │
├─────────────┼─────────────┼───────────────┼──────────────────────┼──────────┤
│  0xA5FE     │  命令类型   │  动态计算     │  指令特定数据结构    │ 动态计算 │
│ (固定)      │ (0x80/0x81) │              │                      │ (低8位)  │
└─────────────┴─────────────┴───────────────┴──────────────────────┴──────────┘
```

### 新增指令：获取设备ID (0x81)

| 字段       | 值        | 说明                     |
|------------|-----------|--------------------------|
| 命令       | 0x81      | 获取蓝牙所属设备ID       |
| 数据长度   | 0x0000    | 无数据内容               |
| 数据区     | 空        |                          |
| 响应数据   | ASCII字符串 | 设备ID (如：86970069944772) |

**示例指令**：
- 请求：`A5FE 81 0000 [CRC]`
- 响应：`A5FE 81 0010 38363730303639393434373732 [CRC]`  
  (设备ID: "86970069944772")

---

### OTA升级流程（Mermaid语法）

```mermaid
sequenceDiagram
    participant 小程序端
    participant 设备端
    participant 服务端

    rect rgb(191, 223, 255)
    note over 小程序端,设备端: 蓝牙连接阶段
    小程序端->>设备端: 1. 连接蓝牙设备
    end

    rect rgb(223, 191, 255)
    note over 小程序端,设备端: 设备ID获取
    小程序端->>设备端: 2. 获取设备ID (0x81)
    设备端-->>小程序端: 3. 返回设备ID
    end

    rect rgb(191, 223, 255)
    note over 小程序端,服务端: 升级准备阶段
    小程序端->>服务端: 4. 请求升级包信息
    服务端-->>小程序端: 5. 返回升级包信息
    小程序端->>服务端: 6. 下载升级包(缓存本地)
    end

    rect rgb(191, 255, 191)
    note over 小程序端,设备端: OTA升级流程
    小程序端->>设备端: 7. 查询版本 (0x80+0x13)
    设备端-->>小程序端: 8. 返回当前版本号
    
    alt 需要升级
        小程序端->>设备端: 9. 新版本通知 (0x80+0x14)
        设备端-->>小程序端: 10. 返回结果码 (0x00=允许升级)
        
        loop 分片传输 (断点续传)
            设备端->>小程序端: 11. 请求分片 (0x80+0x15)
            alt 分片有效
                小程序端-->>设备端: 12. 返回分片数据
            else 分片无效
                小程序端-->>设备端: 12. 返回错误码
                设备端->>小程序端: 上报下载状态 (0x80+0x84)
            end
        end
        
        设备端->>小程序端: 13. 上报下载成功 (0x80+0x84)
        小程序端->>设备端: 14. 执行升级 (0x80+0x85)
        设备端-->>小程序端: 15. 返回执行结果
        设备端->>小程序端: 16. 上报升级结果 (0x80+0x86)
        小程序端-->>设备端: 17. 确认结果 (0x00)
    else 无需升级
        小程序端-->>设备端: 中止升级流程
    end
    end
```

### 流程关键变更说明

1. **新增设备ID获取阶段**：
   - 连接蓝牙后立即获取设备ID（指令0x81）
   - 设备响应示例：`A5FE810010383636393730303639393434373732XX`
     - `0010`：数据长度16字节
     - `3836...3732`：ASCII编码的设备ID "86970069944772"

2. **升级准备阶段优化**：
   ```mermaid
   graph LR
   A[获取设备ID] --> B[根据设备ID请求服务端]
   B --> C{服务端响应}
   C -->|成功| D[下载升级包到本地缓存]
   C -->|失败| E[终止流程]
   ```

3. **OTA指令嵌套关系**：
   ```mermaid
   graph TD
   外层指令 --> A[命令 0x80]
   A --> OTA帧头
   OTA帧头 --> 版本号
   OTA帧头 --> 消息码
   OTA帧头 --> CRC16
   OTA帧头 --> 数据区
   数据区 --> 实际内容
   ```

### 关键接口说明

#### 1. 获取设备ID (0x81)
- **请求格式**：`A5FE 81 0000 [CRC]`
- **响应格式**：`A5FE 81 [长度] [设备ID] [CRC]`
  - 长度 = 设备ID字符串长度的十六进制（大端序）
  - 设备ID = ASCII编码的十六进制值

#### 2. OTA升级指令 (0x80)
**数据区结构**：
```plaintext
┌───────┬───────┬───────┬───────┬───────────┬─────────┐
| 帧头  | 版本  | 消息码| CRC16 | 数据长度  | 数据内容 |
| FFFE  | 01    | 13-24 | 动态  | 0000-0100 | 0-256B  |
└───────┴───────┴───────┴───────┴───────────┴─────────┘
```

### 开发注意事项
1. **设备ID处理**：
   ```javascript
   // 将十六进制设备ID转字符串
   function hexToDeviceId(hexData) {
     let str = '';
     for (let i = 0; i < hexData.length; i += 2) {
       str += String.fromCharCode(parseInt(hexData.substr(i, 2), 16));
     }
     return str;
   }
   ```

2. **动态长度计算**：
   ```javascript
   // 计算OTA数据区总长度
   function calcOtaTotalLength(dataContent) {
     const baseLength = 8; // 帧头2+版本1+消息码1+CRC16+数据长度2
     return baseLength + dataContent.length;
   }
   ```

3. **升级包请求**：
   - 根据获取的设备ID请求接口：`GET /cst/firmware/enableVersion?deviceCode=863155050404639`
   - 响应格式：
     ```json
     {
       "version": "SF02-103A SV1.0 250125",
       "url": "http://127.0.0.1:9300/statics/2025/04/16/CH4_BC260_TCP_20250416155330A003.bin"
     }
     ```

     a5 fe 80 0 d3 
     ff fe 1 15 32 59 cb 0 0 1 0 c8 c9 ca cb cc cd ce cf d0 d1 d2 d3 d4 d5 d6 d7 d8 d9 da db dc dd de df e0 e1 e2 e3 e4 e5 e6 e7 e8 e9 ea eb ec ed ee ef f0 f1 f2 f3 f4 f5 f6 f7 f8 f9 fa fb fc fd fe ff 0 1 2 3 4 5 6 7 8 9 a b c d e f 10 11 12 13 14 15 16 17 18 19 1a 1b 1c 1d 1e 1f 20 21 22 23 24 25 26 27 28 29 2a 2b 2c 2d 2e 2f 30 31 32 33 34 35 36 37 38 39 3a 3b 3c 3d 3e 3f 40 41 42 43 44 45 46 47 48 49 4a 4b 4c 4d 4e 4f 50 51 52 53 54 55 56 57 58 59 5a 5b 5c 5d 5e 5f 60 61 62 63 64 65 66 67 68 69 6a 6b 6c 6d 6e 6f 70 71 72 73 74 75 76 77 78 79 7a 7b 7c 7d 7e 7f 80 81 82 83 84 85 86 87 88 89 8a 8b 8c 8d 8e 8f 
     b9 