<template>
	<view class="DefaultHandle">
		<!-- <view v-if="isSuper == false"  class="tabsView">
			<p v-for="(tabs,index) in tabsList" :class="currentTab===index?'activeTabs':'tabsClass'"
				@click="cutTabs(index)">{{tabs}}</p>
		</view>
		<view v-else  class="tabsView">
			<p v-for="(tabs,index) in tabsList" :class="currentTab===index?'activeTabs':'tabsClass'"
				@click="cutTabs(index)">{{tabs}}</p>
		</view> -->
		
		<view v-if="isSuper == true" class="sceneManage">
		<view  class="listView">
			<view class="listModule" v-for="(item,index) in bjDataList" :key='index' @click.stop="moreDefault(item)">
				<view class="userHead">
					
					<image src="@/static/image/故障信息.png" mode=""></image>
				</view>
				<view class="userContent">
					<view class="titleHead" v-if="item.status == '待处理'">
						<text>{{item.code}}</text>
						<view class="operBtn">
							<u-button type="primary" :plain="true" size="small" text="派遣"
								@tap.stop="toEditPage(item)"></u-button>
							<text class="btnJ"></text>
							<u-button type="error" :plain="true" size="small" text="处理"
								@tap.stop="toDeletJL(item)"></u-button>
						</view>
					</view>
					<view class="titleHead" v-else-if="item.status == '处理中'">
						<text>{{item.code}}</text>
						<!-- <view class="operBtn1">
							<image style="width: 120rpx;height: 120rpx;flex-shrink: 0;"  src="../../static/image/图片4.png" mode=""></image>
						</view> -->
					</view>
					<view class="titleHead" v-else-if="item.status == '已处理'">
						<text>{{item.code}}</text>
						<view class="operBtn1">
							<image src="../../static/image/已处理.png" mode=""></image>
						</view>
					</view>
					<view class="">
						<text v-if="item.equipType == '001'">类型：家用型可燃气体探测器</text>
						<text  v-else-if="item.equipType=='002'">类型：地下空间燃气泄漏监测仪</text>
						<text  v-else-if="item.equipType=='003'">类型：工商业可燃气体探测器</text>
						<text  v-else-if="item.equipType=='004'">类型：地埋式燃气泄漏监测仪</text>
						
						
					</view>
					<view class="">
						<text>创建时间：{{item.createTime}}</text>
					</view>
					<view class="">
						<text>位置：{{item.address}}</text>
					</view>
				</view>
				
				<view class="operBtn1" v-if="item.status == '处理中'">
					
					<image style="width: 120rpx;height: 120rpx;"  src="../../static/image/图片4.png" mode=""></image>
				</view>
			</view>
		</view>
		</view>
		
		
		<view v-else class="sceneManage" >
		<view  class="listView">
			<view class="listModule" v-for="(item,index) in bjDataList" :key='index' @click="moreDefault(item)">
				<view class="userHead">
					
					<image src="@/static/image/故障信息.png" mode=""></image>
				</view>
				<view class="userContent">
					<view class="titleHead" v-if="item.status == '待处理'">
						<text>普通用户</text>
						<view class="operBtn">
							<!-- <u-button type="primary" :plain="true" size="small" text="派遣"
								@click="toEditPage(item)"></u-button> -->
							<text class="btnJ"></text>
							<u-button type="error" :plain="true" size="small" text="处理"
								@tap.stop="toDeletJL(item)"></u-button>
						</view>
					</view>
					<view class="titleHead" v-else-if="item.status == '处理中'">
						<text>普通用户</text>
						<view class="operBtn1">
							<!-- <image src="../../static/image/已受理.png" mode=""></image> -->
							<u-button type="error" :plain="true" size="small" text="处理"
								@tap.stop="toDeletJL(item)"></u-button>
						</view> 
						
					</view>
					<view class="titleHead" v-else-if="item.status == '已处理'" >
						<text>普通用户</text>
						<view class="operBtn1">
							<image src="../../static/image/已处理.png" mode=""></image>
						</view>
					</view>
					<view class="">
						<text v-if="item.equipType == '001'">类型：家用型可燃气体探测器</text>
						<text  v-else-if="item.equipType=='002'">类型：地下空间燃气泄漏监测仪</text>
						<text  v-else-if="item.equipType=='003'">类型：工商业可燃气体探测器</text>
						<text  v-else-if="item.equipType=='004'">类型：地埋式燃气泄漏监测仪</text>
					</view>
					<view class="">
						<text>创建时间：{{item.createTime}}</text>
					</view>
					<view class="">
						<text>位置：{{item.address}}</text>
					</view>
				</view>
			</view>
		</view>
		</view>
	</view>
</template>

<script>
	import {
		DefaultList
	} from "@/network/api.js"
	export default {
		activated() {
		    // 在页面返回时数据刷新
		    this.getList1();
		  },
		data() {
			return {
				tabsList: ['未处置', '已处置'],
				currentTab: 0,
				isSuper: false,
				user: uni.getStorageSync("userS"),
				bjDataList: [], //数据
				total: null, //总条数
				pageNum: 1, //第几页
				pageSize: 15, //显示多少条
				equipType: '',
				modelQuery: {
					queryForm: {
						equipTypeName: '',
						code: ''
					},
				},
			}
		},
		methods: {
			onLoad(opt) { // 初始化
				// this.init(opt)
				let stu = uni.getStorageSync("userS")
				
				if( stu == "admin")
				{
					this.isSuper = true
				}
				else
				{
					this.isSuper = false
				}
				
				console.log(this.user,'传的参')
				
				
			},
			onShow(options) {
				this.bjDataList = [];
				this.getList()
				console.log(options,'传的参')
			},
			cutTabs(index) {
				this.currentTab = index;
				console.log(this.currentTab)
				this.bjDataList = [];
				this.getList();
			},
			getList() { //获取数据
			 console.log('开始了onshow')
				let params = {
					userId: uni.getStorageSync('userId'),
					equipType: this.equipType,
					code: this.modelQuery.queryForm.code,
					pageNum: this.pageNum,
					pageSize: this.pageSize
				}
				
				DefaultList(params).then(res => {
					console.log(res.data,'故障列表');
						this.bjDataList = [...this.bjDataList, ...res.data.list]
					console.log(this.bjDataList,'故障列表1');
						this.total = res.data.total
						
				})
			},
			getList1() { //获取数据
			  console.log('开始了onload')
				let params = {
					userId: uni.getStorageSync('userId'),
					equipType: this.equipType,
					code: this.modelQuery.queryForm.code,
					pageNum: this.pageNum,
					pageSize: this.pageSize
				}
				
				DefaultList(params).then(res => {
					console.log(res.data,'故障列表');
						this.bjDataList = res.data.list
					console.log(this.bjDataList,'故障列表1');
						this.total = res.data.total
						
				})
			},
			toDeletJL(item) {
				uni.navigateTo({
					url: './dispatch?gzId=' + item.id
				})
			},
			onReachBottom() { //触底事件
				if (this.pageNum * this.pageSize >= this.total) {
					uni.showToast({
						title: '没有更多数据了',
						icon: 'none',
						duration: 1000
					});
				} else {
					if (this.pageNum <= this.pageNum - 1) {
						setTimeout(() => {
							uni.hideLoading()
						}, 500)
					} else {
						uni.showLoading({
							title: '加载中'
						});
						this.pageNum++
						this.getList()
					}
					setTimeout(() => {
						uni.hideLoading()
					}, 500)
				}
			},
			moreDefault(item) {
				uni.navigateTo({
					url: './dispatch?gzId=' + item.id
				})
			},
			onPullDownRefresh(opt) {
				this.bjDataList = [];
				this.getList();
				uni.stopPullDownRefresh();
			},
			
			toEditPage(item) {
				uni.navigateTo({
					url: './handle?gzId=' + item.id
				})
			},
		}
	}
</script>
<style>
	page {
		background-color: #F6F6F6;
	}
</style>
<style lang="scss" scoped>

.DefaultHandle {
	
	.tabsView {
		width: 100%;
		border-bottom: 2rpx solid #E7E7E7;
		background-color: #ffffff;
		// margin-top: 50rpx;
		display: flex;
		justify-content: center;
		.activeTabs {
			width: 150rpx;
			font-size: 30rpx;
			font-family: PingFang SC-Medium, PingFang SC;
			font-weight: 500;
			color: #0052D9;
			line-height: 72rpx;
			border-bottom: 4rpx solid #0052D9;
			margin: 0 60rpx;
			text-align: center;
		}
		
		.tabsClass {
			width: 150rpx;
			font-size: 30rpx;
			font-family: PingFang SC-Regular, PingFang SC;
			font-weight: 400;
			color: rgba(0, 0, 0, 0.6);
			line-height: 72rpx;
			border-bottom: 4rpx solid transparent;
			margin: 0 60rpx;
			text-align: center;
		}
		
	}
	.sceneManage{
	.listView {
		margin-top: 10rpx;
	
		.listModule {
			background-color: #ffffff;
			margin-bottom: 12rpx;
			padding: 26rpx 40rpx;
			font-size: 24rpx;
			color: #3D3D3D;
			line-height: 38rpx;
			display: flex;
			align-items: center;
	
			.userHead {
				margin-right: 44rpx;
	
				image {
					width: 86rpx;
					height: 86rpx;
				}
			}
	
			.userContent {
				flex: 1;
			}
	
			.titleHead {
				font-size: 34rpx;
				color: #409EFF;
				line-height: 56rpx;
				margin-bottom: 6rpx;
				display: flex;
				justify-content: space-between;
	
				.operBtn {
					display: flex;
	
					.btnJ {
						margin-right: 30rpx;
					}
				}
				.operBtn1 {
					width: 150rpx;
					 text-align: right;
					image{
						width: 100%;
						height: 100%;
						object-fit: cover;
					}
					
				}
			}
	
			.devAllView {
				display: flex;
				flex-wrap: wrap;
				justify-content: space-between;
				margin: 10rpx 0;
	
				text {
					width: 49%;
					background: #F3F3F3;
					border-radius: 10rpx;
					text-align: center;
					font-size: 24rpx;
					color: #3D3D3D;
					line-height: 50rpx;
					margin: 10rpx 0;
				}
			}
		}
	}
	}
}
</style>
