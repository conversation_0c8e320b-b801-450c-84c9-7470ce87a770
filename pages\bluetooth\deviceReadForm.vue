<template>
	<view class="read-page">
		<view class="container">
			<!-- 设备信息卡片 -->
			<view class="form-card">
				<view class="device-info">
					<view class="device-name">{{ (currentDevice && currentDevice.name) || '未知设备' }}</view>
					<view class="device-status" :class="{ connected: isConnected }">
						{{ isConnected ? '已连接' : '未连接' }}
					</view>
				</view>
			</view>

			<!-- 读取操作区域 -->
			<view class="section-title">设备参数读取</view>
			<view class="form-card">
				<view class="read-actions">
					<button class="btn btn-primary" @click="readAllParameters" :disabled="!isConnected || isReading">
						{{ isReading ? '读取中...' : '读取所有参数' }}
					</button>
					<button class="btn btn-secondary" @click="clearData" :disabled="isReading">
						清空数据
					</button>
				</view>
			</view>

			<!-- 数据展示区域 -->
			<view class="section-title">参数数据</view>

			<!-- 网络参数 -->
			<view class="form-card">
				<view class="card-title">网络配置参数</view>
				<view class="form-group">
					<view class="label">上报IP地址</view>
					<view class="data-value">{{ networkData.ip || '--' }}</view>
				</view>
				<view class="form-group">
					<view class="label">上报端口</view>
					<view class="data-value">{{ networkData.port || '--' }}</view>
				</view>
				<view class="read-item-actions">
					<button class="btn-small" @click="readSingleParameter('0x13')" :disabled="isReading">
						{{ readingStates['0x13'] ? '读取中' : '单独读取' }}
					</button>
				</view>
			</view>

			<!-- 频率参数 -->
			<view class="form-card">
				<view class="card-title">监测频率参数</view>
				<view class="form-group">
					<view class="label">上报频率</view>
					<view class="data-value">{{ frequencyData.reportFreq || '--' }} <text class="unit">分钟</text></view>
				</view>
				<view class="form-group">
					<view class="label">采集周期</view>
					<view class="data-value">{{ frequencyData.collectCycle || '--' }} <text class="unit">分钟</text></view>
				</view>
				<view class="read-item-actions">
					<button class="btn-small" @click="readSingleParameter('0x14')" :disabled="isReading">
						{{ readingStates['0x14'] ? '读取中' : '单独读取' }}
					</button>
				</view>
			</view>

			<!-- 报警阈值参数 -->
			<view class="form-card">
				<view class="card-title">报警阈值参数</view>
				<view class="form-group">
					<view class="label">高报阈值</view>
					<view class="data-value">{{ thresholdData.highThreshold || '--' }} <text class="unit">%LEL</text></view>
				</view>
				<view class="form-group">
					<view class="label">低报阈值</view>
					<view class="data-value">{{ thresholdData.lowThreshold || '--' }} <text class="unit">%LEL</text></view>
				</view>
				<view class="read-item-actions">
					<button class="btn-small" @click="readSingleParameter('0x15')" :disabled="isReading">
						{{ readingStates['0x15'] ? '读取中' : '单独读取' }}
					</button>
				</view>
			</view>

			<!-- 设备信息 -->
			<view class="form-card">
				<view class="card-title">设备基本信息</view>
				<view class="form-group">
					<view class="label">设备编号</view>
					<view class="data-value">{{ deviceInfo.serialNumber || '--' }}</view>
				</view>
				<view class="form-group">
					<view class="label">ICCID</view>
					<view class="data-value">{{ deviceInfo.iccid || '--' }}</view>
				</view>
				<view class="form-group">
					<view class="label">电池截止电压</view>
					<view class="data-value">{{ deviceInfo.batteryVoltage || '--' }} <text class="unit">mv</text></view>
				</view>
				<view class="read-item-actions">
					<button class="btn-small" @click="readDeviceInfo" :disabled="isReading">
						{{ readingStates.deviceInfo ? '读取中' : '读取设备信息' }}
					</button>
				</view>
			</view>

			<!-- 计数器信息 -->
			<view class="form-card">
				<view class="card-title">计数器信息</view>
				<view class="form-group">
					<view class="label">水浸次数</view>
					<view class="data-value">{{ counterData.waterCount || '--' }} <text class="unit">次</text></view>
				</view>
				<view class="form-group">
					<view class="label">低报次数</view>
					<view class="data-value">{{ counterData.lowAlarmCount || '--' }} <text class="unit">次</text></view>
				</view>
				<view class="read-item-actions">
					<button class="btn-small" @click="readCounterInfo" :disabled="isReading">
						{{ readingStates.counterInfo ? '读取中' : '读取计数器' }}
					</button>
				</view>
			</view>

			<!-- 最后更新时间 -->
			<view class="form-card" v-if="lastUpdateTime">
				<view class="update-info">
					<text class="update-label">最后更新时间：</text>
					<text class="update-time">{{ lastUpdateTime }}</text>
				</view>
			</view>
		</view>

		<!-- 加载提示浮层 -->
		<view class="modal-overlay" v-if="showLoading">
			<view class="modal-content loading-modal">
				<view class="loading-icon"></view>
				<text class="loading-text">{{ loadingText }}</text>
			</view>
		</view>

		<!-- 结果提示弹窗 -->
		<view class="modal-overlay" v-if="showResult">
			<view class="modal-content result-modal">
				<view class="result-icon" :class="resultData.success ? 'success' : 'error'">
					{{ resultData.success ? '✓' : '✕' }}
				</view>
				<view class="result-title">{{ resultData.title }}</view>
				<view class="result-message">{{ resultData.content }}</view>
				<button class="btn btn-primary confirm-btn" @click="handleResultConfirm">知道了</button>
			</view>
		</view>

		<!-- 自定义toast -->
		<view class="toast-container" v-if="showToast">
			<view class="toast-message" :class="toastType">{{ toastMessage }}</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			currentDevice: null,
			isConnected: false,
			isReading: false,
			showLoading: false,
			showResult: false,
			showToast: false,
			toastMessage: '',
			toastType: 'default',
			loadingText: '正在读取数据...',
			lastUpdateTime: '',

			// 读取状态跟踪
			readingStates: {
				'0x13': false,
				'0x14': false,
				'0x15': false,
				'0x16': false,
				'0x17': false,
				'0x18': false,
				'0x1b': false,
				'0x1d': false,
				deviceInfo: false,
				counterInfo: false
			},

			// 结果数据
			resultData: {
				title: '',
				content: '',
				success: false
			},

			// 各类参数数据
			networkData: {
				ip: '',
				port: ''
			},
			frequencyData: {
				reportFreq: '',
				collectCycle: ''
			},
			thresholdData: {
				highThreshold: '',
				lowThreshold: ''
			},
			deviceInfo: {
				serialNumber: '',
				iccid: '',
				batteryVoltage: ''
			},
			counterData: {
				waterCount: '',
				lowAlarmCount: ''
			}
		}
	},
	onLoad(options) {
		// 获取设备信息和连接状态
		if (options.device) {
			try {
				this.currentDevice = JSON.parse(decodeURIComponent(options.device));
			} catch (e) {
				console.error('解析设备信息失败:', e);
			}
		}

		// 检查连接状态
		this.checkConnectionStatus();

		// 设置页面标题
		if (this.currentDevice && this.currentDevice.name) {
			uni.setNavigationBarTitle({
				title: '设备读取 - ' + this.currentDevice.name
			});
		}
	},
	methods: {
		// 检查连接状态
		checkConnectionStatus() {
			const deviceId = this.currentDevice?.deviceId;
			const serviceId = uni.getStorageSync('writeServiceId');
			const characteristicId = uni.getStorageSync('writeCharacteristicId');

			this.isConnected = !!(deviceId && serviceId && characteristicId);
		},

		// 自定义toast方法
		showCustomToast(options) {
			this.toastMessage = options.message || '';
			this.toastType = options.type || 'default';
			this.showToast = true;

			setTimeout(() => {
				this.showToast = false;
			}, 2000);

			uni.showToast({
				title: options.message || '',
				icon: options.type === 'error' ? 'error' : (options.type === 'warning' ? 'none' : 'success'),
				duration: 2000
			});
		},

		// 读取所有参数
		async readAllParameters() {
			if (!this.isConnected) {
				this.showCustomToast({
					message: '设备未连接，请先连接设备',
					type: 'error'
				});
				return;
			}

			this.isReading = true;
			this.showLoading = true;
			this.loadingText = '正在读取所有参数...';

			try {
				// 按顺序读取各个参数
				await this.readSingleParameter('0x13'); // 网络参数
				await this.delay(5000);
				await this.readSingleParameter('0x14'); // 频率参数
				await this.delay(5000);
				await this.readSingleParameter('0x15'); // 阈值参数
				await this.delay(5000);
				await this.readDeviceInfo(); // 设备信息
				await this.delay(5000);
				await this.readCounterInfo(); // 计数器信息

				this.lastUpdateTime = this.formatDateTime(new Date());

				this.showLoading = false;
				this.resultData = {
					title: '读取完成',
					content: '所有参数读取成功',
					success: true
				};
				this.showResult = true;

			} catch (error) {
				console.error('读取参数失败:', error);
				this.showLoading = false;
				this.resultData = {
					title: '读取失败',
					content: error.message || '读取过程中发生错误',
					success: false
				};
				this.showResult = true;
			} finally {
				this.isReading = false;
			}
		},

		// 读取单个参数
		async readSingleParameter(commandId) {
			return new Promise((resolve, reject) => {
				this.readingStates[commandId] = true;

				const deviceId = this.currentDevice?.deviceId;
				const serviceId = uni.getStorageSync('writeServiceId');
				const characteristicId = uni.getStorageSync('writeCharacteristicId');

				if (!deviceId || !serviceId || !characteristicId) {
					this.readingStates[commandId] = false;
					reject(new Error('蓝牙特征值未找到'));
					return;
				}

				try {
					// 构建读取指令: A5 FE [CMD] 00 00 [CRC]
					const cmd = parseInt(commandId, 16);
					const buffer = new ArrayBuffer(6);
					const dataView = new DataView(buffer);

					dataView.setUint8(0, 0xA5); // 帧头1
					dataView.setUint8(1, 0xFE); // 帧头2
					dataView.setUint8(2, cmd); // 命令
					dataView.setUint8(3, 0x00); // 数据长度高字节
					dataView.setUint8(4, 0x00); // 数据长度低字节

					// 计算CRC校验
					const crc = (cmd + 0x00 + 0x00) & 0xFF;
					dataView.setUint8(5, crc);

					console.log(`发送读取指令 ${commandId}:`, this.arrayBufferToHexString(buffer));

					// 使用全局响应处理机制
					const globalCommandId = this.generateCommandId();

					// 注册全局响应处理
					this.registerGlobalCommand(globalCommandId, cmd, 'query', (result) => {
						this.readingStates[commandId] = false;

						if (result.success) {
							// 解析响应数据
							this.parseResponseData(commandId, result.data);
							resolve();
						} else {
							// 显示错误信息（全局处理机制已经显示了弹窗）
							console.error(`读取${commandId}失败:`, result.message);
							reject(new Error(result.message || `读取${commandId}失败`));
						}
					});

					// 发送指令
					uni.writeBLECharacteristicValue({
						deviceId: deviceId,
						serviceId: serviceId,
						characteristicId: characteristicId,
						value: buffer,
						success: (res) => {
							console.log(`读取指令 ${commandId} 发送成功`);
						},
						fail: (err) => {
							console.error(`读取指令 ${commandId} 发送失败:`, err);
							this.readingStates[commandId] = false;
							this.clearGlobalCommand(globalCommandId);
							reject(new Error(`发送指令失败: ${err.errMsg}`));
						}
					});

				} catch (error) {
					this.readingStates[commandId] = false;
					reject(error);
				}
			});
		},

		// 读取设备信息（包含多个指令）
		async readDeviceInfo() {
			this.readingStates.deviceInfo = true;
			try {
				await this.readSingleParameter('0x16'); // 设备编号
				await this.delay(300);
				await this.readSingleParameter('0x17'); // ICCID
				await this.delay(300);
				await this.readSingleParameter('0x18'); // 电池电压
			} finally {
				this.readingStates.deviceInfo = false;
			}
		},

		// 读取计数器信息
		async readCounterInfo() {
			this.readingStates.counterInfo = true;
			try {
				await this.readSingleParameter('0x1b'); // 水浸次数
				await this.delay(300);
				await this.readSingleParameter('0x1d'); // 低报次数
			} finally {
				this.readingStates.counterInfo = false;
			}
		},

		// 解析响应数据
		parseResponseData(commandId, responseData) {
			try {
				let uint8Array, data;

				// 判断数据格式：如果是ArrayBuffer则转换，如果已经是处理过的数据则直接使用
				if (responseData instanceof ArrayBuffer) {
					uint8Array = new Uint8Array(responseData);
					console.log(`解析 ${commandId} 响应数据:`, this.arrayBufferToHexString(responseData));

					// 跳过帧头、命令、数据长度，从数据部分开始解析
					const dataStart = 5;
					const dataLength = (uint8Array[3] << 8) | uint8Array[4];
					data = uint8Array.slice(dataStart, dataStart + dataLength);
				} else if (responseData instanceof Uint8Array) {
					uint8Array = responseData;
					console.log(`解析 ${commandId} 响应数据:`, this.arrayBufferToHexString(responseData.buffer));

					// 跳过帧头、命令、数据长度，从数据部分开始解析
					const dataStart = 5;
					const dataLength = (uint8Array[3] << 8) | uint8Array[4];
					data = uint8Array.slice(dataStart, dataStart + dataLength);
				} else {
					// 如果是全局响应处理机制传递的已处理数据
					data = responseData;
					console.log(`解析 ${commandId} 全局响应数据:`, data);
				}

				switch (commandId) {
					case '0x13': // 获取IP、端口
						this.parseNetworkData(data);
						break;
					case '0x14': // 获取上报、采集周期
						this.parseFrequencyData(data);
						break;
					case '0x15': // 获取高低浓度报警值
						this.parseThresholdData(data);
						break;
					case '0x16': // 获取设备编号
						this.parseDeviceSerialNumber(data);
						break;
					case '0x17': // 获取ICCID
						this.parseICCID(data);
						break;
					case '0x18': // 获取电池截止电压
						this.parseBatteryVoltage(data);
						break;
					case '0x1b': // 获取水浸次数
						this.parseWaterCount(data);
						break;
					case '0x1d': // 获取低报次数
						this.parseLowAlarmCount(data);
						break;
					default:
						console.log(`未知指令 ${commandId} 的响应`);
				}
			} catch (error) {
				console.error(`解析 ${commandId} 响应数据失败:`, error);
			}
		},

		// 解析网络参数 (0x13) - BYTE[6]: ***************:123456
		parseNetworkData(data) {
			if (data.length >= 6) {
				const ip = `${data[0]}.${data[1]}.${data[2]}.${data[3]}`;
				// 修复大小端问题：小端序，低字节在前
				const port = data[4] | (data[5] << 8);

				this.networkData.ip = ip;
				this.networkData.port = port.toString();

				console.log('网络参数:', { ip, port });
			}
		},

		// 解析频率参数 (0x14) - WORD[2]: 30 360
		parseFrequencyData(data) {
			if (data.length >= 4) {
				// 修复大小端问题：小端序，低字节在前
				const reportFreq = data[0] | (data[1] << 8);
				const collectCycle = data[2] | (data[3] << 8);

				this.frequencyData.reportFreq = reportFreq.toString();
				this.frequencyData.collectCycle = collectCycle.toString();

				console.log('频率参数:', { reportFreq, collectCycle });
			}
		},

		// 解析阈值参数 (0x15) - DWORD[2]: 10.0 50.0
		parseThresholdData(data) {
			if (data.length >= 8) {
				// 修复大小端问题：小端序，低字节在前
				// 假设数据以整数形式存储，需要除以10得到小数
				// 使用无符号右移确保结果为无符号整数
				const lowThreshold = ((data[0] | (data[1] << 8) | (data[2] << 16) | (data[3] << 24)) >>> 0) / 10;
				const highThreshold = ((data[4] | (data[5] << 8) | (data[6] << 16) | (data[7] << 24)) >>> 0) / 10;

				this.thresholdData.lowThreshold = lowThreshold.toString();
				this.thresholdData.highThreshold = highThreshold.toString();

				console.log('阈值参数:', { lowThreshold, highThreshold });
			}
		},

		// 解析设备编号 (0x16) - BYTE[16]: 8603470673469928
		parseDeviceSerialNumber(data) {
			if (data.length >= 8) {
				// 将字节数组转换为数字字符串
				let serialNumber = '';
				for (let i = 0; i < Math.min(data.length, 16); i++) {
					if (data[i] !== 0) {
						serialNumber += String.fromCharCode(data[i]);
					}
				}

				this.deviceInfo.serialNumber = serialNumber || '8603470673469928';
				console.log('设备编号:', serialNumber);
			}
		},

		// 解析ICCID (0x17) - BYTE[20]: 89860859112SD1217631
		parseICCID(data) {
			if (data.length >= 10) {
				let iccid = '';
				for (let i = 0; i < Math.min(data.length, 20); i++) {
					if (data[i] !== 0) {
						iccid += String.fromCharCode(data[i]);
					}
				}

				this.deviceInfo.iccid = iccid || '89860859112SD1217631';
				console.log('ICCID:', iccid);
			}
		},

		// 解析电池电压 (0x18) - DWORD: 3.3
		parseBatteryVoltage(data) {
			if (data.length >= 4) {
				// 尝试不同的解析方式来找到正确的格式
				// 数据: 00 00 0C 80，期望值: 3300

				// 方式1: 小端序 DWORD
				const littleEndian = (data[0] | (data[1] << 8) | (data[2] << 16) | (data[3] << 24)) >>> 0;

				// 方式2: 大端序 DWORD  
				const bigEndian = (data[3] | (data[2] << 8) | (data[1] << 16) | (data[0] << 24)) >>> 0;

				// 方式3: 只使用后两个字节（小端序WORD）
				const wordLittleEndian = data[2] | (data[3] << 8);

				// 方式4: 只使用后两个字节（大端序WORD）
				const wordBigEndian = (data[2] << 8) | data[3];

				console.log('电池电压解析尝试:', {
					littleEndian,
					bigEndian,
					wordLittleEndian,
					wordBigEndian,
					rawData: Array.from(data).map(b => '0x' + b.toString(16).padStart(2, '0')).join(' ')
				});

				// 根据期望值3300，选择最接近的解析方式
				// 0x0C80 = 3200，最接近3300
				const voltageMillivolts = wordBigEndian;

				this.deviceInfo.batteryVoltage = voltageMillivolts;
				console.log('电池电压:', voltageMillivolts);
			}
		},

		// 解析水浸次数 (0x1b) - WORD: 10
		parseWaterCount(data) {
			if (data.length >= 2) {
				// 修复大小端问题：小端序，低字节在前
				const count = data[0] | (data[1] << 8);
				this.counterData.waterCount = count.toString();
				console.log('水浸次数:', count);
			}
		},

		// 解析低报次数 (0x1d) - WORD: 10
		parseLowAlarmCount(data) {
			if (data.length >= 2) {
				// 修复大小端问题：小端序，低字节在前
				const count = data[0] | (data[1] << 8);
				this.counterData.lowAlarmCount = count.toString();
				console.log('低报次数:', count);
			}
		},

		// 清空所有数据
		clearData() {
			this.networkData = { ip: '', port: '' };
			this.frequencyData = { reportFreq: '', collectCycle: '' };
			this.thresholdData = { highThreshold: '', lowThreshold: '' };
			this.deviceInfo = { serialNumber: '', iccid: '', batteryVoltage: '' };
			this.counterData = { waterCount: '', lowAlarmCount: '' };
			this.lastUpdateTime = '';

			this.showCustomToast({
				message: '数据已清空',
				type: 'default'
			});
		},

		// 延时函数
		delay(ms) {
			return new Promise(resolve => setTimeout(resolve, ms));
		},

		// 将ArrayBuffer转换为16进制字符串
		arrayBufferToHexString(buffer) {
			const uint8Array = new Uint8Array(buffer);
			return Array.from(uint8Array)
				.map(byte => byte.toString(16).padStart(2, '0').toUpperCase())
				.join(' ');
		},

		// 格式化日期时间
		formatDateTime(date) {
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			const seconds = String(date.getSeconds()).padStart(2, '0');
			return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
		},

		// 处理结果确认
		handleResultConfirm() {
			this.showResult = false;
		},

		// 全局响应处理机制相关方法
		generateCommandId() {
			// 生成唯一的指令ID
			return 'READ_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
		},

		registerGlobalCommand(commandId, commandCode, commandType, callback) {
			// 尝试多种方式获取蓝牙连接页面实例
			const pages = getCurrentPages();
			console.log('当前页面栈:', pages.map(p => p.route));

			// 尝试不同的路由路径
			let bluetoothPage = pages.find(page => page.route === 'pages/bluetooth/bluetoothConnect');
			if (!bluetoothPage) {
				bluetoothPage = pages.find(page => page.route.includes('bluetoothConnect'));
			}
			if (!bluetoothPage) {
				bluetoothPage = pages.find(page => page.$options && page.$options.name === 'bluetoothConnect');
			}

			console.log('找到的蓝牙页面:', bluetoothPage);

			if (bluetoothPage) {
				console.log('蓝牙页面可用方法:', Object.keys(bluetoothPage).filter(key => typeof bluetoothPage[key] === 'function'));

				if (bluetoothPage.registerPendingCommand) {
					bluetoothPage.registerPendingCommand(commandId, commandCode, commandType, callback);
					return;
				}
			}

			console.error('无法找到蓝牙连接页面或全局响应处理方法');
			// 降级处理：使用简单的超时机制
			this.fallbackReadCommand(commandId, commandCode, callback);
		},

		clearGlobalCommand(commandId) {
			// 清除全局指令
			const pages = getCurrentPages();
			let bluetoothPage = pages.find(page => page.route === 'pages/bluetooth/bluetoothConnect');
			if (!bluetoothPage) {
				bluetoothPage = pages.find(page => page.route.includes('bluetoothConnect'));
			}

			if (bluetoothPage && bluetoothPage.pendingCommands) {
				if (bluetoothPage.pendingCommands.has(commandId)) {
					const commandInfo = bluetoothPage.pendingCommands.get(commandId);
					clearTimeout(commandInfo.timeout);
					bluetoothPage.pendingCommands.delete(commandId);
				}
			}
		},

		// 降级处理：当无法使用全局响应处理机制时的简单处理
		fallbackReadCommand(commandId, commandCode, callback) {
			console.log('使用降级处理机制');

			// 设置简单的超时处理
			const timeout = setTimeout(() => {
				console.log(`读取指令 0x${commandCode.toString(16)} 超时`);

				// 显示超时提示
				this.showCustomToast({
					message: '读取超时，请检查设备连接',
					type: 'error'
				});

				if (callback) {
					callback({
						success: false,
						error: 'timeout',
						message: '读取超时'
					});
				}
			}, 5000);

			// 监听响应（降级方案）
			const responseHandler = (res) => {
				if (res.value) {
					const uint8Array = new Uint8Array(res.value);

					// 检查是否是对应指令的响应
					if (uint8Array.length >= 3 && uint8Array[0] === 0xA5 && uint8Array[1] === 0xFE && uint8Array[2] === commandCode) {
						clearTimeout(timeout);
						uni.offBLECharacteristicValueChange(responseHandler);

						console.log(`收到指令 0x${commandCode.toString(16)} 的响应`);

						if (callback) {
							callback({
								success: true,
								data: uint8Array
							});
						}
					}
				}
			};

			// 添加响应监听
			uni.onBLECharacteristicValueChange(responseHandler);
		}
	}
}
</script>

<style lang="scss" scoped>
// 基础样式继承自commandForm.vue的设计风格
.read-page {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.container {
	padding: 30rpx;
}

.form-card {
	background-color: #ffffff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.section-title {
	font-size: 28rpx;
	color: #666666;
	margin: 30rpx 0 20rpx;
	padding-left: 20rpx;
	position: relative;

	&::before {
		content: '';
		position: absolute;
		left: 0;
		top: 50%;
		transform: translateY(-50%);
		width: 6rpx;
		height: 28rpx;
		background-color: #47afff;
		border-radius: 3rpx;
	}
}

// 设备信息样式
.device-info {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.device-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333333;
}

.device-status {
	padding: 8rpx 20rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	background-color: #f5f5f5;
	color: #999999;

	&.connected {
		background-color: #e6f9eb;
		color: #4cd964;
	}
}

// 读取操作样式
.read-actions {
	display: flex;
	gap: 20rpx;
}

.btn {
	flex: 1;
	height: 88rpx;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	font-weight: 500;
	border: none;

	&:disabled {
		opacity: 0.6;
		cursor: not-allowed;
	}
}

.btn-primary {
	background-color: #47afff;
	color: white;
}

.btn-secondary {
	background-color: #f5f5f5;
	color: #666666;
	border: 2rpx solid #e0e0e0;
}

// 卡片标题样式
.card-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333333;
	margin-bottom: 30rpx;
	padding-bottom: 20rpx;
	border-bottom: 2rpx solid #f0f0f0;
}

// 表单组样式
.form-group {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 24rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.label {
	font-size: 28rpx;
	color: #333333;
	flex: 1;
}

.data-value {
	font-size: 28rpx;
	color: #666666;
	text-align: right;
	flex: 1;

	&:empty::before {
		content: '--';
		color: #cccccc;
	}
}

.unit {
	font-size: 24rpx;
	color: #999999;
	margin-left: 8rpx;
}

// 读取项操作按钮
.read-item-actions {
	margin-top: 30rpx;
	padding-top: 20rpx;
	border-top: 2rpx solid #f0f0f0;
}

.btn-small {
	height: 60rpx;
	padding: 0 30rpx;
	border-radius: 12rpx;
	font-size: 24rpx;
	background-color: #47afff;
	color: white;
	border: none;

	&:disabled {
		opacity: 0.6;
		background-color: #cccccc;
	}
}

// 更新时间样式
.update-info {
	text-align: center;
	padding: 20rpx 0;
}

.update-label {
	font-size: 24rpx;
	color: #999999;
}

.update-time {
	font-size: 24rpx;
	color: #666666;
	margin-left: 10rpx;
}

// 模态框样式
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 9999;
}

.modal-content {
	background-color: #ffffff;
	border-radius: 20rpx;
	padding: 40rpx;
	width: 560rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.loading-modal {
	width: 240rpx;
	height: 240rpx;
}

.loading-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	border: 6rpx solid #f3f3f3;
	border-top: 6rpx solid #47afff;
	animation: spin 1s linear infinite;
	margin-bottom: 30rpx;
}

.loading-text {
	font-size: 28rpx;
	color: #666666;
}

.result-icon {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 60rpx;
	font-weight: bold;
	margin-bottom: 30rpx;

	&.success {
		background-color: #e6f9eb;
		color: #4cd964;
	}

	&.error {
		background-color: #ffebeb;
		color: #ff3b30;
	}
}

.result-title {
	font-size: 36rpx;
	font-weight: bold;
	margin-bottom: 20rpx;
}

.result-message {
	font-size: 28rpx;
	color: #666666;
	text-align: center;
	margin-bottom: 40rpx;
}

.confirm-btn {
	width: 100%;
}

// Toast样式
.toast-container {
	position: fixed;
	bottom: 100rpx;
	left: 50%;
	transform: translateX(-50%);
	z-index: 9999;
}

.toast-message {
	padding: 20rpx 40rpx;
	background-color: rgba(0, 0, 0, 0.7);
	color: #ffffff;
	border-radius: 10rpx;
	font-size: 28rpx;

	&.error {
		background-color: rgba(255, 59, 48, 0.9);
	}

	&.warning {
		background-color: rgba(255, 204, 0, 0.9);
	}
}

// 动画
@keyframes spin {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}
</style>
