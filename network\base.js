let baseURL = '';
let baseURL_a = '';
// 是否在控制台显示接口请求日志,本地环境启用,打包环境禁用
let showHttpLog = false;
// 正式环境
baseURL = 'https://iot.hclight.com/api/';
baseURL_a = 'https://iot.hclight.com/api/';
// 测试环境
// baseURL = 'https://112.27.230.8:443/api';
// baseURL_a = 'https://112.27.230.8:443/api';
//开发环境
//baseURL_a = 'http://main-service.a1.luyouxia.net:25015/api';

// 孙魏环境
// baseURL_a = 'http://192.168.3.62:8080/api';
// baseURL = 'http://192.168.3.62:8080/api';
module.exports = {
	baseURL: baseURL,
	baseURL_a: baseURL_a,
	showHttpLog: showHttpLog
}
