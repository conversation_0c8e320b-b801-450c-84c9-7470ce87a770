<template>
	<view class="dispatch">
		<view class="head">
			<view class="head-body">
			<image style="height: 100rpx;width: 100rpx;margin-left: 40%;" src="@/static/product/fajing.png" mode="" v-if="devContent.equipType=='002'"></image>
			<image style="height: 100rpx;width: 100rpx;margin-left: 40%;" src="@/static/homePage/dmsIcon.png" mode="" v-else-if="devContent.equipType=='004'"></image>
			<image v-else-if="devContent.equipType=='001'" style="height: 100rpx;width: 100rpx;" src="@/static/product/home.png" mode="" ></image>
			<image style="height: 100rpx;width: 100rpx;margin-left: 40%;" src="@/static/product/gongshang.png" mode="" v-else-if="devContent.equipType=='003'"></image>
		    </view>
			<view class="head-text">
				
			<text  v-if="devContent.equipType=='001'">家用型可燃气体探测器</text>
			<text  v-else-if="devContent.equipType=='002'">地下空间燃气泄漏监测仪</text>
			<text  v-else-if="devContent.equipType=='003'">工商业可燃气体探测器</text>
			<text  v-else-if="devContent.equipType=='004'">地埋式燃气泄漏监测仪</text>
			    
				<view class="head-text2">
			<text>设备编号：{{devContent.code}}</text>
		        </view>
			</view>

		</view>
		
		<view class="body1">
			<view class="body1-0">
			<view class="body1-a">
				<text style="color: #98968d;">设备名称:</text>
				<text>{{devContent.name}}</text>
			</view>
			<view class="body1-a">
				<text style="color: #98968d;">设备浓度:</text>
				<text>{{devContent.chroma}}%LEL</text>
			</view>
			<view class="body1-a">
				<text style="color: #98968d;">故障状态:</text>
				<text>{{devContent.status}}</text>
			</view>
			<view class="body1-a">	
			     <text style="color: #98968d;">故障描述:</text>
			     <text>{{devContent.description}}</text>
			</view>
			<view class="body1-a">
				<text style="color: #98968d;">故障创建时间:</text>
				<text>{{devContent.createTime}}</text>
			</view>
			<view class="body1-a">
				<text style="color: #98968d;">设备安装地址:</text>
				<text>{{devContent.address}}</text>
			</view>
		    <view class="body1-a">
		    	<text style="color: #98968d;">故障上传照片:</text>
				<u-upload :file-list="fileList1"  name="1" disabled multiple :maxCount="3"
				:previewFullImage="true" :deletable='false' useBeforeRead></u-upload>
		    	<!-- <u-upload :file-list="fileList1"  name="1"  :maxCount="3"
		    	 :deletable='false' ></u-upload> -->
		    </view>
			<view class="body1-a">
				<text style="color: #98968d;">用户沟通电话:</text>
				<text @click="callPhone(devContent.mobile)" style="width: 100%;text-decoration:underline;font-style: italic;">{{devContent.mobile}}</text>
			</view>
			<view class="body1-a">
				<text style="color: #98968d;">用户统一标识号:</text>
				<text>{{devContent.userId}}</text>
			</view>
			</view>
		</view>
		      <view class="" style="margin-top: 70rpx;margin-bottom: 30rpx;">
			  <text v-if="devContent.status == '处理中' || devContent.status == '待处理'" style="font-size: 36rpx;margin-left: 300rpx;">故障处理</text>
			  <text v-if="devContent.status == '已处理'" style="font-size: 36rpx;margin-left: 300rpx;">处理结果</text>
		      </view>
		<view class="body2">
			<view v-if="devContent.status == '待处理'" class="form_view rightForm">
			<u--form labelPosition="left" :model="modelPatient" :rules="addRules" ref="addForm" labelWidth="120" :labelStyle="labelStyle">
				
				<u-form-item label="处理结果" prop="userInfo.description" borderBottom :required="true">
					<view style="width: 100%; height: auto">
						<textarea placeholder="请输入处理结果" style="width: 100%;" class="textarea1"  v-model="modelPatient.userInfo.description"  auto-height ></textarea>
					</view>	
				</u-form-item>
				
				<u-form-item label="上传照片"  borderBottom  :required="true">
					<u-upload :fileList="fileList2" @afterRead="afterRead" @delete="deletePic" name="1" multiple
						:maxCount="3" :previewFullImage="true"></u-upload>
				</u-form-item>
				<u-form-item>
					<u-button type="primary" text="提交" :disabled="disabled" :customStyle="primaryBtnCss"
						@click="submit"></u-button>
				</u-form-item>
			</u--form>
			</view>
			
			<view v-else-if="devContent.status == '处理中' && user == 'yw'" class="form_view rightForm">
			<u--form labelPosition="left" :model="modelPatient" :rules="addRules" ref="addForm" labelWidth="120" :labelStyle="labelStyle">
				
				<u-form-item label="处理结果" prop="userInfo.description" borderBottom :required="true">
					<view style="width: 100%; height: auto">
						<textarea placeholder="请输入处理结果" style="width: 100%;" class="textarea1"  v-model="modelPatient.userInfo.description"  auto-height ></textarea>
					</view>	
				</u-form-item>
				
				<u-form-item label="上传照片"  borderBottom  :required="true">
					<u-upload :fileList="fileList2" @afterRead="afterRead" @delete="deletePic" name="1" multiple
						:maxCount="3" :previewFullImage="true"></u-upload>
				</u-form-item>
				<u-form-item>
					<u-button type="primary" text="提交" :disabled="disabled" :customStyle="primaryBtnCss"
						@click="submit"></u-button>
				</u-form-item>
			</u--form>
			</view>
			
			<view v-else-if="devContent.status == '处理中' && user == 'admin'" class="form_view rightForm">
			<u--form labelPosition="left" :model="modelPatient" :rules="addRules" ref="addForm" labelWidth="120" :labelStyle="labelStyle">
				
				<u-form-item label="派遣运维人员姓名"   borderBottom :required="true" >
					<view style="width: 100%; height: auto">
						
						<textarea disabled v  style="width: 100%;" class="textarea1"    auto-height >{{devContent.ywName}}</textarea>
					</view>	
				</u-form-item>
				
				<u-form-item label="联系电话"   borderBottom :required="true" >
					<view style="width: 100%; height: auto">
						<!-- <view @click="callPhone(devContent.ywMobile)">{{devContent.ywMobile}}</view> -->
						
						<textarea @click="callPhone(devContent.ywMobile)" disabled   style="width: 100%;text-decoration:underline;font-style: italic;" class="textarea1"    auto-height >{{devContent.ywMobile}}</textarea>
					</view>	
				</u-form-item>
				
				<u-form-item label="处理结果" prop="userInfo.description" borderBottom :required="true">
					<view style="width: 100%; height: auto">
						<textarea placeholder="请输入处理结果" style="width: 100%;" class="textarea1"  v-model="modelPatient.userInfo.description"  auto-height ></textarea>
					</view>	
				</u-form-item>
				
				<u-form-item label="上传照片"  borderBottom  :required="true">
					<u-upload :fileList="fileList2" @afterRead="afterRead" @delete="deletePic" name="1" multiple
						:maxCount="3" :previewFullImage="true"></u-upload>
				</u-form-item>
				<u-form-item>
					<u-button type="primary" text="提交" :disabled="disabled" :customStyle="primaryBtnCss"
						@click="submit"></u-button>
				</u-form-item>
				
			</u--form>
			</view>
			
			<view v-else-if="devContent.status == '处理中' && user == 'pt'" class="form_view rightForm">
			<u--form labelPosition="left" :model="modelPatient" :rules="addRules" ref="addForm" labelWidth="120" :labelStyle="labelStyle">
				
				<!-- <u-form-item label="处理结果" borderBottom :required="true">
					<view style="width: 100%; height: auto">
						<textarea  style="width: 100%;" class="textarea1"   auto-height >处理中</textarea>
					</view>	
				</u-form-item> -->
				
				<u-form-item label="运维人员姓名"   borderBottom :required="true" >
					<view style="width: 100%; height: auto">
						
						<textarea disabled v  style="width: 100%;" class="textarea1"    auto-height >{{devContent.ywName}}</textarea>
					</view>	
				</u-form-item>
				
				<u-form-item label="联系电话"   borderBottom :required="true" >
					<view style="width: 100%; height: auto">
						<!-- <view border="bottom" @click="callPhone(devContent.ywMobile)">{{devContent.ywMobile}}</view> -->
						<textarea  disabled  border="bottom" @click="callPhone(devContent.ywMobile)" style="width: 100%;text-decoration:underline;font-style: italic;" class="textarea1" auto-height >
						{{devContent.ywMobile}}</textarea>
					</view>	
				</u-form-item>
			</u--form>
			</view>
			
			
			<view v-else-if="devContent.status == '已处理'" class="form_view rightForm">
			<u--form labelPosition="left" :model="modelPatient" :rules="addRules" ref="addForm" labelWidth="120" :labelStyle="labelStyle">
				
				<u-form-item label="处理结果" prop="userInfo.description" borderBottom :required="true" >
					<view style="width: 100%; height: auto">
						<textarea  v-if="devContent.status == '处理中' || devContent.status == '待处理'" placeholder="请输入处理结果" style="width: 100%;" class="textarea1"  v-model="modelPatient.userInfo.description"  auto-height ></textarea>
						<textarea disabled v-else-if="devContent.status == '已处理'"  style="width: 100%;" class="textarea1"  v-model="devContent.dealResult"  auto-height ></textarea>
					</view>	
				</u-form-item>
				
				<u-form-item label="处理时间" v-if="devContent.status == '已处理'"  borderBottom :required="true" >
					<view style="width: 100%; height: auto">
						
						<textarea disabled   style="width: 100%;" class="textarea1"  v-model="devContent.updateTime"  auto-height ></textarea>
					</view>	
				</u-form-item>
				
				<u-form-item label="处理照片"  borderBottom  :required="true">
					<u-upload :fileList="fileList3" name="1" disabled multiple :maxCount="3"
				:previewFullImage="true" :deletable='false' useBeforeRead></u-upload>
				</u-form-item>
				
				<u-form-item label="运维人员姓名"   borderBottom :required="true" >
					<view style="width: 100%; height: auto">
						
						<textarea disabled   style="width: 100%;" class="textarea1"    auto-height >{{devContent.ywName}}</textarea>
					</view>	
				</u-form-item>
				
				<u-form-item label="联系电话"   borderBottom :required="true" >
					<view style="width: 100%; height: auto">
						
						<textarea border="bottom" @click="callPhone(devContent.ywMobile)" disabled   style="width: 100%;text-decoration:underline;font-style: italic;" class="textarea1" auto-height >
						{{devContent.ywMobile}}</textarea>
						
						
					</view>	
				</u-form-item>
				
				<u-form-item>
					<u-button v-if="devContent.status == '处理中' || devContent.status == '待处理'" type="primary" text="提交" :disabled="disabled" :customStyle="primaryBtnCss"
						@click="submit"></u-button>
				</u-form-item>
			</u--form>
			</view>
			
			
		</view>
	</view>
</template>

<script>
	import {
		cstFault,
		updateDevice,
		Update
	} from "@/network/api.js"
	import {
		baseURL
	} from '@/network/base.js';
	
	export default {
		data() {
			return {
				user: uni.getStorageSync("userS"),
				devContent:{},
				fileList1: [],
				fileList2: [],
				fileList3: [],
				modelPatient: {
					userInfo: {
						name: '',
						equipDev: '',
						equipType: '',
						code: '',
						chroma: '',
						address: '',
						createTime: '',
						description: '',
						picUrl: "",
						type: '',
						mobile: '',
						userId: '',
						ywName: '',
						ywMobile: '',
					},
				},
				updateData: {
					dealPicUrl: '',
					dealResult: '',
					id: 0,
					status: 2,
				},
				addRules: {
					'userInfo.description': {
						type: 'string',
						required: true,
						message: '请输入处理结果',
						trigger: ['input','blur', 'change']
					},
				}
			}
		},
		methods: {
			onLoad(opt) { // 初始化
			this.devicDetail(opt.gzId)
			this.updateData.id = opt.gzId;
			console.log(this.user,'this.user')
			},
			callPhone(opt) {
				
				// uni.authorize({
				//               scope: 'scope.makePhoneCall',
				//               success: () => {
				               
				//               },
				// 			  })
				console.log(opt,'this.opt')
				uni.makePhoneCall({
							phoneNumber: opt
						})
				
			},
			devicDetail(id) {
				cstFault(id).then(res => {
						let resData = res.data;
						this.devContent = res.data
						
						if(res.data.status == '已处理')
						{
							if (resData.picUrl != '') {
								let picUrlArry = (resData.picUrl).split(',');
								let fileArry = [];
								for (let i = 0; i < picUrlArry.length; i++) {
									fileArry.push({
										url: picUrlArry[i]
									})
								}
								
								this.fileList1 = fileArry;
							}
							
							if (resData.dealPicUrl != '') {
								let picUrlArry = (resData.dealPicUrl).split(',');
								let fileArry = [];
								for (let i = 0; i < picUrlArry.length; i++) {
									fileArry.push({
										url: picUrlArry[i]
									})
								}
								
								this.fileList3 = fileArry;
							}
						}
						else 
						{
							if (resData.picUrl != '') {
								let picUrlArry = (resData.picUrl).split(',');
								let fileArry = [];
								for (let i = 0; i < picUrlArry.length; i++) {
									fileArry.push({
										url: picUrlArry[i]
									})
								}
								
								this.fileList1 = fileArry;
							}
						}
						
						
						
				})
			
			.catch((err) => {})
			},
			scanClock(){
				let that = this;
				uni.showLoading({
					title: '定位中..'
				})
				uni.getLocation({
					type: 'gcj02',
					success: function (res) {
						let URL = 'https://apis.map.qq.com/ws/geocoder/v1/?location=';
						let key = 'XMCBZ-RU5CU-3SXV7-GAPV5-BKZXZ-6WBM5';
						let getAddressUrl = URL + res.latitude + ',' + res.longitude + `&key=${key}`;
						uni.request({
							url: getAddressUrl,
							success: result => {
								let Res_Data = result.data.result;
								console.log("🚀 ~ Res_Data:", Res_Data.address)
								that.modelPatient.userInfo.address = Res_Data.address;
								uni.hideLoading();
							}
						});
					}
				});
			},
			submit() {
				this.$refs.addForm.validate().then(res => {
					if(this.fileList2.length > 0 )
					{
						
						let fileList = [];
						for (let i = 0; i < this.fileList2.length; i++) {
							let listUrl = JSON.parse(this.fileList2[i].url)
							fileList.push(listUrl.url)
						}
					 // uni.$u.toast('校验通过')
					 let form = this.updateData;
					 form.dealPicUrl = fileList.join(',');
					 form.dealResult = this.modelPatient.userInfo.description;
					 form.status = 2;
					 console.log(form,'提交form')
					 Update(form).then(res => {
						 
						 let pages = getCurrentPages();
						 let prevPage = pages[pages.length - 2];
						 let obj = 1;
						 setTimeout(()=>{
						 // prevPage.onShow(obj);
						  
					 		uni.navigateBack({
					 			delta: 1 ,// 默认值是1，表示返回的页面层数

                              success: () => {
								
                              	uni.$u.toast('提交成功')
                              }
					 		});
							
					 		
					 	})
					 	.catch((err) => {
					 		console.log("1212", err)
					 		
					 	})
						},1500)
					 
					 }
					 else
					 {
						 uni.$u.toast('请上传处理图片')
					 }
					// this.showModal = true;
					//this.modalConfirm()
				}).catch(errors => {
					 uni.$u.toast('校验失败')
				})
				},
			// 新增图片
			async afterRead(event) {
				// 当设置 mutiple 为 true 时, file 为数组格式，否则为对象格式
				console.log("event", event)
				console.log("event22", this.fileList2)
				let lists = [].concat(event.file)
				let fileListLen = this.fileList2.length
				lists.map((item) => {
					this.fileList2.push({
						...item,
						status: 'uploading',
						message: '上传中'
					})
				})
				for (let i = 0; i < lists.length; i++) {
					const result = await this.uploadFilePromise(lists[i].url)
					let item = this.fileList2[fileListLen]
					this.fileList2.splice(fileListLen, 1, Object.assign(item, {
						status: 'success',
						message: '',
						url: result
					}))
					fileListLen++
				}
				this.$refs.addForm.validateField('userInfo.picUrl')
			},
			// 删除图片
			deletePic(event) {
				this.fileList2.splice(event.index, 1)
			},
			uploadFilePromise(url) {
				return new Promise((resolve, reject) => {
					let a = uni.uploadFile({
						url: baseURL + '/common/upload', // 仅为示例，非真实的接口地址
						filePath: url,
						name: 'file',
						// formData: {
						// 	user: 'test'
						// },
						success: (res) => {
							console.log("iiii", res, typeof(res.data))
							setTimeout(() => {
								resolve(res.data)
							}, 1000)
						}
					});
				})
			},
			
		}
	}
</script>
<style>
	page {
		background-color: #F6F6F6;
	}
</style>
<style lang="scss" scoped>

.dispatch{
	.head{
		background-color: #ffffff;
		margin-top: 20rpx;
		width: 690rpx;
		margin-left: 30rpx;
		height: 120rpx;
		display: flex;
		align-items: center;
		text-align: center; 
		
		.head-body{
			
		}
		.head-text {
			width: 100%;
			display: flex;
			flex-direction: column;
			align-items: center;
			text-align: center; 
			.head-text1 {

				}
			.head-text2 {	
				// text{
				// 	color: #a9d9ff;
				// }
				}
		}
	}

     .body1{
		 
		 width: 700rpx;
		 background-color: #ffffff;
		 margin-top: 20rpx;
		 margin-left: 25rpx;
		
		 
		 .body1-0{
			 border-radius: 32rpx;
			 padding: 1.5%;
			 margin: 10rpx 0;
			 display: flex;
			 flex-direction: column;
		 .body1-a {
			display: grid;
			grid-template-columns: 37% 63%;
			
		 }
		
		 }
	 }
	 
	 .body2 {
		 background-color: #ffffff;
		 width: 700rpx;
		 margin-left: 25rpx;
		 // padding: 0 0 100rpx;
		 textarea::placeholder {
			 text-align:right;
		 }
	 }
	 
	placeholder {
		 text-align:right;
	}

}
</style>
