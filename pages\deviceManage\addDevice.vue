<template>
	<!-- 添加设备 -->
	<view class="addDeviceInform">
		<view class="addHead">
			
				
			
			<image  src="@/static/product/fajing.png" mode="" v-if="modelAdd.userInfo.equipType2=='002'"></image>
			<image  src="@/static/homePage/dmsIcon.png" mode="" v-else-if="modelAdd.userInfo.equipType2=='004'"></image>
			<image  src="@/static/product/home.png" mode="" v-else-if="modelAdd.userInfo.equipType2=='001'"></image>
			<image  src="@/static/product/gongshang.png" mode="" v-else-if="modelAdd.userInfo.equipType2=='003'"></image>
		     
		</view>
		<view class="form_view rightForm">
			<u--form labelPosition="left" :model="modelAdd" :rules="addRules" ref="addForm" label-width="250rpx">
				<u-form-item  label="设备类型"  prop="userInfo.equipType" borderBottom :required="true"
					@click="typeShow = true;" >
						<u--input v-model="modelAdd.userInfo.equipType" disabled disabledColor="#ffffff"
						placeholder="请选择设备类型" border="none" inputAlign="right"></u--input>
					<u-icon slot="right" name="arrow-right"></u-icon>
				</u-form-item>
				<u-form-item label="所属场景" prop="userInfo.sceneId" borderBottom :required="true"
					@click="sceneShow = true;">
					<u--input v-model="modelAdd.userInfo.sceneId" disabled disabledColor="#ffffff" inputAlign="right"
						border="none" placeholder="请选择所属场景"></u--input>
					<u-icon slot="right" name="arrow-right"></u-icon>
				</u-form-item>
				<u-form-item  label="设备名称" prop="userInfo.name" borderBottom :required="true">
					<u--input  v-model="modelAdd.userInfo.name" maxlength="15" border="none" placeholder="请输入设备名称"
						inputAlign="right"></u--input>
						
				</u-form-item>
				<u-form-item label="设备编号" prop="userInfo.code" borderBottom :required="true">
					<u--input v-model="modelAdd.userInfo.code" maxlength="30" border="none" placeholder="请输入设备编号"
						inputAlign="right">
					</u--input>
					<u-icon name="scan" color="#2979ff" slot="right" size="25" @click="getScanCode()" ></u-icon>
				</u-form-item>
				<u-form-item label="省" prop="userInfo.province" borderBottom>
					<u--input v-model="modelAdd.userInfo.province" maxlength="30" border="none" placeholder="请输入省"
						inputAlign="right"></u--input>
					<u-icon slot="right" name="map" color="#2979ff" size="25" @click="scanClock()"></u-icon>
				</u-form-item>
				<u-form-item label="市" prop="userInfo.city" borderBottom>
					<u--input v-model="modelAdd.userInfo.city" maxlength="30" border="none" placeholder="请输入市"
						inputAlign="right"></u--input>
				</u-form-item>
				<u-form-item label="区" prop="userInfo.area" borderBottom>
					<u--input v-model="modelAdd.userInfo.area" maxlength="30" border="none" placeholder="请输入区"
						inputAlign="right"></u--input>
				</u-form-item>
				<u-form-item label="街道" prop="userInfo.road" borderBottom>
					<u--input v-model="modelAdd.userInfo.road" maxlength="30" border="none" placeholder="请输入街道"
						inputAlign="right"></u--input>
				</u-form-item>
				<u-form-item label="小区" prop="userInfo.neighbourhood" borderBottom>
					<u--input v-model="modelAdd.userInfo.neighbourhood" maxlength="30" border="none" placeholder="请输入小区"
						inputAlign="right"></u--input>
				</u-form-item>
				<u-form-item label="门牌" prop="userInfo.room" borderBottom>
					<u--input v-model="modelAdd.userInfo.room" maxlength="30" border="none" placeholder="请输入门牌"
						inputAlign="right"></u--input>
				</u-form-item>
				<u-form-item label="报警浓度(%LEL)" prop="userInfo.chromaMax" borderBottom>
					<u--input v-model="modelAdd.userInfo.chromaMax" maxlength="30" border="none" placeholder="请输入最大报警浓度"
						inputAlign="right"></u--input>
				</u-form-item>
				<!-- <u-form-item label="最小报警浓度(%LEL)" prop="userInfo.chromaMin" borderBottom>
					<u--input v-model="modelAdd.userInfo.chromaMin" maxlength="30" border="none" placeholder="请输入最小报警浓度"
						inputAlign="right"></u--input>
				</u-form-item>
				<u-form-item label="最大报警温度(℃)" prop="userInfo.tempMax" borderBottom>
					<u--input v-model="modelAdd.userInfo.tempMax" maxlength="30" border="none" placeholder="请输入最大报警温度"
						inputAlign="right"></u--input>
				</u-form-item>
				<u-form-item label="最小报警温度(℃)" prop="userInfo.tempMin" borderBottom>
					<u--input v-model="modelAdd.userInfo.tempMin" maxlength="30" border="none" placeholder="请输入最小报警温度"
						inputAlign="right"></u--input>
				</u-form-item> -->
				<u-form-item>
					<u-button type="primary" text="添加" :disabled="disabled" :customStyle="primaryBtnCss"
						@click="submit"></u-button>
				</u-form-item>
			</u--form>
			<u-picker itemHeight="60" :show="sceneShow" :columns="sceneColumns" keyName="sceneName" @confirm="sceneConfirm"
				@cancel="sceneCancel"></u-picker>
			<u-picker itemHeight="60" :show="typeShow" :columns="typeColumns" keyName="name" @confirm="typeConfirm"
				@cancel="dgfsCancel"></u-picker>
			<!-- <view class="btnXFView">
				<u-button type="primary" text="添加" :disabled="disabled" :customStyle="primaryBtnCss"
					@click="submit"></u-button>
			</view> -->
			<u-modal title="提示" :content="content" :show="showModal" @confirm="modalConfirm"></u-modal>
		</view>
	</view>
</template>

<script>
	import {
		sceneListAll,
		addDevice,
		QueryBind
	} from "@/network/api.js"
	export default {
		data() {
			return {
				content: '当前无所属场景数据，请前往添加',
				showModal: false,
				tabsLocation: 0,
				formEndArry: [],
				primaryBtnCss: {
					width: '574rpx',
					height: '88rpx',
					background: '#0165FC',
					boxShadow: '0rpx 4rpx 24rpx 0rpx rgba(54,150,255,0.4)',
					borderRadius: '200rpx',
					fontSize: '28rpx',
					fontFamily: 'Microsoft YaHei UI-Regular, Microsoft YaHei UI',
					color: '#FFFFFF',
					lineHeight: '28rpx'
				},
				disabled: false,
				labelStyle: {
					// fontSize: '28rpx',
					// fontFamily: 'Microsoft YaHei UI-Regular, Microsoft YaHei UI',
					// lineHeight: '28rpx'
				},
				sceneShow: false,
				sceneColumns: [],
				typeShow: false,
				typeColumns: [
					[{
						name: '家用型可燃气体探测器',
							id: '001'
						},
						{
							name: '地下空间燃气泄漏监测仪',
							id: '002'
						},
						{
							name: '工商业可燃气体探测器',
							id: '003'
						},
						{
							name: '地埋式燃气泄漏监测仪',
							id: '004'
						}
					]
				],
				modelAdd: {
					userInfo: {
						sceneId: '',
						name: '',
						equipType: '',
						code: '',
						address: '',
						province: '', //省
						city: '', //市
						area: '', //区
						road: '', //街道
						neighbourhood: '', //小区
						room: '', //门牌
						chromaMax: '', //最大报警浓度
						chromaMin: '0', //最小报警浓度
						tempMax: '50', //最大报警温度
						tempMin: '0', //最小报警温度
					},
					phone: '',
				},
				addRules: {
					'userInfo.sceneId': {
						type: 'string',
						required: true,
						message: '请选择所属场景',
						trigger: ['blur', 'change']
					},
					'userInfo.name': {
						type: 'string',
						required: true,
						message: '请输入设备名称',
						trigger: ['blur',  'change']
					},
					'userInfo.equipType': {
						type: 'string',
						required: true,
						message: '请选择设备类型',
						trigger: ['blur', 'change']
					},
					// 'userInfo.code': {
					// 	type: 'string',
					// 	required: true,
					// 	message: '请输入设备编号',
					// 	trigger: [ 'change']
					// },
					'userInfo.address': {
						type: 'string',
						required: false,
						message: '请输入地址',
						trigger: ['blur', 'change']
					}
				}
			};
		},
		onLoad(opt) { // 初始化
			this.getConfigsList();
			this.phone = uni.getStorageSync('userPhone');
			// uni.showLoading({
			// 	title: opt.id,
			// });
		},
		methods: {
			getConfigsList() {
				let query = {
					"userType": uni.getStorageSync('userType'), // 用户类型
					"id": uni.getStorageSync('userId') // 用户ID
				}
				sceneListAll(query).then(res => {
						this.sceneColumns = [];
						this.sceneColumns.push(res.data);
					})
					.catch((err) => {})
			},
			sceneConfirm(e) {
				console.log('eeee', e)
				this.modelAdd.userInfo.sceneId = e.value[0].sceneName
				this.modelAdd.userInfo.sceneId2 = e.value[0].id
				this.$refs.addForm.validateField('userInfo.sceneId')
				this.sceneShow = false;
			},
			sceneCancel() {
				this.sceneShow = false;
			},

			typeConfirm(e) {
				console.log("1212", e)
				this.modelAdd.userInfo.equipType = e.value[0].name
				this.modelAdd.userInfo.equipType2 = e.value[0].id
				this.$refs.addForm.validateField('userInfo.equipType')
				this.typeShow = false;
				if (e.value[0].id == '002') 
				{
					this.modelAdd.userInfo.chromaMax = '3'
					this.modelAdd.userInfo.name = '地下空间报警器'
				}else if(e.value[0].id == '001'){
					this.modelAdd.userInfo.chromaMax = '8'
					this.modelAdd.userInfo.name = '家用报警器'
				}
				else if(e.value[0].id == '004'){
					this.modelAdd.userInfo.chromaMax = '3'
					this.modelAdd.userInfo.name = '地埋式报警器'
				}
				else{
					this.modelAdd.userInfo.chromaMax = '10'
					this.modelAdd.userInfo.name = '工商报警器'
				}
			},
			dgfsCancel() {
				this.typeShow = false;
			},
			submit() {
				console.log(this.modelAdd.userInfo.equipType);
				
				if(this.modelAdd.userInfo.equipType == '家用型可燃气体探测器')
				{
				if(this.modelAdd.userInfo.chromaMax <= 0 || this.modelAdd.userInfo.chromaMax > 8)
				{
					
					 uni.showModal({
					     title: '警告',
					     content: '家用最大报警浓度范围在0%LEL~8%LEL',
					     success: function (res) {
					       if (res.confirm) {//这里是点击了确定以后
					         console.log('用户点击确定')
					       } else {//这里是点击了取消以后
					         console.log('用户点击取消')
					       }
					     }
					   });
					   this.modelAdd.userInfo.chromaMax = 8

				}
				else{
				this.$refs.addForm.validate().then(res => {
					// uni.$u.toast('校验通过')
					// this.showModal = true;
					this.modalConfirm()
				}).catch(errors => {
					// uni.$u.toast('校验失败')
				})
				}
				}
				else if(this.modelAdd.userInfo.equipType == '工商业可燃气体探测器')
				{
				if(this.modelAdd.userInfo.chromaMax <= 0 || this.modelAdd.userInfo.chromaMax > 10)
				{
					
					 uni.showModal({
					     title: '警告',
					     content: '工商最大报警浓度范围在0%LEL~10%LEL',
					     success: function (res) {
					       if (res.confirm) {//这里是点击了确定以后
					         console.log('用户点击确定')
					       } else {//这里是点击了取消以后
					         console.log('用户点击取消')
					       }
					     }
					   });
					   this.modelAdd.userInfo.chromaMax = 10

				}
				else{
				this.$refs.addForm.validate().then(res => {
					// uni.$u.toast('校验通过')
					// this.showModal = true;
					this.modalConfirm()
				}).catch(errors => {
					// uni.$u.toast('校验失败')
				})
				}
				}
				else{
					if(this.modelAdd.userInfo.chromaMax <= 0 || this.modelAdd.userInfo.chromaMax > 3)
					{
						
						 uni.showModal({
						     title: '警告',
						     content: '该报警器最大报警浓度范围在0%LEL~3%LEL',
						     success: function (res) {
						       if (res.confirm) {//这里是点击了确定以后
						         console.log('用户点击确定')
						       } else {//这里是点击了取消以后
						         console.log('用户点击取消')
						       }
						     }
						   });
						   this.modelAdd.userInfo.chromaMax = 3
					
					}
					else{
					this.$refs.addForm.validate().then(res => {
						// uni.$u.toast('校验通过')
						// this.showModal = true;
						this.modalConfirm()
					}).catch(errors => {
						// uni.$u.toast('校验失败')
					})
				}
				
			}
			},
			modalConfirm() {
				
				let query = {
					code: this.modelAdd.userInfo.code,
					type: this.modelAdd.userInfo.equipType2
				}
				QueryBind(query).then(res=> {

				uni.showLoading({
					title: '请求中..'
				})
				let form = {
					customerId: uni.getStorageSync('userId'),
					token: uni.getStorageSync('token'),
					name: this.modelAdd.userInfo.name,
					sceneId: this.modelAdd.userInfo.sceneId2,
					equipType: this.modelAdd.userInfo.equipType2,
					code: this.modelAdd.userInfo.code,

					province: this.modelAdd.userInfo.province, //省
					city: this.modelAdd.userInfo.city, //市
					area: this.modelAdd.userInfo.area, //区
					road: this.modelAdd.userInfo.road, //街道
					neighbourhood: this.modelAdd.userInfo.neighbourhood, //小区
					room: this.modelAdd.userInfo.room, //门牌
					chromaMax: this.modelAdd.userInfo.chromaMax, //最大报警浓度
					chromaMin: this.modelAdd.userInfo.chromaMin, //最小报警浓度
					tempMax: this.modelAdd.userInfo.tempMax, //最大报警温度
					tempMin: this.modelAdd.userInfo.tempMin, //最小报警温度
					phone: this.phone,
				}
				console.log('form', form);
				addDevice(form).then(res => {
						uni.switchTab({
							url: "/pages/homePage/homePage"
						})
						uni.hideLoading();
					})
					.catch((err) => {
						console.log("1212", err)
						uni.hideLoading();
					})
					}).catch((err) => {
						
						uni.hideLoading();
					})
			},
			ADDdevice(){
				
			},
			modalCancel() {
				this.showModal = false
				console.log('cancel');
			},
			modalClose() {
				this.showModal = false
				console.log('close');
			},
			scanClock(){
				let that = this;
				uni.showLoading({
					title: '定位中..'
				})
				uni.getLocation({
					type: 'gcj02',
					success: function (res) {
						let URL = 'https://apis.map.qq.com/ws/geocoder/v1/?location=';
						let key = 'XMCBZ-RU5CU-3SXV7-GAPV5-BKZXZ-6WBM5';
						let getAddressUrl = URL + res.latitude + ',' + res.longitude + `&key=${key}`;
						uni.request({
							url: getAddressUrl,
							success: result => {
								let Res_Data = result.data.result;
								console.log("🚀 ~ Res_Data:", Res_Data)
								that.modelAdd.userInfo.province = Res_Data.address_component.province;
								that.modelAdd.userInfo.city = Res_Data.address_component.city;
								that.modelAdd.userInfo.area = Res_Data.address_component.district;
								that.modelAdd.userInfo.road = Res_Data.address_component.street;
								that.modelAdd.userInfo.neighbourhood = Res_Data.address_component.street_number;
								uni.hideLoading();
							},
							fail: function (res) {
								console.log("🚀 1", res)
							}
						});
							
					},
					fail: function (res) {
						console.log("🚀 2", res)
					}
				});
			},
			 getScanCode(){
				uni.scanCode({
					onlyFromCamera: true,
					success: res => {
						// uni.showLoading({
						// 	title: res.result,
						// });
						
						
						
						this.modelAdd.userInfo.code = res.result;
						// uni.showLoading({
						// 	title: res.result,
						// });        

						
					}
				});
			}
			
		},
		onReady() {
			//如果需要兼容微信小程序，并且校验规则中含有方法等，只能通过setRules方法设置规则。
			this.$refs.addForm.setRules(this.addRules)
		},
	};
</script>

<style lang="scss" scoped>
	.addDeviceInform {
		padding: 0 0 160rpx;
        
		.addHead {
			display: flex;
			justify-content: center;	
				
			text-align: center;
			margin: 0rpx 0;
			
			
          
			image {
				
				width: 400rpx;
				height: 400rpx;
			
			}
		}

		.form_view {
			padding-top: 0;
		}
		.type_item{
			width: 150rpx !important;
		}
	}
</style>