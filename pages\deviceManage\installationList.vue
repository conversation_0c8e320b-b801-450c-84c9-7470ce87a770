<template>
	<view class="installation-list">
		<!-- <view class="header">
			<view class="title">设备安装工单</view>
		</view> -->
		
		<!-- Tab切换 -->
		<view class="tab-container">
			<view class="tab-item" :class="{ active: activeTab === 0 }" @click="switchTab(0)">
				<text>未完结</text>
			</view>
			<view class="tab-item" :class="{ active: activeTab === 1 }" @click="switchTab(1)">
				<text>已完结</text>
			</view>
		</view>
		
		<!-- 工单列表 -->
		<view class="list-container">
			<view v-if="installationList.length > 0">
				<view class="list-item" v-for="(item, index) in installationList" :key="index">
					<view class="item-header">
						<text class="item-title">{{item.missionName}}</text>
						<view class="detail-btn" @click="showDeviceDetail(item)">详情</view>
					</view>
					<view class="item-content">
						<view class="info-row">
							<text class="info-label">安装设备类型：</text>
							<text class="info-value">{{item.productName}}</text>
						</view>
						<view class="info-row">
							<text class="info-label">安装位置：</text>
							<text class="info-value">{{item.pointTypeName}}</text>
						</view>
						<view class="info-row">
							<text class="info-label">设备总数：</text>
							<text class="info-value">{{item.deviceNum}}</text>
						</view>
						<view class="info-row">
							<text class="info-label">已安装数：</text>
							<text class="info-value">{{item.installedNum}}</text>
						</view>
						<view class="info-row">
							<text class="info-label">未安装数：</text>
							<text class="info-value">{{item.deviceNum - item.installedNum}}</text>
						</view>
						<view class="info-row">
							<text class="info-label">创建时间：</text>
							<text class="info-value">{{item.createTime ? item.createTime.substring(0, 10) : ''}}</text>
						</view>
						<view class="info-row">
							<text class="info-label">截止时间：</text>
							<text class="info-value">{{item.endTime}}</text>
						</view>
						<view class="info-row">
							<text class="info-label">安装人员：</text>
							<text class="info-value">{{item.installUserName}}</text>
						</view>
					</view>
				</view>
			</view>
			<view v-else class="empty-list">
				<image src="@/static/image/暂无设备.png" class="empty-image"></image>
				<text class="empty-text">暂无工单</text>
			</view>
		</view>
		

		
		<!-- 地图导航确认弹窗 -->
		<view class="map-confirm-mask" v-if="showMapConfirm" @click="closeMapConfirm"></view>
		<view class="map-confirm-container" v-if="showMapConfirm">
			<view class="map-confirm-title">导航确认</view>
			<view class="map-confirm-content">
				<text>请选择导航应用</text>
				<view class="map-options">
					<view class="map-option" @click="openNavigation('baidu')">
						<text>百度地图</text>
					</view>
					<view class="map-option" @click="openNavigation('gaode')">
						<text>高德地图</text>
					</view>
				</view>
			</view>
			<view class="map-confirm-footer">
				<view class="map-cancel-btn" @click="closeMapConfirm">取消</view>
			</view>
		</view>
		
		<!-- 加载提示 -->
		<u-toast ref="uToast"></u-toast>
		<u-loading-icon :show="loading" mode="circle"></u-loading-icon>
	</view>
</template>

<script>
	import { installationList, installationTaskList } from "@/network/api.js";
	
	export default {
		data() {
			return {
				activeTab: 0, // 默认显示未完结工单
				installationList: [],
				loading: false,
				customerId: "",

				showMapConfirm: false, // 控制地图导航确认弹窗显示
				currentDevice: null, // 当前选中的设备
			};
		},
		onLoad() {
			this.customerId = uni.getStorageSync('userId');
			this.getInstallationList();
		},
		methods: {
			// 切换Tab
			switchTab(index) {
				if (this.activeTab !== index) {
					this.activeTab = index;
					this.getInstallationList();
				}
			},
			
			// 获取工单列表
			getInstallationList() {
				this.loading = true;
				const params = {
					customerId: this.customerId,
					missionStatus: this.activeTab // 0表示未完结，1表示已完结
				};
				
				installationList(params)
					.then(res => {
						console.log('工单列表:', res);
						if (res.code === 200) {
							this.installationList = res.data || [];
						} else {
							this.$refs.uToast.show({
								message: res.msg || '获取工单列表失败',
								position: 'bottom'
							});
							this.installationList = [];
						}
					})
					.catch(err => {
						console.error('获取工单列表错误:', err);
						this.$refs.uToast.show({
							message: '网络错误，请稍后重试',
							position: 'bottom'
						});
						this.installationList = [];
					})
					.finally(() => {
						this.loading = false;
					});
			},
			
			// 显示设备详情
			showDeviceDetail(item) {
				// 跳转到设备安装点列表页
				uni.navigateTo({
					url: `/pages/deviceManage/installationPointList?missionId=${item.missionId}&missionName=${encodeURIComponent(item.missionName)}&productName=${encodeURIComponent(item.productName)}&pointTypeName=${encodeURIComponent(item.pointTypeName)}&deviceNum=${item.deviceNum}&installedNum=${item.installedNum}&createTime=${encodeURIComponent(item.createTime || '')}`
				});
			},
			

			
			// 打开地图选择弹窗
			openMap(device) {
				if (!device.pointLat || !device.pointLon) {
					this.$refs.uToast.show({
						message: '该设备没有位置信息，无法导航',
						position: 'bottom'
					});
					return;
				}
				
				this.currentDevice = device;
				this.showMapConfirm = true;
			},
			
			// 关闭地图导航确认弹窗
			closeMapConfirm() {
				this.showMapConfirm = false;
				this.currentDevice = null;
			},
			
			// 打开导航应用
			openNavigation(mapType) {
				if (!this.currentDevice || !this.currentDevice.pointLat || !this.currentDevice.pointLon) {
					this.$refs.uToast.show({
						message: '导航信息不完整',
						position: 'bottom'
					});
					return;
				}
				
				const latitude = this.currentDevice.pointLat;
				const longitude = this.currentDevice.pointLon;
				const name = this.currentDevice.pointName || '安装位置';
				
				// 检查当前运行环境
				// #ifdef APP-PLUS
				// App环境下检查权限
				uni.getSystemInfo({
					success: (sysInfo) => {
						if (sysInfo.platform === 'android') {
							// Android平台需要检查权限
							const permissions = ['android.permission.ACCESS_FINE_LOCATION'];
							
							plus.android.requestPermissions(
								permissions,
								(result) => {
									// 检查权限是否获取成功
									const hasPermission = result.granted && result.granted.length > 0;
									
									if (hasPermission) {
										this.navigateToMap(mapType, latitude, longitude, name);
									} else {
										this.$refs.uToast.show({
											message: '请授予位置权限以使用导航功能',
											position: 'bottom'
										});
									}
								},
								(error) => {
									console.error('权限申请失败:', error);
									this.$refs.uToast.show({
										message: '权限申请失败',
										position: 'bottom'
									});
								}
							);
						} else {
							// iOS平台直接导航
							this.navigateToMap(mapType, latitude, longitude, name);
						}
					}
				});
				// #endif
				
				// #ifdef MP-WEIXIN
				// 微信小程序环境下直接导航
				this.navigateToMap(mapType, latitude, longitude, name);
				// #endif
				
				this.closeMapConfirm();
			},
			
			// 导航到地图应用
			navigateToMap(mapType, latitude, longitude, name) {
				// #ifdef APP-PLUS
				// App环境下使用plus API
				let url = '';
				
				if (mapType === 'baidu') {
					// 百度地图URL Scheme
					url = `baidumap://map/direction?destination=${latitude},${longitude}&destinationName=${encodeURIComponent(name)}&coord_type=gcj02&mode=driving`;
				} else if (mapType === 'gaode') {
					// 高德地图URL Scheme
					url = `androidamap://navi?sourceApplication=安装工单&lat=${latitude}&lon=${longitude}&dev=0&style=2`;
				}
				
				// 尝试打开地图应用
				plus.runtime.openURL(url, (err) => {
					if (err) {
						// 打开失败，提示用户安装对应的地图应用
						let appName = mapType === 'baidu' ? '百度地图' : '高德地图';
						let appUrl = mapType === 'baidu' ? 
							'https://apps.apple.com/cn/app/id452186370' : 
							'https://apps.apple.com/cn/app/id461703208';
						
						uni.showModal({
							title: '提示',
							content: `您尚未安装${appName}，是否前往下载？`,
							success: (res) => {
								if (res.confirm) {
									plus.runtime.openURL(appUrl);
								}
							}
						});
					}
				});
				// #endif
				
				// #ifdef MP-WEIXIN
				// 微信小程序环境下使用内置地图导航
				wx.openLocation({
					latitude: parseFloat(latitude),
					longitude: parseFloat(longitude),
					name: name,
					address: name,
					scale: 18,
					success: () => {
						console.log('导航成功');
					},
					fail: (err) => {
						console.error('导航失败:', err);
						this.$refs.uToast.show({
							message: '导航失败，请稍后重试',
							position: 'bottom'
						});
					}
				});
				// #endif
			}
		}
	};
</script>

<style lang="scss" scoped>
.installation-list {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
	
	.header {
		padding: 20rpx 0;
		
		.title {
			font-size: 36rpx;
			font-weight: bold;
			color: #333;
		}
	}
	
	.tab-container {
		display: flex;
		background-color: #fff;
		border-radius: 10rpx;
		margin-bottom: 20rpx;
		overflow: hidden;
		
		.tab-item {
			flex: 1;
			height: 80rpx;
			line-height: 80rpx;
			text-align: center;
			font-size: 28rpx;
			color: #666;
			position: relative;
			
			&.active {
				color: #47afff;
				font-weight: bold;
				
				&::after {
					content: '';
					position: absolute;
					bottom: 0;
					left: 50%;
					transform: translateX(-50%);
					width: 60rpx;
					height: 4rpx;
					background-color: #47afff;
					border-radius: 2rpx;
				}
			}
		}
	}
	
	.list-container {
		padding-bottom: 30rpx;
		
		.list-item {
			background-color: #fff;
			border-radius: 10rpx;
			margin-bottom: 20rpx;
			overflow: hidden;
			box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
			
			.item-header {
				padding: 20rpx;
				border-bottom: 1rpx solid #eee;
				display: flex;
				justify-content: space-between;
				align-items: center;
				
				.item-title {
					font-size: 32rpx;
					font-weight: bold;
					color: #333;
				}
				
				.detail-btn {
					padding: 6rpx 20rpx;
					background-color: #47afff;
					color: #fff;
					font-size: 24rpx;
					border-radius: 30rpx;
					box-shadow: 0 2rpx 6rpx rgba(71, 175, 255, 0.3);
				}
			}
			
			.item-content {
				padding: 20rpx;
				
				.info-row {
					display: flex;
					margin-bottom: 15rpx;
					
					&:last-child {
						margin-bottom: 0;
					}
					
					.info-label {
						width: 200rpx;
						font-size: 28rpx;
						color: #666;
						font-weight: bold;
					}
					
					.info-value {
						flex: 1;
						font-size: 28rpx;
						color: #333;
					}
				}
			}
		}
		
		.empty-list {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			padding: 100rpx 0;
			
			.empty-image {
				width: 200rpx;
				height: 200rpx;
				margin-bottom: 20rpx;
			}
			
			.empty-text {
				font-size: 28rpx;
				color: #999;
			}
		}
	}
	
	// 弹窗遮罩层
	.popup-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 999;
	}
	
	// 设备详情弹窗
	.popup-container {
		position: fixed;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		width: 90%;
		max-height: 80vh;
		background-color: #fff;
		border-radius: 12rpx;
		z-index: 1000;
		overflow: hidden;
		display: flex;
		flex-direction: column;
		
		.popup-header {
			padding: 30rpx;
			border-bottom: 1rpx solid #eee;
			display: flex;
			justify-content: space-between;
			align-items: center;
			
			.popup-title {
				font-size: 32rpx;
				font-weight: bold;
				color: #333;
			}
			
			.close-btn {
				font-size: 40rpx;
				color: #999;
				padding: 0 10rpx;
			}
		}
		
		.popup-content {
			padding: 30rpx;
			max-height: 60vh;
			overflow-y: auto;
			
			.device-item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 20rpx 0;
				border-bottom: 1rpx solid #f5f5f5;
				
				.device-info {
					flex: 1;
					
					.device-name {
						font-size: 28rpx;
						color: #333;
						margin-bottom: 10rpx;
					}
					
					.device-status {
						font-size: 24rpx;
						padding: 4rpx 12rpx;
						border-radius: 20rpx;
						display: inline-block;
						
						&.installed {
							background-color: #e8f7ee;
							color: #07c160;
						}
						
						&.uninstalled {
							background-color: #f7f7f7;
							color: #999;
						}
					}
				}
				
				.nav-icon {
					width: 60rpx;
					height: 60rpx;
					display: flex;
					justify-content: center;
					align-items: center;
					
					.nav-image {
						width: 40rpx;
						height: 40rpx;
					}
					
					.nav-svg-icon {
						width: 40rpx;
						height: 40rpx;
					}
				}
			}
			
			.empty-device-list {
				padding: 50rpx 0;
				text-align: center;
				color: #999;
				font-size: 28rpx;
			}
		}
	}
	
	// 地图导航确认弹窗
	.map-confirm-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 1001;
	}
	
	.map-confirm-container {
		position: fixed;
		left: 50%;
		bottom: 0;
		transform: translateX(-50%);
		width: 100%;
		background-color: #fff;
		border-radius: 20rpx 20rpx 0 0;
		z-index: 1002;
		overflow: hidden;
		
		.map-confirm-title {
			padding: 30rpx;
			text-align: center;
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
			border-bottom: 1rpx solid #eee;
		}
		
		.map-confirm-content {
			padding: 30rpx;
			
			text {
				display: block;
				text-align: center;
				margin-bottom: 30rpx;
				color: #666;
				font-size: 28rpx;
			}
			
			.map-options {
				display: flex;
				justify-content: space-around;
				
				.map-option {
					padding: 20rpx 40rpx;
					background-color: #f5f5f5;
					border-radius: 10rpx;
					text-align: center;
					
					text {
						margin-bottom: 0;
						color: #333;
						font-size: 28rpx;
					}
				}
			}
		}
		
		.map-confirm-footer {
			padding: 20rpx 30rpx 50rpx;
			
			.map-cancel-btn {
				text-align: center;
				padding: 20rpx 0;
				color: #999;
				font-size: 28rpx;
			}
		}
	}
}
</style>