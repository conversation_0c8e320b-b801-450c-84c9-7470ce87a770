<template>
	<!-- 我的设备 -->
	<view class="myDeviceInform">
		<view class="topTabs">
			<u-sticky>
				<u-tabs :list="conduitList" keyName="sceneName" :current="tabsLocation" :activeStyle="{color: '#0165FC',fontWeight: 'bold'}"
					itemStyle="padding-left: 21rpx; padding-right: 21rpx; height: 86rpx;" @click="cutTabs"></u-tabs>
			</u-sticky>
		</view>


		<view class="devList">
			<view class="devView" v-for="(item,index) in deviceList" :key="index" @click="goDevicePage(item)"
				@longpress="devLongClick(item)">
				<!-- code 编号   status  状态 00正常 01光路故障  02温度获取异常  03温控故障   equipType 类型 -->
				<view class="dev_head" >
					<text class='numCode'>{{item.name}}</text>
					<view class="moreButton" @tap.stop = "moreWays(item)">
						<image src="@/static/image/更多1.png" mode="" ></image>
					</view>
					<!-- <text>{{item.status}}</text> -->
				</view>
				<view class="dev_content">

					<image src="@/static/product/fajing.png" mode="" v-if="item.equipType=='002'"></image>
					<image src="@/static/homePage/dmsIcon.png" mode="" v-else-if="item.equipType=='004'"></image>
					<template v-else-if="item.equipType=='001'">
					<image  src="@/static/product/yuanxing.png" mode="" v-if=" item.code.indexOf('82') == 0"></image>
					<image src="@/static/product/fangxing.png" mode="" v-else-if=" item.code.indexOf('81') == 0"></image>
					<image   src="@/static/product/home.png" mode="" v-else></image>
					</template>
					<image src="@/static/product/gongshang.png" mode="" v-else-if="item.equipType=='003'"></image>
					<view class="dev_data">{{item.code}}</view>
				</view>
			</view>
		</view>


		<u-action-sheet :actions="devActionList" :show="devShow" round="30" @select="devActionClick" safeAreaInsetBottom>
		</u-action-sheet>
		<u-modal :content="delContent" title="提示" confirmText="确定" showCancelButton :show="delShowModal"
			@confirm="delConfirm" @cancel="delShowModal=false" style="text-align: center;"></u-modal>
	</view>
</template>

<script>
	import {
		sceneListAll,
		deviceInfoList,
		equipUnbind
	} from "@/network/api.js"
	export default {
		data() {
			return {
				tabsLocation: 0,
				conduitList: [],
				deviceList: [],
				sceneID: '1',
				devShow: false,
				delContent: '是否解绑设备?',
				delShowModal: false,
				devActionList: [
					{
						name: '编辑'
					},
					{
						name: '解绑'
					},
					{
						name: '取消'
					}
				],
				currentDev:{}
			};
		},
		onShow() {
			this.tabsLocation = 0
			this.getConfigsList();
			console.log('onshow')
		},
		methods: {
			// 切换tabs
			cutTabs(item) {
				console.log("12", item)
				this.tabsLocation = item.index;
				this.sceneID = item.id;
				console.log(this.sceneID,'ID');
				this.getDevicList(item.id)
			},
			getConfigsList() {
				this.tabsLocation = 0;
				let query = {
					userType: uni.getStorageSync('userType'),
					id: uni.getStorageSync('userId')
				}
				sceneListAll(query).then(res => {
					console.log(res,'场景列表');
						this.conduitList = res.data;
						this.getDevicList(1)
					})
					.catch((err) => {})
			},
			getDevicList(id) {
				let query = {
					userType: uni.getStorageSync('userType'),
					customerId: uni.getStorageSync('userId'),
					token: uni.getStorageSync('token'),
					sceneId: id
				}
				deviceInfoList(query).then(

				res => {
					console.log(res,'获取设备');
						this.deviceList = res.data.list;
					})
					.catch((err) => {})
			},
			goDevicePage(item) {
				uni.navigateTo({
					url: '/pages/deviceManage/devicePage?params=' + JSON.stringify(item)
				})
			},
			devLongClick(item) {
				console.log("www",item)
				this.devShow = true;
				this.currentDev = item;
			},
			moreWays(item) {
				console.log("www",item)
				this.devShow = true;
				this.currentDev = item;
			},
			devActionClick(index) {
				console.log(index)
				if (index.name == "编辑") {
					uni.navigateTo({
						url: '/pages/deviceManage/deviceEdit?params=' + JSON.stringify(this.currentDev)
					});
					this.devShow = false;
				} else if (index.name == "解绑") {
					this.delShowModal = true;
				} else if (index.name == "取消") {
					this.devShow = false
				}
			},
			delConfirm() {
				this.delShowModal = false
				let form = {
					token: uni.getStorageSync('token'),
					id: this.currentDev.id,
					customerId: uni.getStorageSync('userId'),
					code: this.currentDev.code
				}
				equipUnbind(form).then(res => {
						uni.showToast({
							title: '已解绑',
							icon: "none"
						});

						this.getConfigsList();
						this.devShow = false
					})
					.catch((err) => {})
			},
		},
		onReady() {},
	};
</script>
<style>
	page {
		background-color: #F6F6F6;
	}
</style>
<style lang="scss" scoped>
	.myDeviceInform {
		.topTabs {
			// padding: 20rpx 40rpx 30rpx;
			padding: 10rpx 0rpx 20rpx;
		}

		.devList {
			padding: 0rpx 20rpx;
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;

			.devView {
				width: 44%;
				background-color: #ffffff;
				border-radius: 32rpx;
				padding: 2.5%;
				margin: 10rpx 0;

				.dev_head {
					// color: #909193;
					// font-size: 24rpx;
					height: 50rpx;
					width: 100%;
					display: flex;
					justify-content: space-between;
					align-items: center;
					 margin-bottom: 10rpx;

					.numCode {
						width: 85%;
						// white-space: nowrap;
						// overflow: hidden;
						// text-overflow: ellipsis;
					}
					.moreButton {
						width: 15%;
						height: 100%;
						margin-right:0;
						image {

							width: 100%;
							height: 100%;
						}
					}
				}

				.dev_content {
					image {
						width: 140rpx;
						height: 140rpx;

						margin-left: 20rpx;
					}

					.dev_data {
						text-align: right;
					}
				}
			}
		}
	}
</style>