function groupCitiesByProvince(cities) {

  const provinces = {};

  cities.forEach(city => {
    const provinceId = city.provId || city.id;

    if (!provinces.hasOwnProperty(provinceId)) {
      provinces[provinceId] = {
		  name: city.provName,
		  value: city.adminCode,
		  id: city.provId,
		  children: []
      };
    }
    provinces[provinceId].children.push({
      id: city.cityId,
      name: city.cityName,
      value: city.adminCode
    });
  });
  return Object.values(provinces);
}


export {
	groupCitiesByProvince
}