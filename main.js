import App from './App'

// #ifndef VUE3
import Vue from 'vue'
Vue.config.productionTip = false
App.mpType = 'app'
import filters from './unit/filter' //导入过滤器文件
Object.keys(filters).forEach(key => { //通过Object.key方法取出过滤器中导出的每个方法并挂在vue.filter上
	Vue.filter(key, filters[key])
})

// import wx from 'weixin-js-sdk'
// Vue.prototype.$wx = wx

import unit from 'unit/test.js'
Vue.prototype.unit = unit
//includes 判断是否存在

import uView from "uview-ui";
Vue.use(uView);
// uni.$u.config.unit = 'rpx'
// 调用setConfig方法，方法内部会进行对象属性深度合并，可以放心嵌套配置
// 需要在Vue.use(uView)之后执行
uni.$u.setConfig({
	// 修改$u.config对象的属性
	config: {
		// 修改默认单位为rpx，相当于执行 uni.$u.config.unit = 'rpx'
		unit: 'px'
	},
})

import "static/css/quill.core.css";
import "static/css/quill.snow.css";
import "static/css/quill.bubble.css";

try {
	function isPromise(obj) {
		return (
			!!obj &&
			(typeof obj === "object" || typeof obj === "function") &&
			typeof obj.then === "function"
		);
	}

	// 统一 vue2 API Promise 化返回格式与 vue3 保持一致
	uni.addInterceptor({
		returnValue(res) {
			if (!isPromise(res)) {
				return res;
			}
			return new Promise((resolve, reject) => {
				res.then((res) => {
					if (res[0]) {
						reject(res[0]);
					} else {
						resolve(res[1]);
					}
				});
			});
		},
	});
} catch (error) {}


// if(!uni.getStorageSync('deviceId')){

// 	uni.setStorage({
// 		key: 'deviceId',
// 		data: uni.$u.guid(20)
// 	});
// }
const app = new Vue({
	...App
})
app.$mount()
// #endif

// #ifdef VUE3
import {
	createSSRApp
} from 'vue'
export function createApp() {
	const app = createSSRApp(App)
	return {
		app
	}
}
// #endif
