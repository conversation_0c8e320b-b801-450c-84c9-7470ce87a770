<template>
	<view class="nav">
		<view :style="'height:'+status+'rpx;'+containerStyle"></view>
		<view v-if="isHome" class="headNav" :style="'height:'+navHeight+'rpx;line-height:' + navHeight+ 'rpx;padding-left:20rpx;'">
			<text class="city">首页</text>
		</view>
		<view v-else class="navbar" :style="'height:'+navHeight+'rpx;'+containerStyle">
			<view class="nav-title" v-if="titleText">
				<view :style="'height:' + navHeight + 'rpx;' +textStyle">{{titleText}}</view>
			</view>
		</view>
		
		
		
	</view>
</template>

<script>
	import {
		noticeList,
		handle
	} from "@/network/api.js"
	export default {
		props: [ 'isHome'],
		
		data() {
			return {
				props:({
									background:{
										type: String,
										default: 'rgba(255,255,255,1)'
									},
									color:{
										type: String,
										default: 'rgba(0,0,0,1)'
									},
									fontSize:{
										type: String,
										default: 'rgba(0,0,0,1)'
									},						
									titleText:{
										type:String,
										default: ''
									},
									isHome: {
										type: Boolean,
										default: false
									}
								}),
								
								  
				name:"navbar",
			 
				//状态栏高度
				 status: 0,
				//内容高度
				 navHeight:0,
				//背景颜色
				 containerStyle :'',
				//字体样式
				 textStyle:'',
				//图标的样式
				 iconStyle:'',
				//页面栈的数量
				 pages: 0,
				 iconWidth:32,
				 iconHeight:38
				
				
			};
		},
		
		methods: {
			
			
			  // <!-- style="position: relative;left: 10px;" -->
		//计算机状态栏高度
		setNavSize(){
			console.log('123')
			
			const {statusBarHeight,system}=uni.getSystemInfoSync()
			console.log('statusBarHeight',statusBarHeight)
			console.log('system',system)
			this.status = statusBarHeight * 2
			console.log('this.status.value',this.status)
			const isIOS = system.indexOf('iOS') > -1
			if(!isIOS){
				this.navHeight = 96
			}
			else{
				this.navHeight = 88
			}
		},
		
		//样式设置
		setStyle(){
			this.textStyle = ['color:' + this.props.color,'font-size:'+this.props.fontSize+'rpx'].join(';')
			this.containerStyle = ['background:' + this.props.background].join(';')
			
			this.iconStyle = ['width:'+this.iconWidth+'rpx','height:'+this.iconHeight+'rpx'].join(';')
			console.log('jies')
		},
		sendHeight(){
			this.$emit('myHeight',this.navHeight)
		},
		
		},
	
	
		
		mounted() {
			this.pages=getCurrentPages().length
			this.setNavSize();
			this.setStyle();
			console.log('pages',this.pages);
			console.log(this.props.isHome);
			console.log(this.props.titleText,'阿达');
			this.sendHeight();
			
			
			
		}

				
		
	
	}
	
</script>

<style>
.nav{
	 position:fixed;
	 width: 100%;
	 top: 0;
	 left: 0;
	 z-index: 2;
}

.back-icon {
	display: flex;
	align-items: center;
	
	height: 100%;
	margin-left: 20rpx;
}
.back-icon text {
	font-size:30rpx;
	
}
.back-icon image {
	width: 100rpx;
	height: 40rpx;
	
}
.navbar {
	position: relative;
}
.nav-title {
	position: absolute;
	top: 20rpx;
	left: 50%;
	transform: translate(-50%);	
    font-size:40rpx;
}
.headNav {
	display: flex;
	justify-content: center;
	align-items: center;
}
.city {
	position: relative;
	font-size: 30rpx;
	font-weight: bold;
	padding-left: 10rpx;
}
</style>