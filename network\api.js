import { myRequest, myRequest_a } from "./http.js";

export function toApplet(data) {
  //调用后台登录
  return myRequest_a({
    url: "/micro-app/customer/login",
    method: "POST",
    data: data,
  });
}
export function sceneAdd(data) {
  //新增场景
  return myRequest_a({
    url: "/cst/scene/add",
    method: "POST",
    data: data,
  });
}
export function sceneUpdate(data) {
  //修改、删除场景
  return myRequest_a({
    url: "/cst/scene/update",
    method: "POST",
    data: data,
  });
}

export function addDevice(data) {
  //新增设备
  return myRequest_a({
    url: "/cst/cstEquipment/save",
    method: "POST",
    data: data,
  });
}

export function updateDevice(data) {
  //编辑设备
  return myRequest_a({
    url: "/cst/cstEquipment/updateDevice",
    method: "POST",
    data: data,
  });
}
export function equipUnbind(data) {
  //解绑设备
  return myRequest_a({
    url: "/micro-app/cstEquipment/equipUnbind",
    method: "POST",
    data: data,
  });
}

export function sceneListAll(data) {
  //场景管理列表（所有）
  return myRequest_a({
    url: "/cst/scene/list/all",
    method: "POST",
    data: data,
  });
}

export function sceneList(data) {
  //场景管理列表（带分页）
  return myRequest_a({
    url: "/cst/scene/list",
    method: "GET",
    data: data,
  });
}

export function deviceInfoList(data) {
  //获取用户设备列表
  return myRequest_a({
    url: "/cst/cstEquipment/page",
    method: "GET",
    data: data,
  });
}
export function devDetail(data) {
  //设备详情
  return myRequest_a({
    url: "/micro-app/cstEquipment/findByCode",
    method: "GET",
    data: data,
  });
}
export function cstAlarmList(data) {
  //查询报警列表
  return myRequest_a({
    url: "/cst/cstAlarm/page",
    method: "GET",
    data: data,
  });
}

export function cstAlarmDetail(data) {
  //查询报警信息详情
  return myRequest_a({
    url: "/cst/cstAlarm/list/detail",
    method: "GET",
    data: data,
  });
}
export function devAllList(data) {
  //查询所有设备列表
  return myRequest_a({
    url: "/micro-app/cstEquipment/list",
    method: "POST",
    data: data,
  });
}
export function cstFaultAdd(data) {
  //提交上报故障
  return myRequest_a({
    url: "/cst/fault/add",
    method: "POST",
    data: data,
  });
}
// export function cstFaultList(data) { //查询故障信息列表
// 	return myRequest({
// 		url: '/cstFault/list',
// 		method: 'GET',
// 		data: data
// 	})
// }
export function cstFault(data) {
  //故障详情
  return myRequest_a({
    url: "/cst/fault/detail/" + data,
    method: "GET",
  });
}
export function noticeList(data) {
  //通知公告列表
  return myRequest_a({
    url: "/cst/notice/list",
    method: "GET",
    data: data,
  });
}
// export function handle(data) { //获取报警信息
// 	return myRequest({
// 		url: '/cstAlarm/handle',
// 		method: 'POST',
// 		data: data
// 	})
// }
export function equipStatistics(data) {
  //获取设备总数、报警数
  return myRequest_a({
    url: "/micro-app/cstEquipment/equipStatistics",
    method: "GET",
    data: data,
  });
}
export function appletUserList(data) {
  //查询用户列表
  return myRequest_a({
    url: "/cst/customer/page",
    method: "GET",
    data: data,
  });
}
export function AppletUser(data) {
  //用户详情
  return myRequest_a({
    url: "/cst/customer/" + data,
    method: "GET",
  });
}
export function AppletUserEdit(data) {
  //用户编辑、删除
  return myRequest_a({
    url: "/cst/customer/edit",
    method: "POST",
    data: data,
  });
}
export function DeviceContorl(data) {
  //反控
  return myRequest_a({
    url: "/micro-app/dev/sendCmd",
    method: "POST",
    data: data,
  });
}
export function GetCmdRes(data) {
  //實時接收
  return myRequest_a({
    url: "/micro-app/dev/getCmdRes",
    method: "GET",
    data: data,
  });
}

export function QueryBind(data) {
  //请求添加
  return myRequest_a({
    url: "/micro-app/cstEquipment/queryBind",
    method: "GET",
    data: data,
  });
}

export function DefaultList(data) {
  //故障列表
  return myRequest_a({
    url: "/cst/fault/list",
    method: "GET",
    data: data,
  });
}

export function YWList(data) {
  //运维人员列表
  return myRequest_a({
    url: "/cst/customer/ywList",
    method: "GET",
    data: data,
  });
}

export function Update(data) {
  //运维人员列表
  return myRequest_a({
    url: "/cst/fault/update",
    method: "POST",
    data: data,
  });
}

// OTA升级相关接口
export function getFirmwareVersion(deviceCode) {
  //获取设备固件版本信息 https://www.ahhsiot.com:10443/prod-api/cst/firmware/enableVersion
  return myRequest_a({
  	url: '/micro-app/ota/enableVersion',
  	method: 'GET',
  	// data: { deviceCode: deviceCode }
  })
  // 返回模拟数据的Promise
  // return Promise.resolve({
  //   code: 200,
  //   data: {
  //     version: "SF02-103A SV1.0 250125",
  //     url: "https://www.ahhsiot.com:10443/statics/2025/05/22/CH4_BC260_TCP_v1.1(3)_20250522174657A017.bin",
  //   },
  // });
}

export function installationList(data) {
  //获取设备安装工单列表
  return myRequest_a({
    url: "/micro-app/installation/list",
    method: "GET",
    data: data,
  });
}

export function installationTaskList(data) {
  //获取设备安装工单详情列表
  return myRequest_a({
    url: "/micro-app/installation/taskList",
    method: "GET",
    data: data,
  });
}

export function installationCommit(data) {
  //设备安装提交
  return myRequest_a({
    url: "/micro-app/installation/commit",
    method: "POST",
    data: data,
  });
}

export function installationTaskDetail(data) {
  //获取设备安装任务详情
  return myRequest_a({
    url: "/micro-app/installation/taskDetail",
    method: "GET",
    data: data,
  });
}

export function getPicList(data) {
  //获取轮播图片列表
  return myRequest_a({
    url: "/file/getPicList",
    method: "GET",
    data: data,
  });
}
