<template>
	<view class="loginPage">
		<!-- <view class="tabsView">
			<p v-for="(tabs,index) in tabsList" :class="currentTab===index?'activeTabs':'tabsClass'"
				@click="cutTabs(index)">{{tabs}}</p>
		</view> -->
		<view class="loginForm">
			<u--form labelPosition="top" :model="modelLogin" :rules="loginRules" ref="loginForm" labelWidth="200"
				:labelStyle="labelStyle">
				<u-form-item label="手机号" prop="userInfo.hospitalNumber" :required="true" v-if="currentTab==0">
					<u--input v-model="modelLogin.userInfo.hospitalNumber" maxlength="20" border="surround"
						shape="circle" placeholder="请输入手机号"></u--input>
				</u-form-item>
				<u-form-item label="密码" prop="userInfo.password" :required="true">
					<u--input v-model="modelLogin.userInfo.password" password maxlength="6" border="surround"
						shape="circle" placeholder="请输入密码"></u--input>
				</u-form-item>
			</u--form>
			<view class="btnXFView">
				<u-button type="primary" text="登录" :disabled="disabled" :customStyle="primaryBtnCss"
					@click="submit"></u-button>
			</view>
		</view>
		<u-modal :content="content" title="提示" :show="showModal" @confirm="showModal = false"></u-modal>
	</view>
</template>

<script>
	import {
		LoginByHospitalNumber,
		LoginByIDAndPwd
	} from "@/network/api.js";
	import md5 from '@/common/js-md5/md5.min.js';
	export default {
		data() {
			return {
				content: '您输入的账号/密码错误，请重新输入。',
				showModal: false,
				// tabsList: ['住院号登录', 'ID登录'],
				currentTab: 0,
				primaryBtnCss: {
					width: '574rpx',
					height: '88rpx',
					background: '#0165FC',
					boxShadow: '0rpx 4rpx 24rpx 0rpx rgba(54,150,255,0.4)',
					borderRadius: '200rpx',
					fontSize: '28rpx',
					fontFamily: 'Microsoft YaHei UI-Regular, Microsoft YaHei UI',
					color: '#FFFFFF',
					lineHeight: '28rpx'
				},
				disabled: false,
				labelStyle: {
					// fontSize: '28rpx',
					// fontFamily: 'Microsoft YaHei UI-Regular, Microsoft YaHei UI',
					// lineHeight: '28rpx'
				},
				modelLogin: {
					userInfo: {
						hospitalNumber: '',
						password: '',
						userId: ''
					},
				},
				loginRules: {
					'userInfo.hospitalNumber': [{
							type: 'string',
							required: true,
							message: '请输入手机号',
							trigger: ['blur', 'change']
						},
						{
							// 自定义验证函数，见上说明
							validator: (rule, value, callback) => {
								// uni.$u.test.mobile()就是返回true或者false的
								return uni.$u.test.mobile(value);
							},
							message: '手机号码不正确',
							// 触发器可以同时用blur和change
							trigger: ['change', 'blur'],
						}
					],
					'userInfo.password': {
						type: 'string',
						required: true,
						message: '请输入密码',
						trigger: ['blur', 'change']
					},
				}
			};
		},
		methods: {
			cutTabs(index) {
				this.currentTab = index;
			},
			submit() {
				this.$refs.loginForm.validate().then(res => {
					// uni.$u.toast('校验通过')
					uni.showLoading({
						title: '请求中..'
					})
					let form1 = {
						hospitalNumber: this.modelLogin.userInfo.hospitalNumber,
						password: md5(this.modelLogin.userInfo.password)
					}
					// LoginByHospitalNumber(form1).then(res => {
					// 		if (res.statusCode === 200) {
					// 			uni.showToast({
					// 				title: "登录成功",
					// 				icon: "success"
					// 			});
								this.loginSuccess(res.data);
								uni.hideLoading();
					// 		} else if (res.statusCode === 210) {
					// 			this.showModal = true;
					// 			uni.hideLoading();
					// 		} else {
					// 			uni.hideLoading();
					// 			uni.showToast({
					// 				title: res.data,
					// 				icon: "none"
					// 			});
					// 		}
					// 	})
					// 	.catch((err) => {
					// 		uni.hideLoading();
					// 	})
				}).catch(errors => {
					// uni.$u.toast('校验失败')
				})
			},
			loginSuccess(resData) {
				console.log("89", resData)
				// uni.setStorage({
				// 	key: 'uid',
				// 	data: resData.ex
				// });
				// uni.setStorage({
				// 	key: 'userName',
				// 	data: resData.userName
				// });
				// uni.setStorage({
				// 	key: 'hospitalNumber',
				// 	data: resData.ex2
				// });
				uni.setStorage({
					key: 'token',
					// data: resData.token
					data: "12121212"
				});
				uni.switchTab({
					url: "/pages/homePage/homePage"
				})
			}
		},
		onReady() {
			//如果需要兼容微信小程序，并且校验规则中含有方法等，只能通过setRules方法设置规则。
			this.$refs.loginForm.setRules(this.loginRules)
		},
	};
</script>

<style lang="scss" scoped>
	.loginPage {
		.tabsView {
			width: 100%;
			border-bottom: 2rpx solid #E7E7E7;
			margin-top: 20rpx;
			display: flex;
			justify-content: center;

			.activeTabs {
				width: 150rpx;
				font-size: 28rpx;
				font-family: PingFang SC-Medium, PingFang SC;
				font-weight: 500;
				color: #0052D9;
				line-height: 64rpx;
				border-bottom: 4rpx solid #0052D9;
				margin: 0 60rpx;
				text-align: center;
			}

			.tabsClass {
				width: 150rpx;
				font-size: 28rpx;
				font-family: PingFang SC-Regular, PingFang SC;
				font-weight: 400;
				color: rgba(0, 0, 0, 0.6);
				line-height: 64rpx;
				border-bottom: 4rpx solid transparent;
				margin: 0 60rpx;
				text-align: center;
			}
		}

		.loginForm {
			padding: 40rpx 68rpx;

			.u-input--circle {
				width: 574rpx;
				height: 70rpx;
				background: #F8F8F8;
				border-radius: 44rpx;
				margin-top: 16rpx;
				padding: 0 32rpx;
			}
		}
	}
</style>