import {
	baseURL,
	baseURL_a
} from './base.js'; //导入接口的前缀地址
import { debugLog } from '@/utils/debug.js';

export const myRequest = (options) => {
	return new Promise((resolve, reject) => {
		uni.request({
			url: baseURL + options.url, //接口地址：前缀+方法中传入的地址
			method: options.method, //请求方法：传入的方法 或者默认是“GET”
			data: options.data || {}, //传递参数：传入的参数或者默认传递空集合
			timeout: 60000,
			header: {
				"Content-Type": options.method == 'GET' ?
					'application/x-www-form-urlencoded;charset=utf-8' : 'application/json',
				"Authorization": `${uni.getStorageSync('token')}` || '', //自定义请求头信息
			},
			success: (res) => {
				console.log("res", res.data)
				// if (res.data.code != 200) {
				// 	uni.showToast({
				// 		title: res.data.msg || '请求失败',
				// 		icon: "none"
				// 	});
				// } else 
				if (res.data.code == 401) {
					uni.showToast({
						title: "登录过期，请重新登录",
						icon: 'none'
					})
					uni.clearStorage();
					setTimeout(() => {
						uni.reLaunch({
							url: `/pages/loginRegister/welcomePage`,
						});
					}, 1000)
				}
				else {
					//返回的数据（不固定，看后端接口，这里是做了一个判断，如果不为true，用uni.showToast方法提示获取数据失败)
					// if (res.data.success != true) {
					// 	return uni.showToast({
					// 		title: '获取数据失败',
					// 		icon: 'none'
					// 	})
					// }
					// 如果不满足上述判断就输出数据
					resolve(res.data)
				}
			},
			// 这里的接口请求，如果出现问题就输出接口请求失败
			fail: (err) => {
				console.log(err)
				uni.hideLoading();
				reject(err)
			}
		})
	})
}

export const myRequest_a = (options) => {
	return new Promise((resolve, reject) => {
		const fullUrl = baseURL_a + options.url;
		const headers = {
			"Content-Type": options.method == 'GET' ?
				'application/x-www-form-urlencoded;charset=utf-8' : 'application/json',
			"Authorization": `${uni.getStorageSync('token')}` || '', //自定义请求头信息
		};

		// 调试日志
		debugLog.logRequest(fullUrl, options.method, options.data, headers);

		uni.request({
			url: fullUrl, //接口地址：前缀+方法中传入的地址
			method: options.method, //请求方法：传入的方法 或者默认是“GET”
			data: options.data || {}, //传递参数：传入的参数或者默认传递空集合
			timeout: 60000,
			header: headers,
			success: (res) => {
				// 调试日志
				debugLog.logResponse(fullUrl, res);

				// 检查HTTP状态码
				if (res.statusCode !== 200) {
					debugLog.logError(fullUrl, `HTTP错误: ${res.statusCode}`);
					uni.showToast({
						title: `网络错误: ${res.statusCode}`,
						icon: "none"
					});
					reject(res)
					return
				}

				// 检查业务状态码
				if (res.data && res.data.code == 401) {
					uni.showToast({
						title: "登录过期，请重新登录",
						icon: 'none'
					})
					uni.clearStorage();
					setTimeout(() => {
						uni.reLaunch({
							url: `/pages/loginRegister/welcomePage`,
						});
					}, 1000)
				}
				else if(res.data && (res.data.code == 200 || res.data.code == 500|| res.data.code == 1001 || res.data.code == 1010 || res.data.code == 1011 || res.data.code == 1012 || res.data.code == 1013 || res.data.code == 1014)){
					resolve(res.data)
				}
				else {
					debugLog.logError(fullUrl, `业务错误: ${res.data?.msg || '未知错误'}`);
					uni.showToast({
						title: res.data?.msg || '请求失败',
						icon: "none"
					});
					reject(res.data)
				}
			},
			// 这里的接口请求，如果出现问题就输出接口请求失败
			fail: (err) => {
				debugLog.logError(fullUrl, err);
				uni.hideLoading();
				uni.showToast({
					title: '网络连接失败，请检查网络',
					icon: "none"
				});
				reject(err)
			}
		})
	})
}